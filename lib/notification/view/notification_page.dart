import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/notification/cubit/refresh_cubit.dart';
import 'package:leiuniverse/notification/widget/notification_view.dart';
import 'package:leiuniverse/user/model/notification_model.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends State<NotificationPage> with PaginationMixin {
  final notificationList = ValueNotifier<List<NotificationModel>>(<NotificationModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    notificationList.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IUserRepository>().notificationList();

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      notificationList.value = [...r.data ?? []];
      hasReachedMax = (r.data?.length ?? 0) < 10;
      page = 2;
      context.read<RefreshCubit>().reset();
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IUserRepository>().notificationList(page: page);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = (r.data?.length ?? 0) < 10;
      notificationList.value = [...notificationList.value, ...r.data ?? []];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Notifications',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            if (loading) {
              return const Center(
                child: CustomProgressIndecator(
                  size: 40,
                  color: AppColors.primary,
                ),
              );
            }
            return ValueListenableBuilder<List<NotificationModel>>(
                valueListenable: notificationList,
                builder: (context, notifications, _) {
                  if (!loading && notifications.isEmpty) {
                    return Utility.noDataWidget(context: context, text: 'No Notifications Found');
                  }
                  return ListView.builder(
                    controller: scrollPaginationController,
                    itemCount: notifications.length,
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                    itemBuilder: (context, index) {
                      return ValueListenableBuilder<bool>(
                          valueListenable: isLoadingMore,
                          builder: (context, loadingMore, _) {
                            return Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                NotificationView(
                                  notification: notifications[index],
                                ),
                                if (!hasReachedMax && loadingMore && index == notifications.length - 1)
                                  const Padding(
                                    padding: EdgeInsets.only(top: 20),
                                    child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                                  ),
                              ],
                            );
                          });
                    },
                  );
                });
          }),
    );
  }
}
