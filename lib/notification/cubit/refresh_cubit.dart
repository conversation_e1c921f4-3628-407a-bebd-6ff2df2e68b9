import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';

part 'refresh_state.dart';

class RefreshCubit extends Cubit<RefreshState> {
  final IUserRepository _userRepository;

  RefreshCubit(this._userRepository) : super(RefreshInitial());

  Future<void> loadCount() async {
    final failOrSuccess = await _userRepository.notificationCount();

    failOrSuccess.fold(
      (failure) {
        emit(const RefreshUpdated(0));
      },
      (result) {
        emit(RefreshUpdated(result.data ?? 0));
      },
    );
  }

  void reset() => emit(const RefreshUpdated(0));
}
