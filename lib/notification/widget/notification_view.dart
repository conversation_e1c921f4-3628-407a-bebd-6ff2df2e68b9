import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/user/model/notification_model.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class NotificationView extends StatelessWidget {
  const NotificationView({super.key, this.notification});
  final NotificationModel? notification;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Text(
                notification?.title ?? '',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            if (notification?.isRead == 0) ...[
              const Gap(10),
              const AppSvgImage(
                AppAssets.doteIcon,
                color: AppColors.primary,
              ),
            ],
          ],
        ),
        const Gap(2),
        Text(
          notification?.message ?? '',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const Gap(2),
        Text(
          notification?.timeString ?? '',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
        ),
        const Gap(16),
        const Divider(
          height: 1,
          color: AppColors.border,
        ),
        const Gap(16),
      ],
    );
  }
}
