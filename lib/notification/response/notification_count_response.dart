import 'package:equatable/equatable.dart';

class NotificationCountResponse extends Equatable {
  const NotificationCountResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final int? data;

  NotificationCountResponse copyWith({
    int? status,
    String? message,
    int? data,
  }) {
    return NotificationCountResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory NotificationCountResponse.fromJson(Map<String, dynamic> json) {
    return NotificationCountResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data,
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
