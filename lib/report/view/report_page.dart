import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/report/widget/report_widget.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class ReportPage extends StatefulWidget {
  const ReportPage({super.key, required this.userId});
  final int userId;

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends State<ReportPage> {
  final selectedReport = ValueNotifier<ReasonModel?>(null);
  final reasonController = TextEditingController();
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Report',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: ValueListenableBuilder(
        valueListenable: selectedReport,
        builder: (context, select, _) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 20,
                horizontal: 20,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Why are you reporting?',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                  const Gap(20),
                  ListView.separated(
                    separatorBuilder: (context, index) {
                      return const Gap(20);
                    },
                    itemCount: AppConstants.reportReasons.length,
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      return ReportWidget(
                        title: AppConstants.reportReasons[index].reason ?? '',
                        onTap: () {
                          selectedReport.value = AppConstants.reportReasons[index];
                        },
                        isSelected: select?.id == AppConstants.reportReasons[index].id,
                      );
                    },
                  ),
                  if (select?.id == 8) ...[
                    const Gap(14),
                    Padding(
                      padding: const EdgeInsets.only(left: 30),
                      child: TextFormField(
                        maxLines: 4,
                        controller: reasonController,
                        onTapUpOutside: (event) {
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                        decoration: const InputDecoration(
                          hintText: 'Write detail here...',
                          fillColor: AppColors.white,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 14,
                            horizontal: 16,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(10),
                            ),
                            borderSide: BorderSide(
                              color: AppColors.border,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                              Radius.circular(10),
                            ),
                            borderSide: BorderSide(
                              color: AppColors.border,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: ValueListenableBuilder<ReasonModel?>(
          valueListenable: selectedReport,
          builder: (context, select, _) {
            return ValueListenableBuilder<bool>(
                valueListenable: isButtonLoading,
                builder: (context, loading, _) {
                  return BottomButtonWidget(
                    onTap: () {
                      if (select != null) {
                        if (select.id == 8 && reasonController.text.isEmpty) {
                          Utility.toast(message: 'Please enter reason');
                        } else {
                          reportUser();
                        }
                      } else {
                        Utility.toast(message: 'Please select reason');
                      }
                    },
                    isLoading: loading,
                    text: 'Report',
                  );
                });
          }),
    );
  }

  Future<void> reportUser() async {
    isButtonLoading.value = true;

    final failOrSuccess = await getIt<IUserRepository>().reportUser(
        userId: widget.userId,
        reason: selectedReport.value?.id == 8 ? reasonController.text : selectedReport.value?.reason);

    failOrSuccess.fold((l) {
      isButtonLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isButtonLoading.value = false;
      Navigator.pop(context);
      Utility.toast(message: r.message);
    });
  }
}
