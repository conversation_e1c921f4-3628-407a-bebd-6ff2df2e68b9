import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ReportWidget extends StatelessWidget {
  const ReportWidget({super.key, this.onTap, this.isSelected = false, required this.title});
  final void Function()? onTap;
  final bool isSelected;
  final String title;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppSvgImage(
            isSelected ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon,
          ),
          const Gap(10),
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall,
          ),
        ],
      ),
    );
  }
}
