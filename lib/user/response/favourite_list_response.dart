import 'package:equatable/equatable.dart';
import 'package:leiuniverse/user/model/favourite_model.dart';

class FavouriteListResponse extends Equatable {
  const FavouriteListResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final List<FavouriteModel>? data;

  FavouriteListResponse copyWith({
    int? status,
    String? message,
    List<FavouriteModel>? data,
  }) {
    return FavouriteListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory FavouriteListResponse.fromJson(Map<String, dynamic> json) {
    return FavouriteListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<FavouriteModel>.from(json["data"]!.map((x) => FavouriteModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
