import 'package:equatable/equatable.dart';
import 'package:leiuniverse/user/model/search_model.dart';

class SearchListResponse extends Equatable {
  const SearchListResponse({
    this.status,
    this.message,
    required this.data,
    this.currentPage,
    this.perPage,
    this.total,
  });

  final int? status;
  final String? message;
  final List<SearchModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;

  SearchListResponse copyWith({
    int? status,
    String? message,
    List<SearchModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
  }) {
    return SearchListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
    );
  }

  factory SearchListResponse.fromJson(Map<String, dynamic> json) {
    return SearchListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<SearchModel>.from(json["data"]!.map((x) => SearchModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
      ];
}
