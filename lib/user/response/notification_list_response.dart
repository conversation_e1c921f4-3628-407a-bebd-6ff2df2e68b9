import 'package:equatable/equatable.dart';
import 'package:leiuniverse/user/model/notification_model.dart';

class NotificationListResponse extends Equatable {
  const NotificationListResponse({
    this.status,
    this.message,
    this.data,
    this.currentPage,
    this.perPage,
    this.total,
  });

  final int? status;
  final String? message;
  final List<NotificationModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;

  NotificationListResponse copyWith({
    int? status,
    String? message,
    List<NotificationModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
  }) {
    return NotificationListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
    );
  }

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) {
    return NotificationListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null
          ? []
          : List<NotificationModel>.from(json["data"]!.map((x) => NotificationModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
      ];
}
