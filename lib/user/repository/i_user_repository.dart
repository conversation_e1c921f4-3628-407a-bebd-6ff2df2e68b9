import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/file_extention.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/login/response/user_response.dart';
import 'package:leiuniverse/notification/response/notification_count_response.dart';
import 'package:leiuniverse/user/response/favourite_list_response.dart';
import 'package:leiuniverse/user/response/notification_list_response.dart';
import 'package:leiuniverse/user/response/search_list_response.dart';

part 'user_repository.dart';

abstract class IUserRepository {
  IUserRepository(this.client);
  final Client client;

  ApiResult<UserResponse> update({
    bool? canAnyOneSendMessage,
    bool? canAnyOneCall,
    String? cityId,
    String? pincode,
    String? instagramUrl,
    String? facebookUrl,
    String? name,
    String? mobileNumber,
    String? companyName,
    File? profileImage,
  });

  ApiResult<CommonResponse> likeUser({required int id});

  ApiResult<CommonResponse> favouriteUser({required int id});

  ApiResult<CommonResponse> reportUser({required int userId, String? reason});

  ApiResult<FavouriteListResponse> favouriteList({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<SearchListResponse> searchList({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<NotificationListResponse> notificationList({
    int page = 1,
    int perPage = 10,
  });

  ApiResult<NotificationCountResponse> notificationCount();

  ApiResult<CommonResponse> blockUser({
    required int userId,
  });
}
