part of 'i_user_repository.dart';

@Injectable(as: IUserRepository)
class UserRepository extends IUserRepository {
  UserRepository(super.client);

  @override
  ApiResult<UserResponse> update({
    bool? canAnyOneSendMessage,
    bool? canAnyOneCall,
    String? cityId,
    String? instagramUrl,
    String? facebookUrl,
    String? pincode,
    String? name,
    String? mobileNumber,
    String? companyName,
    File? profileImage,
  }) async {
    final response = await client.multipart(
      url: AppStrings.updateProfile,
      files: [
        if (profileImage != null) MapEntry('profile_image', await profileImage.compressFile()),
      ],
      requests: {
        if (canAnyOneSendMessage != null) 'can_send_message': canAnyOneSendMessage ? '1' : '0',
        if (canAnyOneCall != null) 'can_call': canAnyOneCall ? '1' : '0',
        if (cityId != null) 'city_id': cityId.trim(),
        if (pincode != null) 'pincode': pincode.trim(),
        if (instagramUrl != null) 'instagram_url': instagramUrl.trim(),
        if (facebookUrl != null) 'facebook_url': facebookUrl.trim(),
        if (name != null) 'name': name.trim(),
        if (mobileNumber != null) 'mobile_number': mobileNumber.trim(),
        if (companyName != null) 'company_name': companyName.trim(),
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> likeUser({required int id}) async {
    final response = await client.post(
      url: AppStrings.likeUser(id),
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> favouriteUser({required int id}) async {
    final response = await client.post(
      url: AppStrings.toogleFavorite,
      requests: {
        'favourite_user': id.toString(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> reportUser({required int userId, String? reason}) async {
    final response = await client.post(
      url: AppStrings.reportUser,
      requests: {
        'report_user': userId.toString(),
        if (reason != null) 'reason': reason.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<FavouriteListResponse> favouriteList({int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.favoriteList,
      params: {
        'type': 'ALL',
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(FavouriteListResponse.fromJson);
  }

  @override
  ApiResult<SearchListResponse> searchList({int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.usersSearch,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(SearchListResponse.fromJson);
  }

  @override
  ApiResult<NotificationListResponse> notificationList({int page = 1, int perPage = 10}) async {
    final response = await client.get(
      url: AppStrings.notification,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
      },
    );
    return response.parseResponse(NotificationListResponse.fromJson);
  }

  @override
  ApiResult<NotificationCountResponse> notificationCount() async {
    final response = await client.get(
      url: AppStrings.notificationCount,
    );
    return response.parseResponse(NotificationCountResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> blockUser({
    required int userId,
  }) async {
    final response = await client.post(
      url: AppStrings.blockUser(userId.toString()),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }
}
