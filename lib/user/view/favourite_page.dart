import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/widget/profile_view_widget.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/view/market_place_details_page.dart';
import 'package:leiuniverse/market_place/widget/ad_post_widget.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/user/model/favourite_model.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class FavouritePage extends StatefulWidget {
  const FavouritePage({super.key});

  @override
  State<FavouritePage> createState() => _FavouritePageState();
}

class _FavouritePageState extends State<FavouritePage> with PaginationMixin {
  final searchController = TextEditingController();
  final favouriteList = ValueNotifier<List<FavouriteModel>>(<FavouriteModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    favouriteList.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IUserRepository>().favouriteList(search: searchController.text.trim());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      favouriteList.value = [...r.data ?? []];
      hasReachedMax = (r.data?.length ?? 0) < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IUserRepository>().favouriteList(page: page);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = (r.data?.length ?? 0) < 10;
      favouriteList.value = [...favouriteList.value, ...r.data ?? []];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'My Favourites',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
        child: Column(
          children: [
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              fillColor: AppColors.white,
              prefixIcon: const Padding(
                padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                child: AppSvgImage(
                  AppAssets.searchIcon,
                  color: AppColors.subText,
                ),
              ),
              onChanged: (value) {
                EasyDebounce.debounce(
                  'search_event',
                  const Duration(milliseconds: 500),
                  () {
                    load();
                  },
                );
              },
            ),
            Flexible(
              child: RefreshIndicator(
                onRefresh: () {
                  return load();
                },
                child: ValueListenableBuilder<bool>(
                    valueListenable: isLoading,
                    builder: (context, loading, _) {
                      if (loading) {
                        return const Center(
                          child: CustomProgressIndecator(
                            size: 40,
                            color: AppColors.primary,
                          ),
                        );
                      }
                      return ValueListenableBuilder<List<FavouriteModel>>(
                          valueListenable: favouriteList,
                          builder: (context, favourites, _) {
                            if (!loading && favourites.isEmpty) {
                              return Utility.noDataWidget(context: context, text: 'No Favourite Found');
                            }
                            return ValueListenableBuilder<bool>(
                                valueListenable: isLoadingMore,
                                builder: (context, loadingMore, _) {
                                  return ListView.separated(
                                    controller: scrollPaginationController,
                                    separatorBuilder: (context, index) => const Gap(20),
                                    itemCount: favourites.length,
                                    padding: const EdgeInsets.symmetric(vertical: 20),
                                    physics: const AlwaysScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      final favourite = favourites[index];
                                      return Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          if (favourite.favouriteUser != null)
                                            favoriteUserView(user: favourite.favouriteUser),
                                          if (favourite.favouriteAd != null) favoriteAdView(favourite.favouriteAd!),
                                          if (!hasReachedMax && loadingMore && index == favourites.length - 1)
                                            const Padding(
                                              padding: EdgeInsets.only(top: 20),
                                              child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                                            ),
                                        ],
                                      );
                                    },
                                  );
                                });
                          });
                    }),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget favoriteAdView(Advertisemnet ad) {
    return AdPostWidget(
      advertisement: ad,
      onTap: () async {
        final newAd = await Navigator.push<MarkDetailUpdateData?>(
          context,
          MaterialPageRoute(
            builder: (context) => MarketPlaceDetailsPage(advertisementId: ad.id ?? 0),
          ),
        );
        if (newAd != null && newAd.advertisement.isFavourite == 0) {
          favouriteList.value = [...favouriteList.value]
            ..removeWhere((element) => element.favouriteAdId == newAd.advertisement.id);
        }
      },
    );
  }

  Widget favoriteUserView({UserModel? user}) {
    return UserProfileWidget(
      user: user,
      onTap: () async {
        final newUser = await Navigator.push<UserModel>(
          context,
          MaterialPageRoute(
            builder: (_) => ProfileDetailPage(userId: user?.id),
          ),
        );
        if (newUser != null) {
          if (newUser.isFavourite == 0) {
            favouriteList.value = [...favouriteList.value]
              ..removeWhere((element) => element.favouriteUserId == newUser.id);
          }
          favouriteList.value = [
            ...favouriteList.value.map((e) => e.favouriteUserId == newUser.id
                ? e.copyWith(
                    favouriteUser: e.favouriteUser?.copyWith(
                    isLiked: newUser.isLiked,
                    likesCount: newUser.likesCount,
                  ))
                : e)
          ];
        }
      },
      likeOnTap: () {
        likeUser(id: user?.id ?? 0);
      },
      messageOnTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatDetailWrapperPage(
              userId: user?.id ?? 0,
            ),
          ),
        );
      },
    );
  }

  Future<void> likeUser({required int id}) async {
    final previousFavourite = favouriteList.value.firstWhere((e) => e.favouriteUserId == id);

    favouriteList.value = favouriteList.value
        .map((e) => e.favouriteUserId == id
            ? e.copyWith(
                favouriteUser: e.favouriteUser?.copyWith(
                    isLiked: e.favouriteUser?.isLiked == 0 ? 1 : 0,
                    likesCount: e.favouriteUser?.isLiked == 0
                        ? (e.favouriteUser?.likesCount ?? 0) + 1
                        : (e.favouriteUser?.likesCount ?? 0) - 1))
            : e)
        .toList();

    final failOrSuccess = await getIt<IUserRepository>().likeUser(id: id);

    failOrSuccess.fold((l) {
      favouriteList.value = favouriteList.value.map((e) => e.favouriteUserId == id ? previousFavourite : e).toList();

      Utility.toast(message: l.message);
    }, (r) {});
  }
}
