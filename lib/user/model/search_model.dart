import 'package:equatable/equatable.dart';

class SearchModel extends Equatable {
  const SearchModel({
    this.isEmailVerified,
    this.likesCount,
    this.id,
    this.name,
    this.role,
    this.companyName,
  });

  final bool? isEmailVerified;
  final int? likesCount;
  final int? id;
  final String? name;
  final String? role;
  final String? companyName;

  SearchModel copyWith({
    bool? isEmailVerified,
    int? likesCount,
    int? id,
    String? name,
    String? role,
    String? companyName,
  }) {
    return SearchModel(
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      likesCount: likesCount ?? this.likesCount,
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      companyName: companyName ?? this.companyName,
    );
  }

  factory SearchModel.fromJson(Map<String, dynamic> json) {
    return SearchModel(
      isEmailVerified: json["is_email_verified"],
      likesCount: json["likes_count"],
      id: json["id"],
      name: json["name"],
      role: json["role"],
      companyName: json["company_name"],
    );
  }

  Map<String, dynamic> toJson() => {
        "is_email_verified": isEmailVerified,
        "likes_count": likesCount,
        "id": id,
        "name": name,
        "role": role,
        "company_name": companyName,
      };

  @override
  String toString() {
    return "$isEmailVerified, $likesCount, $id, $name, $role, $companyName, ";
  }

  @override
  List<Object?> get props => [
        isEmailVerified,
        likesCount,
        id,
        name,
        role,
        companyName,
      ];
}
