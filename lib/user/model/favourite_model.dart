import 'package:equatable/equatable.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';

class FavouriteModel extends Equatable {
  const FavouriteModel({
    this.id,
    this.userId,
    this.favouriteUserId,
    this.favouriteAdId,
    this.createdAt,
    this.updatedAt,
    this.favouriteUser,
    this.favouriteAd,
  });

  final int? id;
  final int? userId;
  final int? favouriteUserId;
  final int? favouriteAdId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserModel? favouriteUser;
  final Advertisemnet? favouriteAd;

  FavouriteModel copyWith({
    int? id,
    int? userId,
    int? favouriteUserId,
    int? favouriteAdId,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserModel? favouriteUser,
    Advertisemnet? favouriteAd,
  }) {
    return FavouriteModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      favouriteUserId: favouriteUserId ?? this.favouriteUserId,
      favouriteAdId: favouriteAdId ?? this.favouriteAdId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      favouriteUser: favouriteUser ?? this.favouriteUser,
      favouriteAd: favouriteAd ?? this.favouriteAd,
    );
  }

  factory FavouriteModel.fromJson(Map<String, dynamic> json) {
    return FavouriteModel(
      id: json["id"],
      userId: json["user_id"],
      favouriteUserId: json["favourite_user_id"],
      favouriteAdId: json["favourite_ad_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      favouriteUser: json["favourite_user"] == null ? null : UserModel.fromJson(json["favourite_user"]),
      favouriteAd:
          json["favourite_advertisement"] == null ? null : Advertisemnet.fromJson(json["favourite_advertisement"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "favourite_user_id": favouriteUserId,
        "favourite_ad_id": favouriteAdId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "favourite_user": favouriteUser?.toJson(),
        "favourite_advertisement": favouriteAd,
      };

  @override
  String toString() {
    return "$id, $userId, $favouriteUserId, $favouriteAdId, $createdAt, $updatedAt, $favouriteUser, $favouriteAd, ";
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        favouriteUserId,
        favouriteAdId,
        createdAt,
        updatedAt,
        favouriteUser,
        favouriteAd,
      ];
}
