import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:leiuniverse/chat/model/group_chat_model.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class NotificationPayload extends Equatable {
  const NotificationPayload({
    this.chatType,
    this.metaDataPayload,
    this.senderId,
    this.advertisementId,
    this.group,
    this.clickAction,
  });

  final String? chatType;
  final String? senderId;
  final String? advertisementId;
  final GroupChatModel? group;
  final String? clickAction;
  final MetaDataPayload? metaDataPayload;

  factory NotificationPayload.fromJson(Map<String, dynamic> json) {
    return NotificationPayload(
      chatType: json["chat_type"],
      metaDataPayload:
          json["metaDataPayload"] == null ? null : MetaDataPayload.fromJson(jsonDecode(json["metaDataPayload"])),
      senderId: json["sender_id"]?.toString(),
      advertisementId: json["advertisement_id"]?.toString(),
      group: json["group"] == null ? null : GroupChatModel.fromJson(jsonDecode(json["group"])),
      clickAction: json["clickAction"],
    );
  }

  Map<String, dynamic> toJson() => {
        "chat_type": chatType,
        "metaDataPayload": metaDataPayload?.toJson(),
        "sender_id": senderId,
        "advertisement_id": advertisementId,
        "group": group?.toJson(),
        "clickAction": clickAction,
      };

  @override
  List<Object?> get props => [
        chatType,
        metaDataPayload,
        senderId,
        advertisementId,
        group,
        clickAction,
      ];
}

class MetaDataPayload extends Equatable {
  const MetaDataPayload({
    this.sender,
    this.chatType,
    this.senderId,
    this.advertisementId,
    this.group,
    this.clickAction,
  });

  final UserModel? sender;
  final String? chatType;
  final String? senderId;
  final String? advertisementId;
  final GroupChatModel? group;
  final String? clickAction;

  factory MetaDataPayload.fromJson(Map<String, dynamic> json) {
    return MetaDataPayload(
      sender: json["sender"] == null ? null : UserModel.fromJson(json["sender"]),
      chatType: json["chat_type"],
      senderId: json["sender_id"]?.toString(),
      advertisementId: json["advertisement_id"]?.toString(),
      group: json["group"] == null ? null : GroupChatModel.fromJson(jsonDecode(json["group"])),
      clickAction: json["clickAction"],
    );
  }

  Map<String, dynamic> toJson() => {
        "sender": sender?.toJson(),
        "chat_type": chatType,
        "sender_id": senderId,
        "advertisement_id": advertisementId,
        "group": group?.toJson(),
        "clickAction": clickAction,
      };

  @override
  List<Object?> get props => [
        sender,
      ];
}
