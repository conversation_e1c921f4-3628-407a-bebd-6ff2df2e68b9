import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class NotificationModel extends Equatable {
  const NotificationModel({
    this.id,
    this.userId,
    this.clickAction,
    this.title,
    this.message,
    this.anotherUserId,
    this.metaData,
    this.isRead,
    this.createdAt,
    this.updatedAt,
    this.anotherUser,
  });

  final int? id;
  final int? userId;
  final String? clickAction;
  final String? title;
  final String? message;
  final int? anotherUserId;
  final dynamic metaData;
  final int? isRead;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserModel? anotherUser;

  NotificationModel copyWith({
    int? id,
    int? userId,
    String? clickAction,
    String? title,
    String? message,
    int? anotherUserId,
    dynamic metaData,
    int? isRead,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserModel? anotherUser,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      clickAction: clickAction ?? this.clickAction,
      title: title ?? this.title,
      message: message ?? this.message,
      anotherUserId: anotherUserId ?? this.anotherUserId,
      metaData: metaData ?? this.metaData,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      anotherUser: anotherUser ?? this.anotherUser,
    );
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json["id"],
      userId: json["user_id"],
      clickAction: json["click_action"],
      title: json["title"],
      message: json["message"],
      anotherUserId: json["another_user_id"],
      metaData: json["meta_data"],
      isRead: json["is_read"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      anotherUser: json["anotherUser"] == null ? null : UserModel.fromJson(json["anotherUser"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "click_action": clickAction,
        "title": title,
        "message": message,
        "another_user_id": anotherUserId,
        "meta_data": metaData,
        "is_read": isRead,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "anotherUser": anotherUser?.toJson(),
      };

  @override
  String toString() {
    return "$id, $userId, $clickAction, $title, $message, $anotherUserId, $metaData, $isRead, $createdAt, $updatedAt, $anotherUser, ";
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        clickAction,
        title,
        message,
        anotherUserId,
        metaData,
        isRead,
        createdAt,
        updatedAt,
        anotherUser,
      ];

  String get timeString {
    if (createdAt == null) return "";

    final now = DateTime.now();
    final date = createdAt!.toLocal();

    final dateFormatter = DateFormat('h:mm a'); // e.g., 10:42 AM

    if (date.year == now.year && date.month == now.month && date.day == now.day) {
      return "Today ${dateFormatter.format(date).toLowerCase()}";
    } else if (date.year == now.year && date.month == now.month && date.day == now.day - 1) {
      return "Yesterday ${dateFormatter.format(date).toLowerCase()}";
    } else {
      return DateFormat('dd MMM, h:mm a').format(date).toLowerCase(); // e.g., 05 Aug, 10:42 am
    }
  }
}
