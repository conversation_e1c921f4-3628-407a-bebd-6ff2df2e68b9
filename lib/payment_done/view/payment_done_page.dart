import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/home/<USER>/home_page.dart';
import 'package:leiuniverse/payment_done/widget/business_service_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:lottie/lottie.dart';

class PaymentDonePage extends StatefulWidget {
  const PaymentDonePage({super.key});

  @override
  State<PaymentDonePage> createState() => _PaymentDonePageState();
}

class _PaymentDonePageState extends State<PaymentDonePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 34),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  Lottie.asset(
                    AppAssets.paymentDoneImage,
                  ),
                  const Gap(20),
                  Text(
                    'Payment successful',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          color: AppColors.primary,
                        ),
                  ),
                  const Gap(7),
                  Text(
                    'Paid : Rs.250',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  const Gap(7),
                  Text(
                    'Trxn id : 1212112112',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppColors.subText,
                        ),
                  ),
                  const Gap(7),
                  Text(
                    'Valid til 26 may 2026',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: AppColors.subText,
                        ),
                  ),
                  const Gap(16),
                  const Divider(),
                  const Gap(14),
                ],
              ),
              Text(
                'Business/Service added',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.subText,
                    ),
              ),
              const Gap(8),
              ListView.separated(
                separatorBuilder: (context, index) => const Gap(10),
                shrinkWrap: true,
                itemCount: 2,
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return const BusinessServiceWidget();
                },
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(bottom: 30, left: 100, right: 100), // optional margin
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonButton(
              text: 'Ok',
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HomePage(),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
