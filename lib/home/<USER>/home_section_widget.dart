import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class HomeSectionWidget extends StatelessWidget {
  const HomeSectionWidget({super.key, required this.title, required this.icon, this.onTap});
  final String title;
  final String icon;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: ((MediaQuery.of(context).size.height * 0.63) - 90) / 3, //146,
        width: (MediaQuery.of(context).size.width - 70) / 2,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(height: 1.2)),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.only(left: 8),
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity2(0.045),
                    offset: const Offset(0, 8), // only bottom shadow
                    blurRadius: 20, // smooth falloff
                    spreadRadius: 0, // no harsh expansion
                  ),
                ],
              ),
              child: AppSvgImage(icon,
                  height: Utility.isSmallDevice(context) ? 50 : null,
                  width: Utility.isSmallDevice(context) ? 50 : null),
            )
          ],
        ),
      ),
    );
  }
}
