import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/more/view/more_page.dart';
import 'package:leiuniverse/notification/cubit/refresh_cubit.dart';
import 'package:leiuniverse/notification/view/notification_page.dart';
import 'package:leiuniverse/profile/view/profile_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';

class HomeSearchSection extends StatefulWidget {
  const HomeSearchSection({super.key, this.onTap});
  final void Function()? onTap;

  @override
  State<HomeSearchSection> createState() => _HomeSearchSectionState();
}

class _HomeSearchSectionState extends State<HomeSearchSection> {
  final searchController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.37,
      width: MediaQuery.of(context).size.width,
      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top, left: 20, right: 20),
      decoration: const BoxDecoration(
        color: AppColors.whiteOffColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(23),
          bottomRight: Radius.circular(23),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                // Left: User avatar
                InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ProfilePageWraper(),
                      ),
                    );
                  },
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: BlocSelector<AuthenticationBloc, AuthenticationState, String?>(
                    selector: (state) => state.user.profileImage,
                    builder: (context, image) {
                      return Utility.imageLoader(
                        url: image ?? '',
                        placeholder: AppAssets.userAvtarImage,
                        height: Utility.isSmallDevice(context) ? 30 : 45,
                        width: Utility.isSmallDevice(context) ? 30 : 45,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(6),
                        boxShadow: [
                          BoxShadow(
                            offset: const Offset(0, 4),
                            blurRadius: 4,
                            spreadRadius: 0,
                            color: AppColors.black.withOpacity2(0.15),
                          ),
                        ],
                      );
                    },
                  ),
                ),

                // Center: Logo and App name
                Expanded(
                  child: Image.asset(
                    AppAssets.logo,
                    height: 26,
                  ),
                ),

                // Right: Notification and menu icons
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const NotificationPage(),
                          ),
                        );
                      },
                      overlayColor: WidgetStateProperty.all(AppColors.transparent),
                      child: BlocBuilder<RefreshCubit, RefreshState>(
                        builder: (context, state) {
                          final count = state is RefreshUpdated ? state.count : 0;
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: [
                                const AppSvgImage(AppAssets.notificationIcon),
                                if (count != 0)
                                  Positioned(
                                    top: -8,
                                    right: -5,
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 4),
                                      height: 16,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(50),
                                        color: AppColors.primary,
                                      ),
                                      child: Center(
                                        child: Text(
                                          count > 9 ? '9+' : count.toString(),
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelLarge
                                              ?.copyWith(color: AppColors.white, height: 1),
                                        ),
                                      ),
                                    ),
                                  )
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MorePage(),
                          ),
                        );
                      },
                      overlayColor: WidgetStateProperty.all(AppColors.transparent),
                      child: const Padding(
                        padding: EdgeInsets.only(left: 5),
                        child: AppSvgImage(AppAssets.menuDotsIcon),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Gap(Utility.isSmallDevice(context) ? 10 : 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                BlocSelector<AuthenticationBloc, AuthenticationState, String>(
                  selector: (state) => state.user.name ?? '',
                  builder: (context, name) {
                    return Flexible(
                      child: Text('Hello $name',
                          style: Theme.of(context).textTheme.titleMedium, overflow: TextOverflow.ellipsis),
                    );
                  },
                ),
                const Gap(4),
                const AppSvgImage(AppAssets.handIcon),
              ],
            ),
            Gap(Utility.isSmallDevice(context) ? 5 : 10),
            Text(
              AppConstants.homePageTagline,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  height: Utility.isSmallDevice(context) ? 1.2 : 1.5,
                  fontSize: Utility.isSmallDevice(context) ? 16 : null),
              textAlign: TextAlign.center,
            ),
            Gap(Utility.isSmallDevice(context) ? 10 : 16),
            AppTextFormField(
              hintText: 'Search by business, user...',
              fillColor: AppColors.white,
              controller: searchController,
              onTap: widget.onTap,
              readOnly: true,
              maxHeight: Utility.isSmallDevice(context) ? 40 : null,
              suffixIcon: Padding(
                padding: const EdgeInsets.all(10.0),
                child: AppSvgImage(AppAssets.homeSearchIcon,
                    height: Utility.isSmallDevice(context) ? 16 : null,
                    width: Utility.isSmallDevice(context) ? 16 : null),
              ),
            )
          ],
        ),
      ),
    );
  }
}
