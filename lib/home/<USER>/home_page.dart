import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/app/app.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/firebase_messaging_service.dart';
import 'package:leiuniverse/home/<USER>/home_search_section_view.dart';
import 'package:leiuniverse/home/<USER>/home_section_view.dart';
import 'package:leiuniverse/notification/cubit/refresh_cubit.dart';
import 'package:leiuniverse/search/view/search_page.dart';
import 'package:leiuniverse/user/model/notification_payload.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    super.initState();
    FirebaseMessagingService().initialize(
      onMessage: (message) {
        final data = message.data;
        final response = NotificationPayload.fromJson(data);
        if (response.clickAction == AppStrings.openChat) {
          navigatorKey.currentState?.push(
            MaterialPageRoute(
              builder: (context) => ChatDetailWrapperPage(
                userId: int.tryParse(response.senderId ?? '') ?? 0,
                groupChatModel: response.group,
                groupId: response.group?.id,
                advertisementId: int.tryParse(response.advertisementId ?? ''),
              ),
            ),
          );
        }
      },
    );
    navigatorKey.currentContext?.read<RefreshCubit>().loadCount();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Home search section
          HomeSearchSection(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SearchPage(),
                ),
              );
            },
          ),
          // Home section
          const HomeSectionView(),
        ],
      ),
    );
  }
}
