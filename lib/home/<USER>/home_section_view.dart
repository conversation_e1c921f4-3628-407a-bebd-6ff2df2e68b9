import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/view/categories_page.dart';
import 'package:leiuniverse/chat/view/all_chat_list_page.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/event_promotion/view/event_promotion_page.dart';
import 'package:leiuniverse/home/<USER>/home_section_widget.dart';
import 'package:leiuniverse/inquiry/view/inquiry_page.dart';
import 'package:leiuniverse/market_place/view/market_place_page.dart';
import 'package:leiuniverse/user/view/favourite_page.dart';

class HomeSectionView extends StatefulWidget {
  const HomeSectionView({super.key});

  @override
  State<HomeSectionView> createState() => _HomeSectionViewState();
}

class _HomeSectionViewState extends State<HomeSectionView> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.63,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                HomeSectionWidget(
                  title: 'Market Place',
                  icon: AppAssets.marketPlaceIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MarketPlacePage(),
                      ),
                    );
                  },
                ),
                const Gap(20),
                HomeSectionWidget(
                  title: 'Event & Promotion',
                  icon: AppAssets.eventPromotionIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EventPromotionPageWrapper(),
                      ),
                    );
                  },
                ),
              ],
            ),
            const Gap(20),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                HomeSectionWidget(
                  title: 'Send Inquiry',
                  icon: AppAssets.inquiryIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InquiryPage(),
                      ),
                    );
                  },
                ),
                const Gap(20),
                HomeSectionWidget(
                  title: 'Chat & Msg',
                  icon: AppAssets.chatIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ChatWrapperPage(),
                      ),
                    );
                  },
                ),
              ],
            ),
            const Gap(20),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                HomeSectionWidget(
                  title: 'Categories',
                  icon: AppAssets.categoriesIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const CategoriesPage(),
                      ),
                    );
                  },
                ),
                const Gap(20),
                HomeSectionWidget(
                  title: 'My Favourites',
                  icon: AppAssets.favouitesIcon,
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const FavouritePage(),
                      ),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
