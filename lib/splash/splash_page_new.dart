import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/home/<USER>/home_page.dart';
import 'package:leiuniverse/login/view/login_page.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';

class SplashPageNew extends StatefulWidget {
  const SplashPageNew({super.key});

  @override
  State<SplashPageNew> createState() => _SplashPageNewState();
}

class _SplashPageNewState extends State<SplashPageNew> {
  @override
  void initState() {
    super.initState();
    startNavigation();
  }

  Future<void> startNavigation() async {
    await Future.delayed(const Duration(milliseconds: 5500));
    _navigation();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Transform.scale(
                  scale: 1.5,
                  child: Image.asset(
                    AppAssets.splash,
                  ),
                ),
                // Transform.scale(
                //   scale: 1.5,
                //   child: splashVideoBytes == null
                //       ? GifView.asset(
                //           AppAssets.splash,
                //           onFinish: () {
                //             _navigation(context);
                //           },
                //           loop: false,
                //         )
                //       : GifView.memory(
                //           splashVideoBytes!,
                //           onFinish: () {
                //             _navigation(context);
                //           },
                //           loop: false,
                //         ),
                // ),
              ],
            ),
          ),
          Positioned(bottom: 80, child: Image.asset(AppAssets.loading, color: AppColors.white, width: 44)),
        ],
      ),
    );
  }

  void _navigation() {
    final authState = context.read<AuthenticationBloc>().state;

    if (authState.status == AuthenticationStatus.unknown || authState.status == AuthenticationStatus.loading) {
      EasyDebounce.debounce('SplashPageNavigation', const Duration(milliseconds: 1000), () async {
        _navigation();
      });
      return;
    }

    if (authState.status == AuthenticationStatus.unAutheticated) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const LoginPage()),
        (route) => false,
      );
      return;
    }

    if (authState.user.isEmailVerified == 0) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const VerificationPage()),
        (route) => false,
      );
      return;
    }
    if (authState.user.role == AppConstants.businessRole && authState.user.cityId == null) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const SetUpBusinessPage()),
        (route) => false,
      );
      return;
    }
    Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (context) => const HomePage()), (route) => false);
  }
}
