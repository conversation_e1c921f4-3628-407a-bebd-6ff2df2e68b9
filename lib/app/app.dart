// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/theme/app_theme.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/notification/cubit/refresh_cubit.dart';
import 'package:leiuniverse/splash/splash_page_new.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';

final navigatorKey = GlobalKey<NavigatorState>();

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return AppWrapper(
      child: MaterialApp(
        navigatorKey: navigatorKey,
        title: 'Lei-Universe',
        debugShowCheckedModeBanner: false,
        theme: AppTheme.light,
        themeMode: ThemeMode.system,
        home: const SplashPageNew(),
      ),
    );
  }
}

class AppWrapper extends StatelessWidget {
  const AppWrapper({
    super.key,
    required this.child,
  });
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<AuthenticationBloc>()..add(const CheckAuthentication()),
        ),
        BlocProvider(
          create: (context) => RefreshCubit(getIt<IUserRepository>())..loadCount(),
        ),
      ],
      child: child,
    );
  }
}
