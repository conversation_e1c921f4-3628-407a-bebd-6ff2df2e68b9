import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/utils/app_bloc_observer.dart';
import 'core/utils/firebase_messaging_service.dart';
import 'injector/injector.dart';

Future<void> bootstrap(Widget builder) async {
  WidgetsFlutterBinding.ensureInitialized();

  await runZonedGuarded(() async {
    Bloc.observer = AppBlocObserver();

    try {
      await configureDependencies();

      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

      await FirebaseMessagingService.initializeApp();
    } catch (e, s) {
      log("Initialization error: $e", stackTrace: s);
    }

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.dark,
      ),
    );

    runApp(builder);
  }, (error, stack) {
    log("Uncaught Flutter error: $error", stackTrace: stack);
  });
}

Future<Uint8List> getGifBytesFromAsset(String path) async {
  final data = await rootBundle.load(path);
  return data.buffer.asUint8List();
}
