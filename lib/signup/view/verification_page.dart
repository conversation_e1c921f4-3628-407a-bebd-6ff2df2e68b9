// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/home/<USER>/home_page.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/login/view/login_page.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

class VerificationPage extends StatefulWidget {
  const VerificationPage({
    super.key,
  });

  @override
  State<VerificationPage> createState() => _VerificationPageState();
}

class _VerificationPageState extends State<VerificationPage> {
  final otpController = TextEditingController();
  final isButtonLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();
  final isResendLoading = ValueNotifier<bool>(false);
  Timer? timerValue;
  final remainingTime = ValueNotifier<int>(30);

  void startTimer() {
    timerValue = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingTime.value > 0) {
        remainingTime.value = remainingTime.value - 1;
      } else {
        timer.cancel();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    isButtonLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
            return;
          }

          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const LoginPage(),
            ),
            (route) => false,
          );
        },
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Form(
              key: formKey,
              child: Column(
                children: [
                  const AppSvgImage(
                    AppAssets.emailIcon,
                    color: AppColors.primary,
                  ),
                  const Gap(20),
                  Text(
                    'Verify Email',
                    style: Theme.of(context).textTheme.headlineLarge,
                  ),
                  const Gap(6),
                  BlocSelector<AuthenticationBloc, AuthenticationState, String>(
                    selector: (state) => state.user.email ?? '',
                    builder: (context, email) {
                      return Text(
                        'We have sent verification code on\n$email',
                        style: Theme.of(context).textTheme.titleSmall,
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                  const Gap(12),
                  Text(
                    "Please check your inbox for verification email.\nDidn't receive it? Check your spam folder or\nrequest a new one.",
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                    textAlign: TextAlign.center,
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(34, 30, 34, 20),
                    child: PinCodeTextField(
                      textInputAction: TextInputAction.go,
                      obscureText: false,
                      scrollPadding: EdgeInsets.zero,
                      backgroundColor: AppColors.bgColor,
                      pinTheme: PinTheme(
                        fieldWidth: 60,
                        fieldHeight: 66,
                        shape: PinCodeFieldShape.box,
                        borderRadius: BorderRadius.circular(12),
                        selectedFillColor: AppColors.background,
                        activeFillColor: AppColors.background,
                        activeColor: AppColors.background,
                        disabledColor: AppColors.background,
                        inactiveColor: AppColors.background,
                        selectedColor: AppColors.background,
                        inactiveFillColor: AppColors.background,
                      ),
                      textStyle: Theme.of(context).textTheme.headlineLarge,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      enableActiveFill: true,
                      appContext: context,
                      length: 4,
                      controller: otpController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter OTP';
                        }
                        return null;
                      },
                    ),
                  ),
                  ValueListenableBuilder<int>(
                      valueListenable: remainingTime,
                      builder: (context, time, _) {
                        return time > 0
                            ? Text(
                                '$time Sec',
                                style: Theme.of(context).textTheme.titleSmall,
                              )
                            : ValueListenableBuilder<bool>(
                                valueListenable: isResendLoading,
                                builder: (context, loading, _) {
                                  return loading
                                      ? const CustomProgressIndecator(
                                          color: AppColors.black,
                                          size: 24,
                                        )
                                      : InkWell(
                                          onTap: loading
                                              ? null
                                              : () {
                                                  resendOtp();
                                                },
                                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                          child: Text(
                                            'Resend Email',
                                            style: Theme.of(context).textTheme.titleSmall,
                                          ),
                                        );
                                });
                      }),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 30),
                    child: CommonButton(
                      onTap: () {
                        if (formKey.currentState!.validate()) {
                          verification();
                        }
                      },
                      text: 'Verify',
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> verification() async {
    isButtonLoading.value = true;
    final email = context.read<AuthenticationBloc>().state.user.email;
    final role = context.read<AuthenticationBloc>().state.user.role;
    final response = await getIt<IAuthRepository>().verify(
      email: email ?? '',
      otp: otpController.text,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.status == 1) {
          if (mounted) {
            if (role == AppConstants.businessRole) {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => const SetUpBusinessPage(),
                ),
                (route) => false,
              );
            } else {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(
                  builder: (context) => const HomePage(),
                ),
                (route) => false,
              );
            }
          }
        }
        isButtonLoading.value = false;
      },
    );
  }

  Future<void> resendOtp() async {
    isResendLoading.value = true;
    final email = context.read<AuthenticationBloc>().state.user.email;
    final response = await getIt<IAuthRepository>().resendOtp(
      email: email ?? '',
    );

    await response.fold(
      (l) {
        isResendLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.status == 1) {
          remainingTime.value = 30;
          startTimer();
        }
        Utility.toast(message: r.message);
        isResendLoading.value = false;
      },
    );
  }
}
