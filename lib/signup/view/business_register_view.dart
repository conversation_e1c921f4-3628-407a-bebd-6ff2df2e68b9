import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';

class BusinessRegisterView extends StatefulWidget {
  const BusinessRegisterView({super.key});

  @override
  State<BusinessRegisterView> createState() => _BusinessRegisterViewState();
}

class _BusinessRegisterViewState extends State<BusinessRegisterView> {
  final pickedImageFile = ValueNotifier<PickedFileModel?>(null);
  final nameOfOwnerController = TextEditingController();
  final nameOfCompanyController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final obscureText = ValueNotifier<bool>(false);
  final termsAccepted = ValueNotifier<bool>(false);
  final isButtonLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    nameOfOwnerController.dispose();
    nameOfCompanyController.dispose();
    mobileController.dispose();
    emailController.dispose();
    passwordController.dispose();
    obscureText.dispose();
    termsAccepted.dispose();
    isButtonLoading.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Form(
        key: formKey,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.offGreen,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                AppConstants.bussinessSignupText,
                style: Theme.of(context).textTheme.labelLarge?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
              ),
            ),
            const Gap(30),
            ValueListenableBuilder<PickedFileModel?>(
                valueListenable: pickedImageFile,
                builder: (context, file, _) {
                  return PhotoFrameWidget(
                    onTap: () async {
                      final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                      if (pickedImage != null) {
                        pickedImageFile.value = PickedFileModel(file: File(pickedImage.path));
                      }
                    },
                    onRemove: () {
                      pickedImageFile.value = null;
                    },
                    images: file,
                  );
                }),
            const Gap(30),
            AppTextFormField(
              title: 'Name of owner / handler',
              controller: nameOfOwnerController,
              textInputAction: TextInputAction.next,
              isRequired: true,
              inputFormatters: [LengthLimitingTextInputFormatter(50)],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter name of owner/handler';
                }
                return null;
              },
            ),
            const Gap(14),
            AppTextFormField(
              title: 'Name of company/ organisation/ firm',
              controller: nameOfCompanyController,
              inputFormatters: [LengthLimitingTextInputFormatter(70)],
              textInputAction: TextInputAction.next,
            ),
            const Gap(14),
            AppTextFormField(
              title: 'Mobile',
              controller: mobileController,
              textInputAction: TextInputAction.next,
              keyboardType: TextInputType.phone,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(10)],
              isRequired: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter mobile number';
                }
                return null;
              },
            ),
            AppTextFormField(
              title: 'Email',
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              isRequired: true,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter email';
                } else if (!Utility.isValidEmail(value)) {
                  return 'Please enter valid email';
                }
                return null;
              },
            ),
            const Gap(14),
            ValueListenableBuilder<bool>(
                valueListenable: obscureText,
                builder: (context, obscure, _) {
                  return AppTextFormField(
                    controller: passwordController,
                    title: 'Password',
                    hintText: 'Password',
                    obscureText: !obscure,
                    isRequired: true,
                    suffixIcon: IconButton(
                      onPressed: () {
                        obscureText.value = !obscure;
                      },
                      icon: AppSvgImage(
                        !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter password';
                      }
                      return null;
                    },
                  );
                }),
            const Gap(30),
            ValueListenableBuilder<bool>(
                valueListenable: termsAccepted,
                builder: (context, terms, _) {
                  return InkWell(
                    onTap: () {
                      termsAccepted.value = !termsAccepted.value;
                    },
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Row(
                      children: [
                        AppSvgImage(terms ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon),
                        const Gap(8),
                        Text(
                          'Terms and Conditions',
                          style: Theme.of(context).textTheme.bodyLarge,
                        )
                      ],
                    ),
                  );
                }),
            const Gap(30),
            ValueListenableBuilder<bool>(
                valueListenable: isButtonLoading,
                builder: (context, loading, _) {
                  return CommonButton(
                    isLoading: loading,
                    onTap: () {
                      validateForm();
                    },
                    text: 'Signup',
                  );
                }),
          ],
        ),
      ),
    );
  }

  void validateForm() {
    if (pickedImageFile.value == null) {
      Utility.toast(message: 'Please select profile image');
      return;
    }

    if (!formKey.currentState!.validate()) {
      return;
    }

    if (!termsAccepted.value) {
      Utility.toast(message: 'Please accept terms and conditions');
      return;
    }

    businessSignup();
  }

  Future<void> businessSignup() async {
    isButtonLoading.value = true;
    final response = await getIt<IAuthRepository>().signUp(
      email: emailController.text,
      password: passwordController.text,
      mobileNumber: mobileController.text,
      name: nameOfOwnerController.text,
      companyName: nameOfCompanyController.text,
      role: AppConstants.businessRole,
      profileImage: pickedImageFile.value?.file,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data != null && r.token != null) {
          getIt<ILocalStorageRepository>().setToken(r.token);
          context.read<AuthenticationBloc>().add(CheckAuthentication(user: r.data));
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VerificationPage(),
              ),
            );
          }
        }
        isButtonLoading.value = false;
        Utility.toast(message: r.message);
      },
    );
  }
}
