import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/city_dropdown.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/country_dropdown.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';
import 'package:leiuniverse/widget/state_dropdown.dart';

class VisitorRegisterView extends StatefulWidget {
  const VisitorRegisterView({super.key});

  @override
  State<VisitorRegisterView> createState() => _VisitorRegisterViewState();
}

class _VisitorRegisterViewState extends State<VisitorRegisterView> {
  final nameController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final obscureText = ValueNotifier<bool>(false);
  final passwordController = TextEditingController();
  final pincodeController = TextEditingController();

  List<CountryModel> countryList = <CountryModel>[];
  List<CountryModel> stateList = <CountryModel>[];
  List<CountryModel> cityList = <CountryModel>[];

  final selectedCity = ValueNotifier<CountryModel?>(null);
  final selectedState = ValueNotifier<CountryModel?>(null);
  final selectedCountry = ValueNotifier<CountryModel?>(null);
  final termsAccepted = ValueNotifier<bool>(false);
  final pickedImageFile = ValueNotifier<PickedFileModel?>(null);
  final isButtonLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    nameController.dispose();
    mobileController.dispose();
    emailController.dispose();
    passwordController.dispose();
    obscureText.dispose();
    termsAccepted.dispose();
    selectedCity.dispose();
    selectedState.dispose();
    selectedCountry.dispose();
    pickedImageFile.dispose();
    isButtonLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppColors.offGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    AppConstants.visitorLoginText,
                    style:
                        Theme.of(context).textTheme.labelLarge?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
                  ),
                ),
                const Gap(30),
                ValueListenableBuilder<PickedFileModel?>(
                    valueListenable: pickedImageFile,
                    builder: (context, file, _) {
                      return PhotoFrameWidget(
                        onTap: () async {
                          final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                          if (pickedImage != null) {
                            pickedImageFile.value = PickedFileModel(file: File(pickedImage.path));
                          }
                        },
                        onRemove: () {
                          pickedImageFile.value = null;
                        },
                        images: file,
                      );
                    }),
                const Gap(30),
                AppTextFormField(
                  title: 'Name',
                  controller: nameController,
                  isRequired: true,
                  inputFormatters: [LengthLimitingTextInputFormatter(50)],
                  textInputAction: TextInputAction.next,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter name';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                AppTextFormField(
                  title: 'Mobile',
                  controller: mobileController,
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.phone,
                  isRequired: true,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(10)],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter mobile number';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                AppTextFormField(
                  title: 'Email',
                  controller: emailController,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  isRequired: true,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter email';
                    } else if (!Utility.isValidEmail(value)) {
                      return 'Please enter valid email';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                ValueListenableBuilder<bool>(
                    valueListenable: obscureText,
                    builder: (context, obscure, _) {
                      return AppTextFormField(
                        controller: passwordController,
                        title: 'Password',
                        hintText: 'Password',
                        isRequired: true,
                        obscureText: !obscure,
                        suffixIcon: IconButton(
                          onPressed: () {
                            obscureText.value = !obscure;
                          },
                          icon: AppSvgImage(
                            !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter password';
                          }
                          return null;
                        },
                      );
                    }),
              ],
            ),
          ),
          const Divider(
            height: 1,
            color: AppColors.border,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Address',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                ),
                const Gap(14),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: AppTextFormField(
                        title: 'Pincode',
                        isRequired: true,
                        controller: pincodeController,
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(10)],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter pincode';
                          }
                          return null;
                        },
                      ),
                    ),
                    const Gap(15),
                    Expanded(
                      child: ValueListenableBuilder<CountryModel?>(
                          valueListenable: selectedCountry,
                          builder: (context, selected, _) {
                            return CountryDropdown(
                              isRequired: true,
                              selectedCountry: selected,
                              onCountryChanged: (country) {
                                countryList = [country];
                                selectedCountry.value = country;
                                selectedState.value = null;
                                selectedCity.value = null;
                              },
                            );
                          }),
                    ),
                  ],
                ),
                const Gap(14),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: ValueListenableBuilder<CountryModel?>(
                          valueListenable: selectedCountry,
                          builder: (context, country, _) {
                            return ValueListenableBuilder<CountryModel?>(
                                valueListenable: selectedState,
                                builder: (context, selected, _) {
                                  return StateDropdown(
                                    isRequired: true,
                                    countryId: country?.id,
                                    selectedState: selected,
                                    onStateChanged: (state) {
                                      stateList = [state];
                                      selectedState.value = state;
                                      selectedCity.value = null;
                                    },
                                  );
                                });
                          }),
                    ),
                    const Gap(15),
                    Expanded(
                      child: ValueListenableBuilder<CountryModel?>(
                          valueListenable: selectedState,
                          builder: (context, state, _) {
                            return CityDropdown(
                              isRequired: true,
                              stateId: state?.id,
                              onCityChanged: (city) {
                                cityList = city;
                                selectedCity.value = city.firstOrNull;
                              },
                              selectedCity: cityList,
                            );
                          }),
                    ),
                  ],
                ),
                const Gap(30),
                ValueListenableBuilder<bool>(
                    valueListenable: termsAccepted,
                    builder: (context, terms, _) {
                      return InkWell(
                        onTap: () {
                          termsAccepted.value = !termsAccepted.value;
                        },
                        overlayColor: WidgetStateProperty.all(AppColors.transparent),
                        child: Row(
                          children: [
                            AppSvgImage(terms ? AppAssets.selectedCheckboxIcon : AppAssets.unselectedCheckboxIcon),
                            const Gap(8),
                            Text(
                              'Terms and Conditions',
                              style: Theme.of(context).textTheme.bodyLarge,
                            )
                          ],
                        ),
                      );
                    }),
                const Gap(30),
                ValueListenableBuilder<bool>(
                    valueListenable: isButtonLoading,
                    builder: (context, loading, _) {
                      return CommonButton(
                        isLoading: loading,
                        onTap: () {
                          validateForm();
                        },
                        text: 'Signup',
                      );
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void validateForm() {
    if (pickedImageFile.value == null) {
      Utility.toast(message: 'Please select profile image');
      return;
    }

    if (!formKey.currentState!.validate()) {
      return;
    }

    if (!termsAccepted.value) {
      Utility.toast(message: 'Please accept terms and conditions');
      return;
    }

    visitorSignup();
  }

  Future<void> visitorSignup() async {
    isButtonLoading.value = true;
    final response = await getIt<IAuthRepository>().signUp(
      email: emailController.text,
      password: passwordController.text,
      mobileNumber: mobileController.text,
      name: nameController.text,
      role: AppConstants.visitorRole,
      profileImage: pickedImageFile.value?.file,
      pincode: pincodeController.text,
      cityId: selectedCity.value?.id?.toString(),
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data != null && r.token != null) {
          getIt<ILocalStorageRepository>().setToken(r.token);
          context.read<AuthenticationBloc>().add(CheckAuthentication(user: r.data));
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VerificationPage(),
              ),
            );
          }
        }
        isButtonLoading.value = false;
        Utility.toast(message: r.message);
      },
    );
  }
}
