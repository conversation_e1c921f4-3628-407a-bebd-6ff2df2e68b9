import 'package:equatable/equatable.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class CountryListResponse extends Equatable {
  const CountryListResponse({
    this.status,
    this.message,
    required this.data,
  });

  final int? status;
  final String? message;
  final List<CountryModel> data;

  CountryListResponse copyWith({
    int? status,
    String? message,
    List<CountryModel>? data,
  }) {
    return CountryListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory CountryListResponse.fromJson(Map<String, dynamic> json) {
    return CountryListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<CountryModel>.from(json["data"]!.map((x) => CountryModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
