import 'package:equatable/equatable.dart';

class CountryModel extends Equatable {
  const CountryModel({
    this.id,
    this.name,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.state,
    this.country,
  });

  final int? id;
  final String? name;
  final int? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CountryModel? state;
  final CountryModel? country;

  CountryModel copyWith({
    int? id,
    String? name,
    int? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    CountryModel? state,
    CountryModel? country,
  }) {
    return CountryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      state: state ?? this.state,
      country: country ?? this.country,
    );
  }

  factory CountryModel.fromJson(Map<String, dynamic> json) {
    return CountryModel(
      id: json["id"],
      name: json["name"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      state: json["state"] == null ? null : CountryModel.fromJson(json["state"]),
      country: json["country"] == null ? null : CountryModel.fromJson(json["country"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "state": state?.toJson(),
        "country": country?.toJson(),
      };

  @override
  String toString() {
    return "$id, $name, $isActive, $createdAt, $updatedAt, $state, $country";
  }

  @override
  List<Object?> get props => [id, name, isActive, createdAt, updatedAt, state, country];

  static const CountryModel all = CountryModel(id: null, name: 'All');
}
