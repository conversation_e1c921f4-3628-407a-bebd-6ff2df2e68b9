import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/more/repository/i_setting_repository.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SelectStateDialog extends StatefulWidget {
  const SelectStateDialog({
    super.key,
    this.selectedState,
    this.onStateChanged,
    this.countryId,
    this.isAddAll = false,
  });
  final int? countryId;
  final CountryModel? selectedState;
  final void Function(CountryModel? state)? onStateChanged;
  final bool isAddAll;

  @override
  State<SelectStateDialog> createState() => SelectStateDialogState();
}

class SelectStateDialogState extends State<SelectStateDialog> {
  final stateList = ValueNotifier<List<CountryModel>>([]);
  int page = 0;

  int perPage = 10;

  bool stop = false;

  final searchController = TextEditingController();

  final isLoading = ValueNotifier<bool>(false);
  final isPageLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    getStateList(isFirst: true);
  }

  Future<void> getStateList({bool isFirst = false}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    page += 1;
    final response = await getIt<ISettingRepository>().stateList(
      countryId: widget.countryId,
      page: page,
      perPage: perPage,
      search: searchController.text.trim(),
    );

    await response.fold(
      (l) {
        isLoading.value = false;
        isPageLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.status == 1 && r.data.isNotEmpty) {
          if (r.data.length < 10) {
            stop = true;
          }
          if (isFirst && page == 1) {
            stateList.value = [
              if (widget.isAddAll && searchController.text.trim().isEmpty) CountryModel.all,
              ...r.data
            ];
          } else {
            stateList.value = [...stateList.value, ...r.data];
          }
        }
        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Spacer(),
                Text(
                  'Select State',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const Spacer(),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const Icon(Icons.close, color: AppColors.subText),
                ),
              ],
            ),
            const Gap(20),
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              onChanged: (value) {
                EasyDebounce.debounce('state_search', const Duration(milliseconds: 500), () {
                  stateList.value.clear();
                  page = 0;
                  stop = false;
                  getStateList(isFirst: true);
                });
              },
            ),
            const Gap(16),
            ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  return Expanded(
                    child: loading
                        ? const CustomProgressIndecator(
                            color: AppColors.subText,
                            size: 40,
                          )
                        : ValueListenableBuilder<List<CountryModel>>(
                            valueListenable: stateList,
                            builder: (context, country, _) {
                              return country.isEmpty
                                  ? Utility.noDataWidget(context: context, text: 'No State Found')
                                  : ListView.builder(
                                      padding: EdgeInsets.zero,
                                      itemCount: country.length,
                                      itemBuilder: (context, index) {
                                        final city = country[index];

                                        final child = InkWell(
                                          onTap: () {
                                            widget.onStateChanged?.call(city);
                                            Navigator.pop(context);
                                          },
                                          child: Container(
                                              padding: const EdgeInsets.all(16),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(8),
                                                color: widget.selectedState?.id == city.id
                                                    ? AppColors.primary.withOpacity2(0.3)
                                                    : null,
                                              ),
                                              child: Text(
                                                city.name!,
                                                style: Theme.of(context).textTheme.bodyLarge,
                                              )),
                                        );

                                        if (index == country.length - 1 && !stop) {
                                          return VisibilityDetector(
                                            key: Key('${city.id}_$index'),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                child,
                                                ValueListenableBuilder<bool>(
                                                    valueListenable: isPageLoading,
                                                    builder: (context, pageLoading, _) {
                                                      return pageLoading
                                                          ? const Padding(
                                                              padding: EdgeInsets.only(top: 30),
                                                              child: CustomProgressIndecator(
                                                                color: AppColors.subText,
                                                                size: 40,
                                                              ),
                                                            )
                                                          : const SizedBox.shrink();
                                                    })
                                              ],
                                            ),
                                            onVisibilityChanged: (VisibilityInfo info) {
                                              if (!stop &&
                                                  index == (country.length - 1) &&
                                                  !isLoading.value &&
                                                  !isPageLoading.value) {
                                                getStateList(isFirst: false);
                                              }
                                            },
                                          );
                                        }

                                        return child;
                                      },
                                    );
                            }),
                  );
                })
          ],
        ),
      ),
    );
  }
}
