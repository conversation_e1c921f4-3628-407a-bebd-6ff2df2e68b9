import 'package:collection/collection.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/more/repository/i_setting_repository.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SelectCityDialog extends StatefulWidget {
  const SelectCityDialog({
    super.key,
    this.stateId,
    this.selectedCityList,
    this.onCitiesChanged,
    this.isMulti = false,
    this.isAddAll = false,
  });

  final int? stateId;
  final List<CountryModel>? selectedCityList;
  final void Function(List<CountryModel>? cities)? onCitiesChanged;
  final bool isMulti;
  final bool isAddAll;

  @override
  State<SelectCityDialog> createState() => SelectCityDialogState();
}

class SelectCityDialogState extends State<SelectCityDialog> {
  final cityList = ValueNotifier<List<CountryModel>>([]);
  final selectedCityList = ValueNotifier<List<CountryModel>>([]);
  int page = 0;
  final int perPage = 10;
  bool stop = false;

  final searchController = TextEditingController();
  final isLoading = ValueNotifier<bool>(false);
  final isPageLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    selectedCityList.value = [...(widget.selectedCityList ?? [])];
    getCityList(isFirst: true);
  }

  CountryModel? _findById(List<CountryModel> list, int? id) {
    try {
      return list.firstWhere((e) => e.id == id);
    } catch (_) {
      return null;
    }
  }

  bool _selectedContains(CountryModel item) {
    if (item.id == null) {
      return selectedCityList.value.any((e) => e.id == null);
    } else {
      return selectedCityList.value.any((e) => e.id == item.id);
    }
  }

  Future<void> getCityList({bool isFirst = false}) async {
    if (isFirst) {
      isLoading.value = true;
      cityList.value = [];
      page = 0;
      stop = false;
    } else {
      isPageLoading.value = true;
    }

    page += 1;

    final response = await getIt<ISettingRepository>().cityList(
      stateId: widget.stateId,
      page: page,
      perPage: perPage,
      search: searchController.text.trim(),
    );

    await response.fold(
      (l) {
        isLoading.value = false;
        isPageLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data.isNotEmpty) {
          if (r.data.length < perPage) stop = true;

          List<CountryModel> newData = r.data;

          if (isFirst && widget.isAddAll && searchController.text.trim().isEmpty) {
            newData = [CountryModel.all, ...newData];
          }

          if (isFirst) {
            cityList.value = newData;

            if (selectedCityList.value.any((e) => e.id == null) && widget.isAddAll) {
              final preserved = <CountryModel>[];
              preserved.add(CountryModel.all);
              preserved.addAll(newData.where((e) => e.id != null).toList());
              selectedCityList.value = preserved;
            } else {
              final preserved = <CountryModel>[];
              for (final sel in selectedCityList.value) {
                if (sel.id == null) {
                  if (widget.isAddAll) preserved.add(CountryModel.all);
                } else {
                  final match = _findById(newData, sel.id);
                  preserved.add(match ?? sel);
                }
              }
              // IMPORTANT: do not reapply widget.selectedCityList here
              selectedCityList.value = preserved;
            }
          } else {
            // pagination append
            cityList.value = [...cityList.value, ...newData];

            if (selectedCityList.value.any((e) => e.id == null)) {
              final idsAlready = selectedCityList.value.where((e) => e.id != null).map((e) => e.id).toSet();
              final toAdd = r.data.where((d) => d.id != null && !idsAlready.contains(d.id)).toList();
              selectedCityList.value = [...selectedCityList.value, ...toAdd];
            }
          }
        }

        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(),
                Text('Select City', style: Theme.of(context).textTheme.headlineMedium),
                const Spacer(),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const Icon(Icons.close, color: AppColors.subText),
                ),
              ],
            ),
            const Gap(20),
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              onChanged: (value) {
                EasyDebounce.debounce('city_search', const Duration(milliseconds: 500), () {
                  cityList.value = [];
                  page = 0;
                  stop = false;
                  getCityList(isFirst: true);
                });
              },
            ),
            const Gap(16),
            ValueListenableBuilder<bool>(
              valueListenable: isLoading,
              builder: (context, loading, _) {
                return Expanded(
                  child: loading
                      ? const CustomProgressIndecator(color: AppColors.subText, size: 40)
                      : ValueListenableBuilder<List<CountryModel>>(
                          valueListenable: cityList,
                          builder: (context, country, _) {
                            return country.isEmpty
                                ? Utility.noDataWidget(context: context, text: 'No City Found')
                                : ValueListenableBuilder<List<CountryModel>>(
                                    valueListenable: selectedCityList,
                                    builder: (context, selected, __) {
                                      return ListView.builder(
                                        padding: EdgeInsets.zero,
                                        itemCount: country.length,
                                        itemBuilder: (context, index) {
                                          final city = country[index];

                                          final child = Padding(
                                            padding: EdgeInsets.only(bottom: widget.isMulti ? 4 : 0),
                                            child: InkWell(
                                              onTap: () {
                                                if (widget.isMulti) {
                                                  if (city.id == null) {
                                                    // toggle All — All is only present when search is empty (we add it only in that condition)
                                                    if (selectedCityList.value.any((e) => e.id == null)) {
                                                      selectedCityList.value = [];
                                                    } else {
                                                      selectedCityList.value = [
                                                        CountryModel.all,
                                                        ...cityList.value.where((e) => e.id != null)
                                                      ];
                                                    }
                                                  } else {
                                                    final alreadySelected =
                                                        selectedCityList.value.any((e) => e.id == city.id);
                                                    if (alreadySelected) {
                                                      selectedCityList.value = [
                                                        ...selectedCityList.value.where((e) => e.id != city.id)
                                                      ];
                                                    } else {
                                                      selectedCityList.value = [...selectedCityList.value, city];
                                                    }

                                                    // Remove "All" when individual selection exists
                                                    selectedCityList.value = [
                                                      ...selectedCityList.value.where((e) => e.id != null)
                                                    ];

                                                    // Auto-select "All" only if search is empty (so visible list == unfiltered list)
                                                    final allCities =
                                                        cityList.value.where((e) => e.id != null).toList();
                                                    final currSelected = selectedCityList.value;
                                                    final isSearchEmpty = searchController.text.trim().isEmpty;
                                                    final allSelected = allCities.isNotEmpty &&
                                                        allCities.every((c) => currSelected.any((s) => s.id == c.id)) &&
                                                        isSearchEmpty;

                                                    if (allSelected && widget.isAddAll) {
                                                      selectedCityList.value = [CountryModel.all, ...allCities];
                                                    }
                                                  }
                                                } else {
                                                  widget.onCitiesChanged?.call([city]);
                                                  Navigator.pop(context);
                                                }
                                              },
                                              child: Container(
                                                padding: const EdgeInsets.all(16),
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(8),
                                                  color: widget.isMulti
                                                      ? _selectedContains(city)
                                                          ? AppColors.primary.withOpacity2(0.3)
                                                          : null
                                                      : (selectedCityList.value.isNotEmpty
                                                              ? (selectedCityList.value.first.id == city.id)
                                                              : (widget.selectedCityList?.firstOrNull?.id == city.id ||
                                                                  (widget.isAddAll &&
                                                                      city.id == null &&
                                                                      widget.selectedCityList?.firstOrNull?.id ==
                                                                          null)))
                                                          ? AppColors.primary.withOpacity2(0.3)
                                                          : null,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Text(
                                                        city.name!,
                                                        style: Theme.of(context).textTheme.bodyLarge,
                                                      ),
                                                    ),
                                                    if (widget.isMulti)
                                                      Icon(
                                                        _selectedContains(city)
                                                            ? Icons.check_box
                                                            : Icons.check_box_outline_blank,
                                                        color: AppColors.primary,
                                                      )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );

                                          if (index == country.length - 1 && !stop) {
                                            return VisibilityDetector(
                                              key: Key('${city.id}_$index'),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  child,
                                                  ValueListenableBuilder<bool>(
                                                    valueListenable: isPageLoading,
                                                    builder: (context, pageLoading, _) {
                                                      return pageLoading
                                                          ? const Padding(
                                                              padding: EdgeInsets.only(top: 30),
                                                              child: CustomProgressIndecator(
                                                                color: AppColors.subText,
                                                                size: 40,
                                                              ),
                                                            )
                                                          : const SizedBox.shrink();
                                                    },
                                                  ),
                                                ],
                                              ),
                                              onVisibilityChanged: (VisibilityInfo info) {
                                                if (!stop &&
                                                    index == (country.length - 1) &&
                                                    !isLoading.value &&
                                                    !isPageLoading.value) {
                                                  getCityList(isFirst: false);
                                                }
                                              },
                                            );
                                          }

                                          return child;
                                        },
                                      );
                                    },
                                  );
                          },
                        ),
                );
              },
            ),
            if (widget.isMulti) ...[
              const Gap(10),
              CommonButton(
                onTap: () {
                  widget.onCitiesChanged?.call(selectedCityList.value);
                  Navigator.pop(context);
                },
                text: 'Submit',
              ),
              const Gap(16),
            ],
          ],
        ),
      ),
    );
  }
}
