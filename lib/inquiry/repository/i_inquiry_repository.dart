import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';

part 'inquiry_repository.dart';

abstract class IInquiryRepository {
  IInquiryRepository(this.client);
  final Client client;

  ApiResult<CommonResponse> create({
    String? description,
    int? countryId,
    int? stateId,
    List<int>? cityIds,
    List<int>? categoryIds,
  });
}
