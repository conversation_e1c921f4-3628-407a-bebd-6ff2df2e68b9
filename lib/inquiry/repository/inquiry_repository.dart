part of 'i_inquiry_repository.dart';

@Injectable(as: IInquiryRepository)
class InquiryRepository extends IInquiryRepository {
  InquiryRepository(super.client);

  @override
  ApiResult<CommonResponse> create({
    String? description,
    int? countryId,
    int? stateId,
    List<int>? cityIds,
    List<int>? categoryIds,
  }) async {
    final response = await client.multipart(
      url: AppStrings.createInquiry,
      requests: {
        if (description != null) 'description': description,
        if (countryId != null) 'country_id': countryId.toString(),
        if (stateId != null) 'state_id': stateId.toString(),
        if (cityIds != null && cityIds.isNotEmpty)
          for (int i = 0; i < cityIds.length; i++) 'city_id[$i]': cityIds[i].toString(),
        if (categoryIds != null && categoryIds.isNotEmpty)
          for (int i = 0; i < categoryIds.length; i++) 'category_id[$i]': categoryIds[i].toString(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }
}
