import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/search/widget/search_widget.dart';
import 'package:leiuniverse/user/model/search_model.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> with PaginationMixin {
  final searchController = TextEditingController();
  final searchValue = ValueNotifier('');
  final searchHistory = ValueNotifier<List<SearchModel>>([]);
  final searchList = ValueNotifier<List<SearchModel>>(<SearchModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    getRecentSearch();
  }

  void getRecentSearch() {
    searchHistory.value = getIt<ILocalStorageRepository>().getRecentSearch();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    searchList.dispose();
    searchController.dispose();
    searchHistory.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IUserRepository>().searchList(search: searchController.text.trim());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      searchList.value = [...r.data ?? []];
      hasReachedMax = (r.data?.length ?? 0) < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IUserRepository>().searchList(page: page);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = (r.data?.length ?? 0) < 10;
      searchList.value = [...searchList.value, ...r.data ?? []];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: ValueListenableBuilder<String>(
          valueListenable: searchValue,
          builder: (context, value, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  color: AppColors.inputField,
                  child: AppTextFormField(
                    controller: searchController,
                    hintText: 'Search by business, user...',
                    onChanged: (val) {
                      EasyDebounce.debounce(
                        'search',
                        const Duration(milliseconds: 500),
                        () {
                          searchValue.value = val;
                          load();
                          getRecentSearch();
                        },
                      );
                    },
                    suffixIcon: value.isNotEmpty
                        ? InkWell(
                            onTap: () {
                              searchController.clear();
                              searchValue.value = '';
                              getRecentSearch();
                            },
                            overlayColor: WidgetStateProperty.all(AppColors.transparent),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 18, horizontal: 17),
                              child: AppSvgImage(AppAssets.closeIcon),
                            ),
                          )
                        : null,
                    prefixIcon: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 17),
                      child: InkWell(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          Navigator.pop(context);
                        },
                        overlayColor: WidgetStateProperty.all(AppColors.transparent),
                        child: const AppSvgImage(AppAssets.leftArrowIcon),
                      ),
                    ),
                  ),
                ),
                if (value.isEmpty)
                  ValueListenableBuilder<List<SearchModel>>(
                      valueListenable: searchHistory,
                      builder: (context, history, _) {
                        if (history.isEmpty && value.isEmpty) {
                          return Flexible(child: Utility.noDataWidget(context: context, text: 'No Recent Search'));
                        }
                        return Flexible(
                            child: ListView.builder(
                          itemCount: history.length,
                          padding: const EdgeInsets.only(bottom: 20),
                          controller: scrollPaginationController,
                          physics: const AlwaysScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            final searchItem = history[index];
                            return SearchWidget(
                              searchString: value,
                              search: searchItem,
                            );
                          },
                        ));
                      }),
                if (value.isNotEmpty)
                  Flexible(
                    child: RefreshIndicator(
                      onRefresh: () {
                        return load();
                      },
                      child: ValueListenableBuilder<bool>(
                          valueListenable: isLoading,
                          builder: (context, loading, _) {
                            if (loading) {
                              return const Center(
                                child: CustomProgressIndecator(
                                  size: 40,
                                  color: AppColors.primary,
                                ),
                              );
                            }
                            return ValueListenableBuilder<List<SearchModel>>(
                                valueListenable: searchList,
                                builder: (context, search, _) {
                                  if (!loading && search.isEmpty && value.isNotEmpty) {
                                    return Utility.noDataWidget(context: context, text: 'No Data Found');
                                  }
                                  return ValueListenableBuilder<bool>(
                                      valueListenable: isLoadingMore,
                                      builder: (context, loadingMore, _) {
                                        return ListView.builder(
                                          itemCount: search.length,
                                          padding: const EdgeInsets.only(bottom: 20),
                                          physics: const AlwaysScrollableScrollPhysics(),
                                          itemBuilder: (context, index) {
                                            final searchItem = search[index];
                                            return Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                SearchWidget(
                                                  searchString: value,
                                                  search: searchItem,
                                                ),
                                                if (!hasReachedMax && loadingMore && index == search.length - 1)
                                                  const Padding(
                                                    padding: EdgeInsets.only(top: 20),
                                                    child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                                                  ),
                                              ],
                                            );
                                          },
                                        );
                                      });
                                });
                          }),
                    ),
                  )
              ],
            );
          },
        ),
      ),
    );
  }
}
