import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/user/model/search_model.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class SearchWidget extends StatelessWidget {
  const SearchWidget({super.key, this.searchString, this.search});
  final String? searchString;
  final SearchModel? search;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (search != null) {
          await getIt<ILocalStorageRepository>().saveRecentSearch(search!);
        }

        Navigator.push(
          // ignore: use_build_context_synchronously
          context,
          MaterialPageRoute(
            builder: (context) => ProfileDetailPage(userId: search?.id),
          ),
        );
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Column(
        children: [
          Padding(
            padding:
                EdgeInsets.symmetric(horizontal: 20, vertical: search?.role == AppConstants.businessRole ? 11 : 20),
            child: Row(
              children: [
                AppSvgImage(
                  searchString?.isEmpty ?? true ? AppAssets.clockIcon : AppAssets.searchIcon,
                  height: 20,
                  width: 20,
                  color: AppColors.subText,
                ),
                const Gap(11),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        search?.name ?? '',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppColors.black,
                            ),
                      ),
                      if (search?.role == AppConstants.businessRole)
                        Text(
                          search?.role == AppConstants.businessRole ? 'Business' : '',
                          style: Theme.of(context).textTheme.labelLarge?.copyWith(
                                color: AppColors.subText,
                              ),
                        ),
                    ],
                  ),
                ),
                if (searchString?.isNotEmpty ?? true) const AppSvgImage(AppAssets.rightArrowIconIos),
              ],
            ),
          ),
          const Divider(
            height: 0,
          )
        ],
      ),
    );
  }
}
