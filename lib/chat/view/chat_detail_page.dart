// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:easy_debounce/easy_debounce.dart';
import 'package:firebase_ui_firestore/firebase_ui_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/bloc/chat_detail_bloc.dart';
import 'package:leiuniverse/chat/model/group_chat_model.dart';
import 'package:leiuniverse/chat/repository/i_chat_repository.dart';
import 'package:leiuniverse/chat/widget/chat_message_view.dart';
import 'package:leiuniverse/chat/widget/chat_more_options.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/always_scrollable_fixed_position.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/report/view/report_page.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';
import 'package:leiuniverse/widget/no_data.dart';

class ChatDetailWrapperPage extends StatelessWidget {
  const ChatDetailWrapperPage({
    super.key,
    required this.userId,
    this.advertisementId,
    this.groupId,
    this.groupChatModel,
    this.advertisement,
  });
  final int userId;
  final int? advertisementId;
  final int? groupId;
  final GroupChatModel? groupChatModel;
  final Advertisemnet? advertisement;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ChatDetailBloc>(
        param1: {'login_user_id': context.read<AuthenticationBloc>().state.user.id, 'other_user_id': userId},
        param2: {'advertisement_id': advertisementId, 'group_id': groupId},
      ),
      child: ChatDetailPage(
          otherUserId: userId,
          groupId: groupId,
          groupChatModel: groupChatModel,
          advertisementId: advertisementId,
          advertisement: advertisement),
    );
  }
}

class ChatDetailPage extends StatefulWidget {
  const ChatDetailPage({
    super.key,
    required this.otherUserId,
    this.groupId,
    this.advertisementId,
    this.groupChatModel,
    this.advertisement,
  });
  final int otherUserId;
  final int? groupId;
  final int? advertisementId;
  final GroupChatModel? groupChatModel;
  final Advertisemnet? advertisement;

  @override
  State<ChatDetailPage> createState() => _ChatDetailPageState();
}

class _ChatDetailPageState extends State<ChatDetailPage> {
  final messageController = TextEditingController();
  final isStackLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    if ((widget.groupId != null && context.read<AuthenticationBloc>().state.user.isJoinedDefaultChatGroup == 1) ||
        widget.groupId == null) {
      context.read<ChatDetailBloc>().add(const ChatDetailLoad());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CommonAppBar(
        onBackTap: () {
          FocusManager.instance.primaryFocus?.unfocus();
          Navigator.pop(context);
        },
        titleWidget: BlocSelector<ChatDetailBloc, ChatDetailState, UserModel?>(
          selector: (state) => state.otherUser,
          builder: (context, user) {
            return BlocSelector<ChatDetailBloc, ChatDetailState, Advertisemnet?>(
              selector: (state) => state.advertisement,
              builder: (context, advertisement) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    widget.advertisement != null || advertisement != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(44 / 2),
                            child: Banner(
                              location: BannerLocation.topStart,
                              color: AppColors.primary.withOpacity2(0.7),
                              message: 'Adv',
                              textStyle:
                                  const TextStyle(color: AppColors.white, fontSize: 12, fontWeight: FontWeight.w600),
                              child: Utility.imageLoader(
                                url: (widget.advertisement ?? advertisement)?.images.firstOrNull?.imageUrl ?? '',
                                placeholder: AppAssets.placeholderImage,
                                height: 44,
                                width: 44,
                                fit: BoxFit.cover,
                                shape: BoxShape.circle,
                                isShapeCircular: true,
                              ),
                            ),
                          )
                        : Utility.imageLoader(
                            url: widget.groupId != null && widget.groupChatModel != null
                                ? (widget.groupChatModel?.imageUrl ?? '')
                                : user?.profileImage ?? '',
                            placeholder: AppAssets.placeholderImage,
                            height: 44,
                            width: 44,
                            isShapeCircular: true,
                            shape: BoxShape.circle,
                          ),
                    const Gap(6),
                    Flexible(
                      child: Text(
                        widget.groupId != null && widget.groupChatModel != null
                            ? (widget.groupChatModel?.name ?? '')
                            : (widget.advertisement ?? advertisement)?.title ?? user?.name ?? '',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ],
                );
              },
            );
          },
        ),
        actions: [
          if ((widget.groupId != null && context.read<AuthenticationBloc>().state.user.isJoinedDefaultChatGroup == 1) ||
              widget.groupId == null)
            BlocBuilder<ChatDetailBloc, ChatDetailState>(
              builder: (context, state) {
                return state.mapOrNull(
                      loaded: (loadedState) {
                        if (loadedState.otherUser.isBlocked == 1) {
                          return const SizedBox();
                        }
                        return ChatMoreOptions(
                          onBlockTap: () {
                            AppDailogs.blockUserDailog(
                              context,
                              onBlock: () {
                                blockToggleUser();
                              },
                            );
                          },
                          onReportTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => ReportPage(
                                    userId: widget.otherUserId,
                                  ),
                                ));
                          },
                          onDeleteChatTap: () {
                            AppDailogs.deleteChatDailog(
                              context,
                              isGroup: widget.groupId != null,
                              onDelete: () {
                                _deleteChat();
                              },
                            );
                          },
                          isGroup: widget.groupId != null,
                          showDelete: loadedState.chatId != null,
                        );
                      },
                    ) ??
                    const SizedBox();
              },
            ),
        ],
      ),
      body: BlocBuilder<ChatDetailBloc, ChatDetailState>(
        builder: (context, state) {
          if (widget.groupId == null && state.otherUser?.isBlocked == 1) {
            return Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      state.otherUser?.isBlockedByMe == 1
                          ? 'You blocked ${state.otherUser?.name ?? ''}'
                          : '${state.otherUser?.name ?? ''} blocked you',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.red,
                          ),
                    ),
                    if (state.otherUser?.isBlockedByMe == 1) ...[
                      const Gap(6),
                      CommonButton(
                        text: 'Unblock',
                        onTap: () {
                          blockToggleUser();
                        },
                      )
                    ],
                  ],
                ),
              ),
            );
          }
          if (widget.groupId != null && context.read<AuthenticationBloc>().state.user.isJoinedDefaultChatGroup != 1) {
            return Center(
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'You left this group, You can join this group again',
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                    const Gap(6),
                    CommonButton(
                      text: 'Rejoin',
                      onTap: () {
                        rejoinGroup();
                      },
                    )
                  ],
                ),
              ),
            );
          }
          return state.map(
            initial: (state) => const CustomProgressIndecator(
              color: AppColors.primary,
              size: 40,
            ),
            loading: (state) => const CustomProgressIndecator(
              color: AppColors.primary,
              size: 40,
            ),
            loaded: (state) {
              if (state.getFirebaseMessageQuery == null) {
                return const NoData();
              }
              return FirestoreQueryBuilder(
                pageSize: 30,
                query: state.getFirebaseMessageQuery!,
                builder: (context, snapshot, child) {
                  if (snapshot.isFetching) {
                    return const CustomProgressIndecator(
                      color: AppColors.primary,
                      size: 40,
                    );
                  }

                  EasyDebounce.debounce('read_chat', const Duration(milliseconds: 800), () {
                    getIt<IChatRepository>().readChatBadge(
                      loginId: context.read<AuthenticationBloc>().state.user.id,
                      otherUserId: widget.otherUserId,
                      chatDocument: state.chatDocument,
                      groupId: widget.groupId,
                      advertisementId: widget.advertisementId,
                    );
                  });

                  final chatList = [...snapshot.docs.map((e) => e.data())];
                  return GroupedListView(
                    elements: chatList,
                    physics: const AllwaysScrollableFixedPositionScrollPhysics(),
                    reverse: true,
                    order: GroupedListOrder.DESC,
                    useStickyGroupSeparators: true,
                    groupBy: (chat) => DateTime(
                      chat.timeStampLocalDate.year,
                      chat.timeStampLocalDate.month,
                      chat.timeStampLocalDate.day,
                    ),
                    separator: const Gap(16),
                    stickyHeaderBackgroundColor: Colors.transparent,
                    padding: const EdgeInsets.all(16),
                    itemComparator: (element1, element2) => element1.timeStampDate.compareTo(element2.timeStampDate),
                    groupHeaderBuilder: (chat) => Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: AppColors.background,
                            borderRadius: BorderRadius.circular(7),
                          ),
                          margin: const EdgeInsets.symmetric(vertical: 16),
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 9),
                          child: Text(
                            chat.formattedDate(context),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontSize: 13,
                                  color: AppColors.text,
                                ),
                          ),
                        ),
                      ],
                    ),
                    indexedItemBuilder: (context, chat, index) {
                      final isLastChatIndex = (chatList.length - 1) == index;
                      if (snapshot.isFetchingMore && isLastChatIndex) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            const CustomProgressIndecator(),
                            const Gap(16),
                            ChatMessageView(
                              chat: chat,
                              isOtherUser: chat.senderId != context.read<AuthenticationBloc>().state.user.id,
                              chatType: widget.groupId != null ? ChatType.group.name : ChatType.private.name,
                            ),
                          ],
                        );
                      }
                      if (snapshot.hasMore && index + 1 == snapshot.docs.length) {
                        snapshot.fetchMore();
                      }
                      return ChatMessageView(
                        chat: chat,
                        isOtherUser: chat.senderId != context.read<AuthenticationBloc>().state.user.id,
                        chatType: widget.groupId != null ? ChatType.group.name : ChatType.private.name,
                      );
                    },
                  );
                },
              );
            },
            error: (state) => const NoData(),
          );
        },
      ),
      bottomNavigationBar: Builder(builder: (context) {
        if ((widget.groupId != null && context.read<AuthenticationBloc>().state.user.isJoinedDefaultChatGroup != 1)) {
          return const SizedBox();
        }

        return BlocSelector<ChatDetailBloc, ChatDetailState, UserModel?>(
          selector: (state) => state.otherUser,
          builder: (context, otherUser) {
            if (otherUser != null && otherUser.isBlocked == 1) return const SizedBox();
            return Container(
              padding: EdgeInsets.fromLTRB(20, 20, 20, MediaQuery.of(context).viewInsets.bottom + 20),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10),
                  topRight: Radius.circular(10),
                ),
                boxShadow: [
                  BoxShadow(
                    offset: const Offset(11, 0),
                    blurRadius: 24,
                    spreadRadius: 0,
                    color: AppColors.black.withOpacity2(0.10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: AppTextFormField(
                          controller: messageController,
                          closeKeybordOnOutsideTap: false,
                          hintText: 'Write your message',
                          hintStyle: Theme.of(context).textTheme.bodyMedium,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide: BorderSide(
                              color: AppColors.white.withOpacity2(0.2),
                            ),
                          ),
                        ),
                      ),
                      const Gap(10),
                      InkWell(
                        onTap: () {
                          if (messageController.text.trim().isEmpty) return;
                          context.read<ChatDetailBloc>().add(ChatDetailSendMessage(
                                message: messageController.text.trim(),
                                loginUserName: context.read<AuthenticationBloc>().state.user.name ?? '',
                              ));
                          messageController.clear();
                        },
                        overlayColor: WidgetStateProperty.all(AppColors.transparent),
                        child: const AppSvgImage(AppAssets.sendIcon),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      }),
    );
  }

  Future<void> _deleteChat() async {
    final state = context.read<ChatDetailBloc>().state;
    if (state is ChatDetailLoaded) {
      if (state.chatId != null) {
        isStackLoading.value = true;
        final result = await getIt<IChatRepository>().deleteChat(
          chatId: state.chatId!,
        );
        result.fold(
          (l) {
            isStackLoading.value = false;
          },
          (r) async {
            if (widget.groupId != null) {
              await getIt<IChatRepository>().deleteChatFromFirebase(
                  loginId: context.read<AuthenticationBloc>().state.user.id,
                  otherUserId: widget.otherUserId,
                  advertisementId: widget.advertisementId);
            }
            Navigator.pop(context, {'isDeleted': true});

            isStackLoading.value = false;
          },
        );
      }
    }
  }

  Future<void> rejoinGroup() async {
    if (widget.groupId == null) return;
    isStackLoading.value = true;
    final result = await getIt<IChatRepository>().joinDefaultChatGrpup();
    result.fold(
      (l) {
        isStackLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        isStackLoading.value = false;
        final user = context.read<AuthenticationBloc>().state.user;
        context.read<AuthenticationBloc>().add(UpdateUser(
              user: user.copyWith(isJoinedDefaultChatGroup: 1),
            ));
        context.read<ChatDetailBloc>().add(const ChatDetailLoad());
        if (mounted) setState(() {});
      },
    );
  }

  Future<void> blockToggleUser() async {
    final user = context.read<ChatDetailBloc>().state.otherUser;
    if (user == null) return;
    isStackLoading.value = true;
    final result = await getIt<IUserRepository>().blockUser(userId: user.id);
    result.fold(
      (l) {
        isStackLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        isStackLoading.value = false;
        context
            .read<ChatDetailBloc>()
            .add(UpdateOtherUser(user: user.copyWith(isBlocked: user.isBlocked == 0 ? 1 : 0)));
      },
    );
  }
}
