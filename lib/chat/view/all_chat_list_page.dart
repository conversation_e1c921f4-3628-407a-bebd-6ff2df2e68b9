// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/bloc/chat_bloc.dart';
import 'package:leiuniverse/chat/widget/chat_filter_view.dart';
import 'package:leiuniverse/chat/widget/chat_view.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class ChatWrapperPage extends StatelessWidget {
  const ChatWrapperPage({
    super.key,
    this.advertisementId,
  });
  final int? advertisementId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ChatBloc>(param1: advertisementId)..add(const ChatLoad()),
      child: AllChatListPage(hideFilter: advertisementId != null),
    );
  }
}

class AllChatListPage extends StatefulWidget {
  const AllChatListPage({super.key, this.hideFilter = false});
  final bool hideFilter;

  @override
  State<AllChatListPage> createState() => _AllChatListPageState();
}

class _AllChatListPageState extends State<AllChatListPage> with PaginationMixin {
  final searchController = TextEditingController();
  final selectedFilter = ValueNotifier<ChatFilter>(AppConstants.chatFilters.first);

  @override
  void initState() {
    super.initState();
    initiatePagination();
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'All Chat',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder<ChatFilter>(
                    valueListenable: selectedFilter,
                    builder: (context, filter, _) {
                      return AppTextFormField(
                        controller: searchController,
                        hintText: 'Search',
                        fillColor: AppColors.background,
                        border: const OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.border),
                        ),
                        prefixIcon: const Padding(
                          padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                          child: AppSvgImage(
                            AppAssets.searchIcon,
                            color: AppColors.subText,
                          ),
                        ),
                        onChanged: (value) {
                          EasyDebounce.debounce(
                            'search_event',
                            const Duration(milliseconds: 500),
                            () {
                              final type = ChatType.fromString(filter.key);
                              context.read<ChatBloc>().add(ChatLoad(search: value, type: type));
                            },
                          );
                        },
                      );
                    }),
                if (!widget.hideFilter) ...[
                  const Gap(20),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: AppConstants.chatFilters
                        .map(
                          (e) => ValueListenableBuilder<ChatFilter?>(
                            valueListenable: selectedFilter,
                            builder: (context, filter, _) {
                              return ChatFilterView(
                                title: e.value ?? '',
                                selected: filter == e,
                                onTap: () {
                                  selectedFilter.value = e;
                                  final type = ChatType.fromString(e.key);
                                  context.read<ChatBloc>().add(ChatLoad(type: type));
                                },
                              );
                            },
                          ),
                        )
                        .toList(),
                  ),
                ],
                const Gap(16),
                state.map(
                  initial: (p0) => const SizedBox.shrink(),
                  loading: (p0) => const Flexible(
                    child: Center(
                      child: CustomProgressIndecator(
                        color: AppColors.primary,
                        size: 40,
                      ),
                    ),
                  ),
                  loaded: (state) {
                    return Flexible(
                      child: !state.isChatLoadMore && state.chats.isEmpty
                          ? Utility.noDataWidget(
                              context: context,
                              text: 'No Chat Found',
                            )
                          : ListView.separated(
                              padding: EdgeInsets.zero,
                              itemCount: state.chats.length,
                              controller: scrollPaginationController,
                              itemBuilder: (context, index) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ChatView(
                                      chat: state.chats[index],
                                      onDelete: () {
                                        if (state.chats[index].type == ChatType.group.name) {
                                          final user = context.read<AuthenticationBloc>().state.user;
                                          context
                                              .read<AuthenticationBloc>()
                                              .add(UpdateUser(user: user.copyWith(isJoinedDefaultChatGroup: 0)));
                                        } else {
                                          context.read<ChatBloc>().add(DeleteChat(chatId: state.chats[index].id));
                                        }
                                      },
                                    ),
                                    if (!state.isChatLoadMore &&
                                        index == state.chats.length - 1 &&
                                        !state.hasReachedMaxChat)
                                      const CustomProgressIndecator(
                                        color: AppColors.primary,
                                        size: 40,
                                      ),
                                  ],
                                );
                              },
                              separatorBuilder: (context, index) {
                                return const Padding(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                  child: Divider(
                                    color: AppColors.border,
                                    height: 1,
                                    thickness: 1,
                                  ),
                                );
                              },
                            ),
                    );
                  },
                  error: (message) => Flexible(child: Center(child: Text(message.failure?.message ?? ''))),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  @override
  void onReachedLast() {
    context.read<ChatBloc>().state.mapOrNull(
      loaded: (state) {
        if (!state.isLoading && !state.isChatLoadMore && !state.hasReachedMaxChat) {
          final type = ChatType.fromString(selectedFilter.value.key);
          context.read<ChatBloc>().add(ChatLoadMore(type: type, search: searchController.text.trim()));
        }
      },
    );
  }
}
