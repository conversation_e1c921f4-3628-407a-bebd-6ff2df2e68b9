part of 'chat_detail_bloc.dart';

abstract class ChatDetailEvent {
  const ChatDetailEvent();
}

class ChatDetailLoad extends ChatDetailEvent {
  const ChatDetailLoad();
}

class ChatDetailSendMessage extends ChatDetailEvent {
  final String message;
  final String loginUserName;

  const ChatDetailSendMessage({required this.message, required this.loginUserName});
}

class UpdateOtherUser extends ChatDetailEvent {
  final UserModel user;

  const UpdateOtherUser({required this.user});
}
