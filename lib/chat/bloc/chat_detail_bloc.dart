import 'package:bloc/bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/chat/repository/i_chat_repository.dart';
import 'package:leiuniverse/chat/response/chat_detail_response.dart';
import 'package:leiuniverse/chat/response/firebase_chat.dart';
import 'package:leiuniverse/chat/response/firebase_message_response.dart';
import 'package:leiuniverse/core/utils/app_typedefs/http_typedef.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/core/utils/tuples.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/login/response/user_response.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';

part 'chat_detail_event.dart';
part 'chat_detail_state.dart';

@injectable
class ChatDetailBloc extends Bloc<ChatDetailEvent, ChatDetailState> {
  ChatDetailBloc({
    required this.chatRepository,
    required this.authRepository,
    @factoryParam required Map<String, dynamic> userData,
    @factoryParam Map<String, dynamic>? extraData,
  }) : super(const ChatDetailInitial()) {
    loginUserId = userData['login_user_id'];
    otheruserId = userData['other_user_id'];
    advertisementId = extraData?['advertisement_id'];
    groupId = extraData?['group_id'];

    on<ChatDetailLoad>(_onLoad);
    on<ChatDetailSendMessage>(_onSendMessage);
    on<UpdateOtherUser>(_onUpdateOther);
  }

  final IChatRepository chatRepository;
  final IAuthRepository authRepository;
  late int loginUserId;
  late int otheruserId;
  late int? advertisementId;
  late int? groupId;

  Future<void> _onLoad(
    ChatDetailLoad event,
    Emitter<ChatDetailState> emit,
  ) async {
    emit(const ChatDetailLoading());

    final failOrGetApisResults = await Future.wait([
      authRepository.userDetail(userId: otheruserId.toString()),
      chatRepository.getChatMessages(
          loginId: loginUserId, otherUserId: otheruserId, groupId: groupId, advertisementId: advertisementId),
      chatRepository.getChatDetail(otherUserId: otheruserId, advertisementId: advertisementId),
    ]);

    final failOrGetUserDetail = failOrGetApisResults[0] as ApiNormalResult<UserResponse>;
    final failOrGetChatQuery = failOrGetApisResults[1] as Tuple2<FirebaseMessageResponse?, Query<FirebaseChat>?>;
    final failOrGetChatDetail = failOrGetApisResults[2] as ApiNormalResult<ChatDetailResponse>;

    failOrGetUserDetail.fold(
      (l) => emit(ChatDetailError(httpFailure: l)),
      (r) {
        if (r.data == null) {
          emit(ChatDetailError(httpFailure: HttpFailure.forbidden()));
        } else {
          emit(ChatDetailLoaded(
            otherUser: r.data!,
            chatDocument: failOrGetChatQuery.value1,
            getFirebaseMessageQuery: failOrGetChatQuery.value2,
            chatId: groupId ?? failOrGetChatDetail.fold((l) => null, (r) => r.data?.id),
            advertisement: failOrGetChatDetail.fold((l) => null, (r) => r.data?.advertisement),
          ));
        }
      },
    );
  }

  Future<void> _onSendMessage(
    ChatDetailSendMessage event,
    Emitter<ChatDetailState> emit,
  ) async {
    if (state is! ChatDetailLoaded) return;
    final loaded = state as ChatDetailLoaded;
    emit(loaded.copyWith(failure: null, isLoading: true));

    final result = await chatRepository.sendMessage(
      loginId: loginUserId,
      loginUserName: event.loginUserName,
      otherUserId: otheruserId,
      msg: event.message,
      advertisementId: advertisementId,
      groupId: groupId,
    );

    result.fold(
      (l) {
        emit(loaded.copyWith(failure: l));
      },
      (r) {
        emit(loaded.copyWith(failure: null, isLoading: false, chatId: r.data?.id));
      },
    );
  }

  Future<void> _onUpdateOther(
    UpdateOtherUser event,
    Emitter<ChatDetailState> emit,
  ) async {
    if (state is! ChatDetailLoaded) return;
    final loaded = state as ChatDetailLoaded;
    emit(loaded.copyWith(otherUser: event.user));
  }
}
