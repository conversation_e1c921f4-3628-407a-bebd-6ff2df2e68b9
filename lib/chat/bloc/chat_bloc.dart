import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/chat/model/chat_model.dart';
import 'package:leiuniverse/chat/repository/i_chat_repository.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';

part 'chat_event.dart';
part 'chat_state.dart';

@injectable
class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final IChatRepository chatRepository;
  int? advertisementId;
  ChatBloc({
    required this.chatRepository,
    @factoryParam int? advertisementIdFromParams,
  }) : super(const ChatInitial()) {
    advertisementId = advertisementIdFromParams;
    on<ChatLoad>(_onLoad);
    on<ChatLoadMore>(_onLoadMore);
    on<DeleteChat>(_onDeleteChat);
  }

  Future<void> _onLoad(ChatLoad event, Emitter<ChatState> emit) async {
    emit(const ChatLoading());
    final failOrSuccess = await chatRepository.chatList(
      search: event.search,
      type: advertisementId != null ? ChatType.advertisement : event.type,
      advertisementId: advertisementId,
    );
    emit(
      failOrSuccess.fold(
        (l) => ChatError(l),
        (r) => ChatLoaded(
          chats: r.data ?? [],
          hasReachedMaxChat: (r.data?.length ?? 0) < 10,
        ),
      ),
    );
  }

  Future<void> _onLoadMore(ChatLoadMore event, Emitter<ChatState> emit) async {
    if (state is! ChatLoaded) return;

    final currentState = state as ChatLoaded;

    emit(currentState.copyWith(isChatLoadMore: true));

    final failOrSuccess = await chatRepository.chatList(
      page: currentState.currentChatPage + 1,
      type: advertisementId != null ? ChatType.advertisement : event.type,
      advertisementId: advertisementId,
      search: event.search,
    );

    emit(
      failOrSuccess.fold(
        (l) => currentState.copyWith(isChatLoadMore: false),
        (r) => currentState.copyWith(
          chats: [...currentState.chats, ...r.data ?? []],
          currentChatPage: currentState.currentChatPage + 1,
          isChatLoadMore: false,
          hasReachedMaxChat: (r.data?.length ?? 0) < 10,
        ),
      ),
    );
  }

  Future<void> _onDeleteChat(DeleteChat event, Emitter<ChatState> emit) async {
    if (state is! ChatLoaded) return;

    final currentState = state as ChatLoaded;

    emit(currentState.copyWith(chats: currentState.chats.where((element) => element.id != event.chatId).toList()));
  }
}
