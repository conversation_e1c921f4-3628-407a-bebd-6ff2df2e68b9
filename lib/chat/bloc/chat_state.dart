part of 'chat_bloc.dart';

abstract class ChatState extends Equatable {
  const ChatState();

  R when<R>({
    required R Function() initial,
    required R Function() loading,
    required R Function(ChatLoaded) loaded,
    required R Function(ChatError) error,
  }) {
    if (this is ChatInitial) return initial();
    if (this is ChatLoading) return loading();
    if (this is ChatLoaded) return loaded(this as ChatLoaded);
    if (this is ChatError) return error(this as ChatError);
    throw Exception('Unrecognized ChatState: $runtimeType');
  }

  R? whenOrNull<R>({
    R Function()? initial,
    R Function()? loading,
    R Function(ChatLoaded)? loaded,
    R Function(ChatError)? error,
  }) {
    if (this is ChatInitial) return initial?.call();
    if (this is ChatLoading) return loading?.call();
    if (this is ChatLoaded) return loaded?.call(this as ChatLoaded);
    if (this is ChatError) return error?.call(this as ChatError);
    return null;
  }

  R map<R>({
    required R Function(ChatInitial) initial,
    required R Function(ChatLoading) loading,
    required R Function(ChatLoaded) loaded,
    required R Function(ChatError) error,
  }) {
    if (this is ChatInitial) return initial(this as ChatInitial);
    if (this is ChatLoading) return loading(this as ChatLoading);
    if (this is ChatLoaded) return loaded(this as ChatLoaded);
    if (this is ChatError) return error(this as ChatError);
    throw Exception('Unrecognized ChatState: $runtimeType');
  }

  R? mapOrNull<R>({
    R Function(ChatInitial)? initial,
    R Function(ChatLoading)? loading,
    R Function(ChatLoaded)? loaded,
    R Function(ChatError)? error,
  }) {
    if (this is ChatInitial) return initial?.call(this as ChatInitial);
    if (this is ChatLoading) return loading?.call(this as ChatLoading);
    if (this is ChatLoaded) return loaded?.call(this as ChatLoaded);
    if (this is ChatError) return error?.call(this as ChatError);
    return null;
  }

  @override
  List<Object?> get props => [];
}

class ChatInitial extends ChatState {
  const ChatInitial();

  @override
  List<Object?> get props => [];
}

class ChatLoading extends ChatState {
  const ChatLoading();

  @override
  List<Object?> get props => [];
}

class ChatLoaded extends ChatState {
  final List<ChatModel> chats;
  final bool isLoading;
  final HttpFailure? failure;
  final bool isChatLoadMore;
  final int currentChatPage;
  final bool hasReachedMaxChat;

  const ChatLoaded({
    this.chats = const <ChatModel>[],
    this.isLoading = false,
    this.failure,
    this.isChatLoadMore = false,
    this.currentChatPage = 1,
    this.hasReachedMaxChat = false,
  });

  ChatLoaded copyWith({
    List<ChatModel>? chats,
    bool? isLoading,
    HttpFailure? failure,
    bool? isChatLoadMore,
    int? currentChatPage,
    bool? hasReachedMaxChat,
  }) {
    return ChatLoaded(
      chats: chats ?? this.chats,
      isLoading: isLoading ?? this.isLoading,
      failure: failure ?? this.failure,
      isChatLoadMore: isChatLoadMore ?? this.isChatLoadMore,
      currentChatPage: currentChatPage ?? this.currentChatPage,
      hasReachedMaxChat: hasReachedMaxChat ?? this.hasReachedMaxChat,
    );
  }

  @override
  List<Object?> get props => [
        chats,
        isLoading,
        failure,
        isChatLoadMore,
        currentChatPage,
        hasReachedMaxChat,
      ];
}

class ChatError extends ChatState {
  final HttpFailure? failure;

  const ChatError(this.failure);

  @override
  List<Object?> get props => [failure];
}
