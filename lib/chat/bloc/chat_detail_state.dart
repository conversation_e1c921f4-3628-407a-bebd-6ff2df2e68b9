part of 'chat_detail_bloc.dart';

abstract class ChatDetailState extends Equatable {
  const ChatDetailState();

  R when<R>({
    required R Function() initial,
    required R Function() loading,
    required R Function(ChatDetailLoaded) loaded,
    required R Function(ChatDetailError) error,
  }) {
    if (this is ChatDetailInitial) return initial();
    if (this is ChatDetailLoading) return loading();
    if (this is ChatDetailLoaded) return loaded(this as ChatDetailLoaded);
    if (this is ChatDetailError) return error(this as ChatDetailError);
    throw Exception('Unrecognized ChatDetailState: $runtimeType');
  }

  R? whenOrNull<R>({
    R Function()? initial,
    R Function()? loading,
    R Function(ChatDetailLoaded)? loaded,
    R Function(ChatDetailError)? error,
  }) {
    if (this is ChatDetailInitial) return initial?.call();
    if (this is ChatDetailLoading) return loading?.call();
    if (this is ChatDetailLoaded) return loaded?.call(this as ChatDetailLoaded);
    if (this is ChatDetailError) return error?.call(this as ChatDetailError);
    return null;
  }

  R map<R>({
    required R Function(ChatDetailInitial) initial,
    required R Function(ChatDetailLoading) loading,
    required R Function(ChatDetailLoaded) loaded,
    required R Function(ChatDetailError) error,
  }) {
    if (this is ChatDetailInitial) return initial(this as ChatDetailInitial);
    if (this is ChatDetailLoading) return loading(this as ChatDetailLoading);
    if (this is ChatDetailLoaded) return loaded(this as ChatDetailLoaded);
    if (this is ChatDetailError) return error(this as ChatDetailError);
    throw Exception('Unrecognized ChatDetailState: $runtimeType');
  }

  R? mapOrNull<R>({
    R Function(ChatDetailInitial)? initial,
    R Function(ChatDetailLoading)? loading,
    R Function(ChatDetailLoaded)? loaded,
    R Function(ChatDetailError)? error,
  }) {
    if (this is ChatDetailInitial) return initial?.call(this as ChatDetailInitial);
    if (this is ChatDetailLoading) return loading?.call(this as ChatDetailLoading);
    if (this is ChatDetailLoaded) return loaded?.call(this as ChatDetailLoaded);
    if (this is ChatDetailError) return error?.call(this as ChatDetailError);
    return null;
  }

  @override
  List<Object?> get props => [];
}

// Initial State
class ChatDetailInitial extends ChatDetailState {
  const ChatDetailInitial();

  @override
  List<Object?> get props => [];
}

// Loading State
class ChatDetailLoading extends ChatDetailState {
  const ChatDetailLoading();

  @override
  List<Object?> get props => [];
}

// Loaded State
class ChatDetailLoaded extends ChatDetailState {
  final UserModel otherUser;
  final Query<FirebaseChat>? getFirebaseMessageQuery;
  final HttpFailure? failure;
  final FirebaseMessageResponse? chatDocument;
  final bool isLoading;
  final bool isStackLoading;
  final int? chatId;
  final Advertisemnet? advertisement;

  const ChatDetailLoaded({
    required this.otherUser,
    this.getFirebaseMessageQuery,
    this.failure,
    this.chatDocument,
    this.isLoading = false,
    this.isStackLoading = false,
    this.chatId,
    this.advertisement,
  });

  ChatDetailLoaded copyWith({
    UserModel? otherUser,
    Query<FirebaseChat>? getFirebaseMessageQuery,
    HttpFailure? failure,
    FirebaseMessageResponse? chatDocument,
    bool? isLoading,
    bool? isStackLoading,
    int? chatId,
    Advertisemnet? advertisement,
  }) {
    return ChatDetailLoaded(
      otherUser: otherUser ?? this.otherUser,
      getFirebaseMessageQuery: getFirebaseMessageQuery ?? this.getFirebaseMessageQuery,
      failure: failure ?? this.failure,
      chatDocument: chatDocument ?? this.chatDocument,
      isLoading: isLoading ?? this.isLoading,
      isStackLoading: isStackLoading ?? this.isStackLoading,
      chatId: chatId ?? this.chatId,
      advertisement: advertisement ?? this.advertisement,
    );
  }

  @override
  List<Object?> get props => [
        otherUser,
        getFirebaseMessageQuery,
        failure,
        chatDocument,
        isLoading,
        isStackLoading,
        chatId,
        advertisement,
      ];
}

// Error State
class ChatDetailError extends ChatDetailState {
  final HttpFailure? httpFailure;

  const ChatDetailError({this.httpFailure});

  @override
  List<Object?> get props => [httpFailure];
}

extension ChatDetailStateExtension on ChatDetailState {
  UserModel? get otherUser => mapOrNull(loaded: (value) => value.otherUser);
  Advertisemnet? get advertisement => mapOrNull<Advertisemnet?>(loaded: (value) => value.advertisement);
}
