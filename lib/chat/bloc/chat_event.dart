part of 'chat_bloc.dart';

abstract class ChatEvent {
  const ChatEvent();
}

class ChatLoad extends ChatEvent {
  final String? search;
  final ChatType? type;

  const ChatLoad({this.search, this.type});
}

class ChatLoadMore extends ChatEvent {
  final ChatType? type;
  final String? search;
  const ChatLoadMore({this.type, this.search});
}

class DeleteChat extends ChatEvent {
  final int? chatId;

  const DeleteChat({this.chatId});
}
