import 'package:equatable/equatable.dart';

class CreateChatDataModel extends Equatable {
  const CreateChatDataModel({
    this.id,
    this.type,
    this.groupChatId,
    this.advertisementId,
    this.lastMessage,
    this.senderId,
    this.createdAt,
    this.updatedAt,
  });

  final int? id;
  final String? type;
  final int? groupChatId;
  final int? advertisementId;
  final String? lastMessage;
  final int? senderId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory CreateChatDataModel.fromJson(Map<String, dynamic> json) {
    return CreateChatDataModel(
      id: json["id"],
      type: json["type"],
      groupChatId: json["group_chat_id"],
      advertisementId: json["advertisement_id"],
      lastMessage: json["last_message"],
      senderId: json["sender_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "group_chat_id": groupChatId,
        "advertisement_id": advertisementId,
        "last_message": lastMessage,
        "sender_id": senderId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  List<Object?> get props => [
        id,
        type,
        groupChatId,
        advertisementId,
        lastMessage,
        senderId,
        createdAt,
        updatedAt,
      ];
}
