import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/chat/model/group_chat_model.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';

class ChatModel extends Equatable {
  const ChatModel({
    this.id,
    this.type,
    this.groupChatId,
    this.advertisementId,
    this.lastMessage,
    this.senderId,
    this.createdAt,
    this.updatedAt,
    this.groupChat,
    this.advertisement,
    this.sender,
    this.other,
  });

  final int? id;
  final String? type;
  final int? groupChatId;
  final int? advertisementId;
  final String? lastMessage;
  final int? senderId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final GroupChatModel? groupChat;
  final Advertisemnet? advertisement;
  final UserModel? sender;
  final UserModel? other;

  ChatModel copyWith({
    int? id,
    String? type,
    int? groupChatId,
    int? advertisementId,
    String? lastMessage,
    int? senderId,
    DateTime? createdAt,
    DateTime? updatedAt,
    GroupChatModel? groupChat,
    Advertisemnet? advertisement,
    UserModel? sender,
    UserModel? other,
  }) {
    return ChatModel(
      id: id ?? this.id,
      type: type ?? this.type,
      groupChatId: groupChatId ?? this.groupChatId,
      advertisementId: advertisementId ?? this.advertisementId,
      lastMessage: lastMessage ?? this.lastMessage,
      senderId: senderId ?? this.senderId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      groupChat: groupChat ?? this.groupChat,
      advertisement: advertisement ?? this.advertisement,
      sender: sender ?? this.sender,
      other: other ?? this.other,
    );
  }

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json["id"],
      type: json["type"],
      groupChatId: json["group_chat_id"],
      advertisementId: json["advertisement_id"],
      lastMessage: json["last_message"],
      senderId: json["sender_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      groupChat: json["group_chat"] == null ? null : GroupChatModel.fromJson(json["group_chat"]),
      advertisement: json["advertisement"] == null ? null : Advertisemnet.fromJson(json["advertisement"]),
      sender: json["sender"] == null ? null : UserModel.fromJson(json["sender"]),
      other: json["other_user"] == null ? null : UserModel.fromJson(json["other_user"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "group_chat_id": groupChatId,
        "advertisement_id": advertisementId,
        "last_message": lastMessage,
        "sender_id": senderId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "group_chat": groupChat?.toJson(),
        "advertisement": advertisement?.toJson(),
        "sender": sender?.toJson(),
      };

  @override
  String toString() {
    return "$id, $type, $groupChatId, $advertisementId, $lastMessage, $senderId, $createdAt, $updatedAt, $groupChat, $advertisement, $sender, ";
  }

  @override
  List<Object?> get props => [
        id,
        type,
        groupChatId,
        advertisementId,
        lastMessage,
        senderId,
        createdAt,
        updatedAt,
        groupChat,
        advertisement,
        sender,
      ];

  String get formattedTime {
    final date = createdAt ?? DateTime.now();
    return DateFormat('jm').format(date.toLocal()).toLowerCase();
  }
}
