import 'package:equatable/equatable.dart';

class GroupChatModel extends Equatable {
  const GroupChatModel({
    this.id,
    this.type,
    this.name,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
  });

  final int? id;
  final String? type;
  final String? name;
  final String? imageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  GroupChatModel copyWith({
    int? id,
    String? type,
    String? name,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GroupChatModel(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory GroupChatModel.fromJson(Map<String, dynamic> json) {
    return GroupChatModel(
      id: json["id"],
      type: json["type"],
      name: json["name"],
      imageUrl: json["image_url"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "name": name,
        "image_url": imageUrl,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  String toString() {
    return "$id, $type, $name, $imageUrl, $createdAt, $updatedAt, ";
  }

  @override
  List<Object?> get props => [
        id,
        type,
        name,
        imageUrl,
        createdAt,
        updatedAt,
      ];
}
