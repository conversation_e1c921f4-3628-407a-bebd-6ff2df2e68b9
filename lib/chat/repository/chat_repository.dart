part of 'i_chat_repository.dart';

@Injectable(as: IChatRepository)
class ChatRepository extends IChatRepository {
  ChatRepository(super.client, super.firestore);

  @override
  ApiResult<ChatListResponse> chatList({
    int page = 1,
    int perPage = 10,
    String? search,
    ChatType? type,
    int? advertisementId,
  }) async {
    final response = await client.get(
      url: AppStrings.chats,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
        if (type != null) 'type': type.name,
        if (advertisementId != null) 'advertisement_id': advertisementId.toString(),
      },
    );
    return response.parseResponse(ChatListResponse.fromJson);
  }

  @override
  Future<Tuple2<FirebaseMessageResponse?, Query<FirebaseChat>?>> getChatMessages({
    required int loginId,
    required int otherUserId,
    int? groupId,
    int? advertisementId,
  }) async {
    final getChatDocument = await _getChatDocument(
        loginId: loginId, otherUserId: otherUserId, groupId: groupId, advertisementId: advertisementId);

    final chatDocument = getChatDocument.getRightFolded();

    if (chatDocument == null) {
      return tuple2(null, null);
    }

    return tuple2(
      chatDocument,
      firestore.chats().doc(chatDocument.id).messages().orderBy('timestamp', descending: true).withConverter(
            fromFirestore: (snapshot, options) => FirebaseChat.fromFirestore(snapshot),
            toFirestore: (value, options) => value.toJson(),
          ),
    );
  }

  ApiResult<FirebaseMessageResponse> _getChatDocument({
    required int loginId,
    required int otherUserId,
    int? groupId,
    int? advertisementId,
  }) async {
    try {
      final chatDocument = firestore.chats().where(
            'chat_id',
            isEqualTo: groupId != null
                ? 'GROUP_$groupId'
                : generateUniqueChatId(loginId: loginId, otherUserId: otherUserId, advertisementId: advertisementId),
          );
      final getChatDocument = await chatDocument.get();
      if (getChatDocument.docs.isNotEmpty) {
        return right(FirebaseMessageResponse.fromFirestore(getChatDocument.docs.first));
      }
      final newChatDocument = await firestore.chats().add(<String, dynamic>{
        'chat_id': groupId != null
            ? 'GROUP_$groupId'
            : generateUniqueChatId(loginId: loginId, otherUserId: otherUserId, advertisementId: advertisementId),
        'updated_at': FieldValue.serverTimestamp(),
        'created_at': FieldValue.serverTimestamp(),
        'users': FieldValue.arrayUnion(
          <dynamic>[
            <String, dynamic>{
              'user_id': loginId,
              'badge': 0,
            },
            <String, dynamic>{
              'user_id': otherUserId,
              'badge': 0,
            },
          ],
        ),
      });

      return right(FirebaseMessageResponse.fromFirestore(await newChatDocument.get()));
    } on FirebaseException catch (e, s) {
      return left(HttpFailure(message: e.message, stackTrace: s));
    } catch (e, s) {
      return left(HttpFailure(message: e.toString(), stackTrace: s));
    }
  }

  @override
  String generateUniqueChatId({required int loginId, required int otherUserId, int? advertisementId}) {
    if (loginId.hashCode <= otherUserId.hashCode) {
      return '$loginId-$otherUserId-$advertisementId';
    }

    return '$otherUserId-$loginId-$advertisementId';
  }

  @override
  ApiResult<CreateChatResponse> sendMessage({
    required int loginId,
    required String loginUserName,
    required int otherUserId,
    String? msg,
    int? advertisementId,
    int? groupId,
  }) async {
    try {
      final getChatDocument = await _getChatDocument(
          loginId: loginId, otherUserId: otherUserId, groupId: groupId, advertisementId: advertisementId);

      final chatDocument = getChatDocument.getRightFolded();

      if (chatDocument == null) {
        throw HttpFailure.forbidden();
      }
      final message = <String, dynamic>{
        'sender_id': loginId,
        'sender_name': loginUserName,
        'timestamp': FieldValue.serverTimestamp(),
        if (msg != null) 'message': msg,
      };

      final isGroup = groupId != null;
      final users = isGroup
          ? chatDocument.users
          : chatDocument.users.map((e) {
              if (e.userId == loginId) {
                return e.copyWith(badge: 0);
              }
              if (e.userId == otherUserId) {
                return e.copyWith(badge: e.badge + 1);
              }
              return e;
            }).toList();
      // log('usersusers ${users.map((e) => e.badge)}');

      await Future.wait(
        [
          firestore.chats().doc(chatDocument.id).update(<String, dynamic>{
            'updated_at': FieldValue.serverTimestamp(),
            'last_message': message,
            if (!isGroup) 'users': users.map((e) => e.toJson()).toList(),
          }),
          firestore.chats().doc(chatDocument.id).messages().doc().set(message),
        ],
      );

      return await sendChatApi(otherUserId: otherUserId, msg: msg, advertisementId: advertisementId, groupId: groupId);
    } on FirebaseException catch (e, s) {
      return left(HttpFailure(message: e.message, stackTrace: s));
    } catch (e, s) {
      return left(HttpFailure(message: e.toString(), stackTrace: s));
    }
  }

  @override
  ApiResult<CreateChatResponse> sendChatApi(
      {required int otherUserId, required String? msg, int? advertisementId, int? groupId}) async {
    final response = await client.post(
      url: AppStrings.sendChat,
      requests: <String, dynamic>{
        if (groupId == null) 'receiver_id': '$otherUserId',
        if (msg != null) 'message': msg,
        if (advertisementId != null) 'advertisement_id': advertisementId.toString(),
        if (groupId != null) 'group_chat_id': groupId.toString(),
      },
    );

    return response.fold(
      left,
      (r) => Either.tryCatch(() => CreateChatResponse.fromJson(r), (o, s) => HttpFailure.parsing(o.toString(), 500, s)),
    );
  }

  @override
  Future<Stream<FirebaseMessageResponse?>?> getChatBadge({
    required int loginId,
    required int otherUserId,
    int? groupId,
    int? advertisementId,
  }) async {
    if (groupId != null) {
      return Future.value(groupBadgeWithLastMessage(loginId: loginId, groupId: groupId));
    } else {
      final chatDocument = firestore.chats().where(
            'chat_id',
            isEqualTo: groupId != null
                ? 'GROUP_$groupId'
                : generateUniqueChatId(loginId: loginId, otherUserId: otherUserId, advertisementId: advertisementId),
          );
      final getChatDocument = await chatDocument.get();

      if (getChatDocument.docs.isEmpty) {
        return null;
      }

      return firestore
          .chats()
          .doc(getChatDocument.docs.first.id)
          .snapshots()
          .map(FirebaseMessageResponse.fromFirestore);
    }
  }

  @override
  Stream<FirebaseMessageResponse?> groupBadgeWithLastMessage({
    required int loginId,
    required int groupId,
  }) async* {
    final metaDocRef = firestore.userGroupMeta().doc('${loginId}_$groupId');

    final chatSnapshot = await firestore.chats().where('chat_id', isEqualTo: 'GROUP_$groupId').limit(1).get();

    if (chatSnapshot.docs.isEmpty) {
      yield null;
      return;
    }

    final chatDoc = chatSnapshot.docs.first;
    final chatId = chatDoc.id;
    final messagesRef = firestore.chats().doc(chatId).collection('messages');

    yield* Rx.combineLatest2<DocumentSnapshot<Map<String, dynamic>>, QuerySnapshot<Map<String, dynamic>>,
        FirebaseMessageResponse?>(
      metaDocRef.snapshots(),
      messagesRef.orderBy('timestamp', descending: true).limit(50).snapshots(),
      (metaSnap, msgSnap) {
        final lastRead = metaSnap.exists && metaSnap.data() != null
            ? metaSnap.data()!['last_read_at'] as Timestamp? ?? Timestamp(0, 0)
            : Timestamp(0, 0);

        final messages = msgSnap.docs;

        final unreadMessages = messages.where((doc) {
          final ts = doc.data()['timestamp'] as Timestamp?;
          return ts != null && ts.toDate().isAfter(lastRead.toDate());
        }).length;

        if (messages.isEmpty) return null;

        final last = messages.first.data();

        return FirebaseMessageResponse(
          id: chatId,
          chatId: 'GROUP_$groupId',
          lastMessage: FirebaseChat.fromJson(last),
          badge: unreadMessages,
        );
      },
    );
  }

  @override
  Future<String?> readChatBadge({
    required int loginId,
    required int otherUserId,
    FirebaseMessageResponse? chatDocument,
    int? groupId,
    int? advertisementId,
  }) async {
    try {
      if (loginId == 0) return null;

      if (groupId != null) {
        // Group chat: only update user's own last read time
        await firestore.userGroupMeta().doc('${loginId}_$groupId').set({
          'user_id': loginId,
          'group_id': groupId,
          'last_read_at': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        return 'Success';
      }

      final getChatDocument = await _getChatDocument(
          loginId: loginId, otherUserId: otherUserId, groupId: groupId, advertisementId: advertisementId);

      final chatDocument = getChatDocument.getRightFolded();

      if (chatDocument == null) {
        throw HttpFailure.forbidden();
      }

      final users = chatDocument.users.map((e) {
        if (e.userId == loginId) {
          return e.copyWith(badge: 0);
        }

        return e;
      }).toList();

      await firestore.chats().doc(chatDocument.id).update(<String, dynamic>{
        'users': users.map((e) => e.toJson()).toList(),
      });
      return 'Success';
    } catch (e) {
      return e.toString();
    }
  }

  @override
  ApiResult<CommonResponse> deleteChat({
    required int chatId,
  }) async {
    final response = await client.delete(
      url: AppStrings.deleteChats(chatId.toString()),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  Future<bool> deleteChatFromFirebase({
    required int loginId,
    required int otherUserId,
    int? advertisementId,
  }) async {
    final chatId = generateUniqueChatId(loginId: loginId, otherUserId: otherUserId, advertisementId: advertisementId);
    try {
      await firestore.chats().doc(chatId).delete();
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  ApiResult<CommonResponse> joinDefaultChatGrpup() async {
    final response = await client.post(
      url: AppStrings.joinDefaultChatGroup,
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<ChatDetailResponse> getChatDetail({required int otherUserId, int? advertisementId}) async {
    final response = await client.get(url: AppStrings.chatDetail, params: {
      'user_id': otherUserId.toString(),
      if (advertisementId != null) 'advertisement_id': advertisementId.toString(),
    });
    return response.parseResponse(ChatDetailResponse.fromJson);
  }
}
