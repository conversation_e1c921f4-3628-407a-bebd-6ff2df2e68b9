import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/chat/response/chat_detail_response.dart';
import 'package:leiuniverse/chat/response/chat_list_response.dart';
import 'package:leiuniverse/chat/response/create_chat_response.dart';
import 'package:leiuniverse/chat/response/firebase_chat.dart';
import 'package:leiuniverse/chat/response/firebase_message_response.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/firestore_helper.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/core/utils/tuples.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:rxdart/rxdart.dart';

part 'chat_repository.dart';

abstract class IChatRepository {
  IChatRepository(this.client, this.firestore);
  final Client client;
  final FirebaseFirestore firestore;

  ApiResult<ChatListResponse> chatList({
    int page = 1,
    int perPage = 10,
    String? search,
    ChatType? type,
    int? advertisementId,
  });

  Future<Tuple2<FirebaseMessageResponse?, Query<FirebaseChat>?>> getChatMessages({
    required int loginId,
    required int otherUserId,
    int? groupId,
    int? advertisementId,
  });

  String generateUniqueChatId({required int loginId, required int otherUserId, int? advertisementId});

  ApiResult<CreateChatResponse> sendMessage({
    required int loginId,
    required String loginUserName,
    required int otherUserId,
    String? msg,
    int? advertisementId,
    int? groupId,
  });

  ApiResult<CreateChatResponse> sendChatApi({
    required int otherUserId,
    required String? msg,
    int? advertisementId,
    int? groupId,
  });

  Future<Stream<FirebaseMessageResponse?>?> getChatBadge({
    required int loginId,
    required int otherUserId,
    int? groupId,
    int? advertisementId,
  });

  Stream<FirebaseMessageResponse?> groupBadgeWithLastMessage({
    required int loginId,
    required int groupId,
  });

  Future<String?> readChatBadge({
    required int loginId,
    required int otherUserId,
    FirebaseMessageResponse? chatDocument,
    int? groupId,
    int? advertisementId,
  });

  ApiResult<CommonResponse> deleteChat({
    required int chatId,
  });

  Future<bool> deleteChatFromFirebase({
    required int loginId,
    required int otherUserId,
    int? advertisementId,
  });

  ApiResult<CommonResponse> joinDefaultChatGrpup();

  ApiResult<ChatDetailResponse> getChatDetail({required int otherUserId, int? advertisementId});
}
