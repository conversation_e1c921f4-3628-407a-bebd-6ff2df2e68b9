// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';

class ChatBadgeView extends StatelessWidget {
  const ChatBadgeView({
    super.key,
    required this.count,
  });
  final int count;

  @override
  Widget build(BuildContext context) {
    if (count == 0) return const SizedBox.shrink();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      height: 24,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        color: AppColors.primary,
      ),
      child: Center(
        child: Text(
          count > 9 ? '9+' : count.toString(),
          style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.white, height: 1),
        ),
      ),
    );
  }
}
