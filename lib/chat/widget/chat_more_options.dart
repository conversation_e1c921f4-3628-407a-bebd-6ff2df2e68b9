import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ChatMoreOptions extends StatelessWidget {
  const ChatMoreOptions({
    super.key,
    this.onBlockTap,
    this.onReportTap,
    this.onDeleteChatTap,
    this.isGroup = false,
    this.showDelete = true,
  });
  final VoidCallback? onBlockTap;
  final VoidCallback? onReportTap;
  final VoidCallback? onDeleteChatTap;
  final bool isGroup;
  final bool showDelete;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      color: AppColors.white,
      elevation: 6,
      position: PopupMenuPosition.under,
      onSelected: (value) {
        if (value == 0) {
          onBlockTap?.call();
        } else if (value == 1) {
          onReportTap?.call();
        } else if (value == 2) {
          onDeleteChatTap?.call();
        }
      },
      itemBuilder: (context) {
        return [
          if (!isGroup) ...[
            PopupMenuItem(
              value: 0,
              child: Row(
                children: [
                  const AppSvgImage(AppAssets.blockIcon),
                  const Gap(11),
                  Text(
                    'Block',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            PopupMenuItem(
              value: 1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const AppSvgImage(AppAssets.reportIcon),
                  const Gap(11),
                  Text(
                    'Report',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
          if (showDelete)
            PopupMenuItem(
              value: 2,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AppSvgImage(
                    isGroup ? AppAssets.signOutIcon : AppAssets.moreDeleteIcon,
                    color: AppColors.primary,
                  ),
                  const Gap(11),
                  Text(
                    isGroup ? 'Exit Group' : 'Delete Chat',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            )
        ];
      },
      child: const Padding(
        padding: EdgeInsets.all(14),
        child: AppSvgImage(
          AppAssets.menuDotsIcon,
        ),
      ),
    );
  }
}
