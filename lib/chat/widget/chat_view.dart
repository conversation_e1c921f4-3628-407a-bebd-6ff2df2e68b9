import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/model/chat_model.dart';
import 'package:leiuniverse/chat/repository/i_chat_repository.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/chat/widget/chat_badge_view.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class ChatView extends StatelessWidget {
  const ChatView({super.key, this.chat, this.onDelete});
  final ChatModel? chat;
  final void Function()? onDelete;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChatDetailWrapperPage(
              userId: chat?.type == AppConstants.group ? chat?.groupChat?.id ?? 0 : chat?.other?.id ?? 0,
              advertisementId: chat?.advertisementId,
              groupId: chat?.type == AppConstants.group ? chat?.groupChat?.id : null,
              groupChatModel: chat?.type == AppConstants.group ? chat?.groupChat : null,
            ),
          ),
        );

        if (result != null && result['isDeleted']) {
          onDelete?.call();
        }
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            chat?.type == ChatType.advertisement.toString() && chat?.advertisement != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(54 / 2),
                    child: Banner(
                      location: BannerLocation.topStart,
                      color: AppColors.primary.withOpacity2(0.7),
                      message: 'Adv',
                      textStyle: const TextStyle(color: AppColors.white, fontSize: 12, fontWeight: FontWeight.w600),
                      child: Utility.imageLoader(
                        url: chat?.advertisement?.images.firstOrNull?.imageUrl ?? '',
                        placeholder: AppAssets.placeholderImage,
                        height: 54,
                        width: 54,
                        fit: BoxFit.cover,
                        shape: BoxShape.circle,
                        isShapeCircular: true,
                      ),
                    ),
                  )
                : Utility.imageLoader(
                    url: chat?.type == AppConstants.group
                        ? chat?.groupChat?.imageUrl ?? ''
                        : chat?.other?.profileImage ?? '',
                    placeholder: chat?.type == AppConstants.group ? AppAssets.logo : AppAssets.placeholderImage,
                    height: 54,
                    width: 54,
                    fit: BoxFit.cover,
                    shape: BoxShape.circle,
                    isShapeCircular: true,
                  ),
            const Gap(14),
            BlocSelector<AuthenticationBloc, AuthenticationState, UserModel?>(
                selector: (state) => state.user,
                builder: (context, loginUser) {
                  return FutureBuilder(
                      future: getIt<IChatRepository>().getChatBadge(
                          loginId: loginUser?.id ?? 0,
                          otherUserId: chat?.other?.id ?? 0,
                          groupId: chat?.groupChat?.id,
                          advertisementId: chat?.advertisementId),
                      builder: (_, stream) {
                        return StreamBuilder(
                            stream: stream.data,
                            builder: (_, snapshot) {
                              final badgeCount = chat?.groupChat?.id != null
                                  ? snapshot.data?.badge
                                  : snapshot.data?.users
                                      .firstWhereOrNull((element) => element.userId == loginUser?.id)
                                      ?.badge;
                              return Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                chat?.type == AppConstants.group
                                                    ? chat?.groupChat?.name ?? ''
                                                    : chat?.advertisement?.title ?? chat?.other?.name ?? '',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: Theme.of(context).textTheme.titleMedium?.copyWith(height: 1.2),
                                              ),
                                              if (chat?.type == ChatType.advertisement.toString())
                                                Text(
                                                  '@${chat?.other?.name ?? ''}',
                                                  maxLines: 1,
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.copyWith(fontSize: 10, color: AppColors.primary, height: 1),
                                                ),
                                            ],
                                          ),
                                        ),
                                        const Gap(14),
                                        Text(
                                          snapshot.data?.lastMessage?.formattedTime ?? '',
                                          style: Theme.of(context)
                                              .textTheme
                                              .labelLarge
                                              ?.copyWith(color: AppColors.subText),
                                        )
                                      ],
                                    ),
                                    if (snapshot.data?.lastMessage?.message != null &&
                                        snapshot.data?.lastMessage?.message != '') ...[
                                      const Gap(6),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Text(
                                              snapshot.data?.lastMessage?.message ?? '',
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(color: AppColors.subText),
                                            ),
                                          ),
                                          const Gap(14),
                                          ChatBadgeView(
                                            count: badgeCount ?? 0,
                                          )
                                        ],
                                      ),
                                    ],
                                  ],
                                ),
                              );
                            });
                      });
                }),
          ],
        ),
      ),
    );
  }
}
