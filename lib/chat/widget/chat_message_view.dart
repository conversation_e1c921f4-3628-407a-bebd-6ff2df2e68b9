// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/chat/response/firebase_chat.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';

class ChatMessageView extends StatelessWidget {
  const ChatMessageView({
    super.key,
    required this.chat,
    required this.isOtherUser,
    required this.chatType,
  });
  final FirebaseChat chat;
  final bool isOtherUser;
  final String chatType;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: isOtherUser ? Alignment.centerLeft : Alignment.centerRight,
      child: Container(
        padding: const EdgeInsets.all(10),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width / 1.5,
        ),
        decoration: BoxDecoration(
            color: isOtherUser ? AppColors.primary : AppColors.background,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(12),
              topRight: const Radius.circular(12),
              bottomLeft: isOtherUser ? Radius.zero : const Radius.circular(12),
              bottomRight: isOtherUser ? const Radius.circular(12) : Radius.zero,
            )),
        child: Column(
          crossAxisAlignment: isOtherUser ? CrossAxisAlignment.start : CrossAxisAlignment.end,
          children: [
            if (isOtherUser && chatType == AppConstants.group) ...[
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ProfileDetailPage(userId: chat.senderId),
                    ),
                  );
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: Text(
                  '~ ${chat.senderName}',
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall
                      ?.copyWith(color: AppColors.white, fontWeight: FontWeight.w900, fontStyle: FontStyle.italic),
                ),
              ),
              const Gap(4),
            ],
            Text(
              chat.message ?? '',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: isOtherUser ? AppColors.white : null),
            ),
            const Gap(4),
            Text(
              chat.formattedTime,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    letterSpacing: 1,
                    color: isOtherUser ? AppColors.white.withOpacity2(0.88) : AppColors.subText,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
