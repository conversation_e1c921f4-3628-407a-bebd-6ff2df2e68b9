import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';

class ChatFilterView extends StatelessWidget {
  const ChatFilterView({super.key, this.onTap, this.selected = false, required this.title});
  final void Function()? onTap;
  final bool selected;
  final String title;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(6),
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 10),
        height: 30,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: selected ? AppColors.turquoiseGreen : AppColors.offGreen,
        ),
        child: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: selected ? AppColors.text : AppColors.subText),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
