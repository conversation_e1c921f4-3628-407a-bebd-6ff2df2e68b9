import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/common_button.dart';

class DeleteChatDialog extends StatelessWidget {
  const DeleteChatDialog({super.key, this.onDelete, this.isGroup = false});
  final bool isGroup;
  final void Function()? onDelete;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              isGroup ? 'Exit Group' : 'Delete Chat',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(10),
            Text(
              'Are you sure you want to ${isGroup ? 'exit' : 'delete'}?',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(20),
            Row(
              children: [
                Expanded(
                  child: CommonButton(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    text: 'Cancel',
                  ),
                ),
                const Gap(15),
                Expanded(
                  child: CommonButton(
                    onTap: () {
                      Navigator.pop(context);
                      onDelete?.call();
                    },
                    text: isGroup ? 'Exit' : 'Delete',
                    removeShadow: true,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
