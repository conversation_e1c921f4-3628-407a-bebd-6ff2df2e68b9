import 'package:equatable/equatable.dart';
import 'package:leiuniverse/chat/model/chat_model.dart';

class ChatDetailResponse extends Equatable {
  const ChatDetailResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final ChatModel? data;

  factory ChatDetailResponse.fromJson(Map<String, dynamic> json) {
    return ChatDetailResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : ChatModel.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
