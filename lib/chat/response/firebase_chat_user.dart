import 'package:cloud_firestore/cloud_firestore.dart';

class FirebaseChatUser {
  final String id;
  final int? userId;
  final int badge;
  final DocumentSnapshot? documentSnapshot;

  FirebaseChatUser({
    this.id = '',
    this.userId,
    this.badge = 0,
    this.documentSnapshot,
  });

  factory FirebaseChatUser.fromJson(Map<String, dynamic> json) {
    return FirebaseChatUser(
      userId: json['user_id'] as int?,
      badge: (json['badge'] as int?) ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'badge': badge,
    };
  }

  factory FirebaseChatUser.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    return FirebaseChatUser.fromJson(data ?? {}).copyWith(
      id: doc.id,
      documentSnapshot: doc,
    );
  }

  FirebaseChatUser copyWith({
    String? id,
    int? userId,
    int? badge,
    DocumentSnapshot? documentSnapshot,
  }) {
    return FirebaseChatUser(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      badge: badge ?? this.badge,
      documentSnapshot: documentSnapshot ?? this.documentSnapshot,
    );
  }

  @override
  String toString() {
    return 'FirebaseChatUser(id: $id, userId: $userId, badge: $badge)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FirebaseChatUser &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          userId == other.userId &&
          badge == other.badge;

  @override
  int get hashCode => id.hashCode ^ userId.hashCode ^ badge.hashCode;
}
