import 'package:equatable/equatable.dart';
import 'package:leiuniverse/chat/model/create_chat_data_model.dart';

class CreateChatResponse extends Equatable {
  const CreateChatResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final CreateChatDataModel? data;

  factory CreateChatResponse.fromJson(Map<String, dynamic> json) {
    return CreateChatResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : CreateChatDataModel.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
