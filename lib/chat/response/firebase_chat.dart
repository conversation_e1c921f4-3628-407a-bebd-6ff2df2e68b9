import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/core/utils/converters/time_stamp_converter.dart';
import 'package:leiuniverse/core/utils/utility.dart';

class FirebaseChat {
  final String id;
  final int? senderId;
  final String? senderName;
  final String? message;
  final String? type;
  final Timestamp? timestamp;
  final DocumentSnapshot? documentSnapshot;

  FirebaseChat({
    this.id = '',
    this.senderId,
    this.senderName,
    this.message,
    this.type,
    this.timestamp,
    this.documentSnapshot,
  });

  factory FirebaseChat.fromJson(Map<String, dynamic> json) {
    return FirebaseChat(
      senderId: json['sender_id'] as int?,
      senderName: json['sender_name'] as String?,
      message: json['message'] as String?,
      type: json['type'] as String?,
      timestamp: const TimeStampConverter().fromJson(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sender_id': senderId,
      'sender_name': senderName,
      'message': message,
      'type': type,
      'timestamp': const TimeStampConverter().toJson(timestamp),
    };
  }

  factory FirebaseChat.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    return FirebaseChat.fromJson(doc.data()!).copyWith(
      id: doc.id,
      documentSnapshot: doc,
    );
  }

  FirebaseChat copyWith({
    String? id,
    int? senderId,
    String? senderName,
    String? message,
    String? type,
    Timestamp? timestamp,
    DocumentSnapshot? documentSnapshot,
  }) {
    return FirebaseChat(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      documentSnapshot: documentSnapshot ?? this.documentSnapshot,
    );
  }

  DateTime get timeStampDate => timestamp?.toDate() ?? DateTime.now();

  DateTime get timeStampLocalDate => timeStampDate.toLocal();

  String formattedDate(BuildContext context) => Utility.formmtedDate(timeStampLocalDate, context);

  String get formattedTime => DateFormat('hh:mm a').format(timeStampLocalDate);

  @override
  String toString() {
    return 'FirebaseChat(id: $id, senderId: $senderId, message: $message, type: $type, timestamp: $timestamp)';
  }
}
