import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:leiuniverse/chat/response/firebase_chat.dart';
import 'package:leiuniverse/chat/response/firebase_chat_user.dart';
import 'package:leiuniverse/core/utils/converters/time_stamp_converter.dart';

class FirebaseMessageResponse {
  final String id;
  final String? chatId;
  final FirebaseChat? lastMessage;
  final List<FirebaseChatUser> users;
  final Timestamp? createdAt;
  final Timestamp? updatedAt;
  final DocumentSnapshot? documentSnapshot;
  final int? badge;

  FirebaseMessageResponse({
    this.id = '',
    this.chatId,
    this.lastMessage,
    this.users = const [],
    this.createdAt,
    this.updatedAt,
    this.documentSnapshot,
    this.badge,
  });

  factory FirebaseMessageResponse.fromJson(Map<String, dynamic> json) {
    return FirebaseMessageResponse(
      chatId: json['chat_id'] as String?,
      lastMessage:
          json['last_message'] != null ? FirebaseChat.fromJson(json['last_message'] as Map<String, dynamic>) : null,
      users: (json['users'] as List<dynamic>?)
              ?.map((e) => FirebaseChatUser.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      createdAt: const TimeStampConverter().fromJson(json['created_at']),
      updatedAt: const TimeStampConverter().fromJson(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'chat_id': chatId,
      'last_message': lastMessage?.toJson(),
      'users': users.map((e) => e.toJson()).toList(),
      'created_at': const TimeStampConverter().toJson(createdAt),
      'updated_at': const TimeStampConverter().toJson(updatedAt),
    };
  }

  factory FirebaseMessageResponse.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data()!;
    return FirebaseMessageResponse.fromJson(data).copyWith(
      id: doc.id,
      documentSnapshot: doc,
    );
  }

  FirebaseMessageResponse copyWith({
    String? id,
    String? chatId,
    FirebaseChat? lastMessage,
    List<FirebaseChatUser>? users,
    Timestamp? createdAt,
    Timestamp? updatedAt,
    DocumentSnapshot? documentSnapshot,
  }) {
    return FirebaseMessageResponse(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      lastMessage: lastMessage ?? this.lastMessage,
      users: users ?? this.users,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      documentSnapshot: documentSnapshot ?? this.documentSnapshot,
    );
  }
}
