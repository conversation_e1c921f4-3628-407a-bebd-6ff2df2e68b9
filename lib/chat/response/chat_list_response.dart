import 'package:equatable/equatable.dart';
import 'package:leiuniverse/chat/model/chat_model.dart';

class ChatListResponse extends Equatable {
  const ChatListResponse({
    this.status,
    this.message,
    this.data,
    this.currentPage,
    this.perPage,
    this.total,
    this.lastPage,
  });

  final int? status;
  final String? message;
  final List<ChatModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;
  final int? lastPage;

  ChatListResponse copyWith({
    int? status,
    String? message,
    List<ChatModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
    int? lastPage,
  }) {
    return ChatListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      lastPage: lastPage ?? this.lastPage,
    );
  }

  factory ChatListResponse.fromJson(Map<String, dynamic> json) {
    return ChatListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<ChatModel>.from(json["data"]!.map((x) => ChatModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
      lastPage: json["last_page"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
        "last_page": lastPage,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, $lastPage, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
        lastPage,
      ];
}
