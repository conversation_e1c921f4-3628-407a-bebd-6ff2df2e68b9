import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';

class AddRatingView extends StatefulWidget {
  const AddRatingView({super.key});

  @override
  State<AddRatingView> createState() => _AddRatingViewState();
}

class _AddRatingViewState extends State<AddRatingView> {
  final _formKey = GlobalKey<FormState>();
  final reviewController = TextEditingController();
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(22),
            topRight: Radius.circular(22),
          ),
        ),
        height: 538 + MediaQuery.of(context).viewInsets.bottom, // fixed height
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Write a review',
                    style: Theme.of(context).textTheme.headlineLarge,
                  ),
                  const Gap(4),
                  Text(
                    'Please share you opinion',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(height: 1.2),
                  ),
                  const Gap(20),
                  RatingBar(
                    itemCount: 5,
                    initialRating: 5,
                    allowHalfRating: true,
                    itemPadding: const EdgeInsets.symmetric(horizontal: 4),
                    direction: Axis.horizontal,
                    glowColor: AppColors.selectiveYellow,
                    maxRating: 5,
                    minRating: 0.5,
                    ratingWidget: RatingWidget(
                      full: const AppSvgImage(
                        AppAssets.fillStarIcon,
                        height: 36,
                        width: 36,
                      ),
                      half: const AppSvgImage(
                        AppAssets.halfStarIcon,
                        height: 36,
                        width: 36,
                      ),
                      empty: const AppSvgImage(
                        AppAssets.emptyStarIcon,
                        height: 36,
                        width: 36,
                      ),
                    ),
                    onRatingUpdate: (value) {
                      log(value.toString());
                    },
                  ),
                  const Gap(20),
                  AppTextFormField(
                    title: 'Write a review',
                    maxLines: 6,
                    controller: reviewController,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a review';
                      }
                      return null;
                    },
                  ),
                  const Gap(30),
                  ValueListenableBuilder<bool>(
                      valueListenable: isButtonLoading,
                      builder: (context, loading, _) {
                        return CommonButton(
                          onTap: loading
                              ? null
                              : () {
                                  if (_formKey.currentState!.validate()) {
                                    Navigator.pop(context);
                                  }
                                },
                          isLoading: loading,
                          text: 'Submit',
                        );
                      }),
                  const Gap(10),
                  InkWell(
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    onTap: () => Navigator.pop(context),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Cancel',
                            textAlign: TextAlign.center,
                            style: Theme.of(context).textTheme.titleSmall,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
