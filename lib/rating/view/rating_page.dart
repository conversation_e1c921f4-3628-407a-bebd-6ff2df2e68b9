import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/rating/view/add_rating_view.dart';
import 'package:leiuniverse/rating/widget/rating_view.dart';
import 'package:leiuniverse/rating/widget/review_view.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class RatingPage extends StatefulWidget {
  const RatingPage({super.key});

  @override
  State<RatingPage> createState() => _RatingPageState();
}

class _RatingPageState extends State<RatingPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Review & Ratings',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '4.3',
                      style: Theme.of(context).textTheme.headlineLarge?.copyWith(height: 1.2),
                    ),
                    const Gap(5),
                    Text(
                      '23 ratings', // 1k, 10k..
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText, height: 1.2),
                    ),
                  ],
                ),
                const Gap(28),
                Flexible(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RatingView(rating: 5, percent: percentage(total: 10, star: '10'), ratingCount: '100'),
                      const Gap(10),
                      RatingView(rating: 4, percent: percentage(total: 10, star: '8'), ratingCount: '10'),
                      const Gap(10),
                      RatingView(rating: 3, percent: percentage(total: 10, star: '6'), ratingCount: '10'),
                      const Gap(10),
                      RatingView(rating: 2, percent: percentage(total: 10, star: '4'), ratingCount: '10'),
                      const Gap(10),
                      RatingView(rating: 1, percent: percentage(total: 10, star: '2'), ratingCount: '10'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Divider(
            height: 4,
            thickness: 4,
            color: AppColors.porcelain,
          ),
          Flexible(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '13 Reviews',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary, height: 1.2),
                    ),
                    const Gap(14),
                    ListView.separated(
                      padding: EdgeInsets.zero,
                      itemCount: 12,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      separatorBuilder: (context, index) {
                        return const Padding(
                          padding: EdgeInsets.only(bottom: 20),
                          child: Divider(
                            height: 1,
                            thickness: 1,
                            color: AppColors.border,
                          ),
                        );
                      },
                      itemBuilder: (context, index) {
                        return const ReviewView();
                      },
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: BottomButtonWidget(
        onTap: () {
          showModalBottomSheet(
            context: context,
            backgroundColor: AppColors.white,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadiusGeometry.only(
                topLeft: Radius.circular(22),
                topRight: Radius.circular(22),
              ),
            ),
            builder: (context) => const AddRatingView(),
          );
        },
        text: 'Write a Review',
      ),
    );
  }

  static double percentage({required int total, String star = '0'}) {
    if (total == 0) return 0;
    double starRat = double.tryParse(star) ?? 0;
    return (starRat * 100) / total;
  }
}
