import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ReviewView extends StatelessWidget {
  const ReviewView({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Utility.imageLoader(
                url: '',
                placeholder: AppAssets.placeholderImage,
                height: 36,
                width: 36,
                shape: BoxShape.circle,
                isShapeCircular: true,
                fit: BoxFit.cover,
              ),
              const Gap(8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '<PERSON>',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(height: 1.2),
                    ),
                    Text(
                      '2 days ago',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.subText,
                            height: 1.2,
                          ),
                    )
                  ],
                ),
              ),
              Row(
                children: [
                  const AppSvgImage(AppAssets.fillStarIcon, height: 18, width: 18),
                  const Gap(6),
                  Text(
                    '4.6',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                  ),
                ],
              ),
            ],
          ),
          const Gap(8),
          Text(
            'text ' * 20,
            style: Theme.of(context).textTheme.bodyMedium,
          )
        ],
      ),
    );
  }
}
