import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class RatingView extends StatelessWidget {
  const RatingView({super.key, required this.rating, required this.percent, required this.ratingCount});

  final int rating;
  final double percent;
  final String ratingCount;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: MediaQuery.of(context).size.width / 4,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: List.generate(
              rating,
              (index) => const Padding(
                padding: EdgeInsets.only(right: 4),
                child: AppSvgImage(
                  AppAssets.fillStarIcon,
                  height: 14,
                  width: 14,
                ),
              ),
            ),
          ),
        ),
        LinearPercentIndicator(
          padding: const EdgeInsets.only(right: 10),
          width: MediaQuery.of(context).size.width / 3.4,
          lineHeight: 8,
          percent: percent != 0 ? (percent / 100) : 0,
          barRadius: const Radius.circular(20),
          backgroundColor: AppColors.transparent,
          progressColor: AppColors.primary,
        ),
        Flexible(
          child: Text(
            ratingCount,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText, height: 1.2),
          ),
        ),
      ],
    );
  }
}
