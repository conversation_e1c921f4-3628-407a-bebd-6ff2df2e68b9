import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';

part 'business_event.dart';
part 'business_state.dart';

@injectable
class BusinessBloc extends Bloc<BusinessEvent, BusinessState> {
  BusinessBloc(this.businessRepository) : super(BusinessInitial()) {
    on<LoadBusinesses>(_loadBusinesses);
    on<LoadMoreBusinesses>(_loadMoreBusinesses);
    on<AddBusiness>(_onAddBusiness);
    on<EditBusiness>(_onEditBusiness);
    on<DeleteBusiness>(_onDelete);
  }
  final IBusinessRepository businessRepository;

  Future<void> _loadBusinesses(LoadBusinesses event, Emitter<BusinessState> emit) async {
    emit(BusinessLoading());
    final failOrSuccess = await businessRepository.businessList(search: event.search, isOwned: 1);
    emit(
      failOrSuccess.fold(
        (l) => BusinessError(failure: l),
        (r) => BusinessLoaded(businesses: r.data ?? [], hasReachedMax: (r.data?.length ?? 0) < 10),
      ),
    );
  }

  Future<void> _loadMoreBusinesses(
    LoadMoreBusinesses event,
    Emitter<BusinessState> emit,
  ) async {
    if (state is! BusinessLoaded) return;
    final newLoadedState = state as BusinessLoaded;

    emit(newLoadedState.copyWith(isLoadingMore: true));

    final failOrSucess = await businessRepository.businessList(
      page: (newLoadedState.page ?? 0) + 1,
      isOwned: 1,
    );

    emit(
      failOrSucess.fold(
        (l) => newLoadedState.copyWith(isLoadingMore: false, loadMoreFailure: l),
        (success) => newLoadedState.copyWith(
          businesses: [...newLoadedState.businesses, ...success.data ?? []],
          hasReachedMax: (success.data?.length ?? 0) < 10,
          isLoadingMore: false,
          page: (newLoadedState.page ?? 0) + 1,
        ),
      ),
    );
  }

  void _onAddBusiness(
    AddBusiness event,
    Emitter<BusinessState> emit,
  ) async {
    final loadedState = state as BusinessLoaded;
    emit(loadedState.copyWith(businesses: [...loadedState.businesses, event.business]));
  }

  void _onEditBusiness(
    EditBusiness event,
    Emitter<BusinessState> emit,
  ) async {
    final loadedState = state as BusinessLoaded;
    emit(
      loadedState.copyWith(
        businesses: loadedState.businesses.map((e) => e.id == event.business.id ? event.business : e).toList(),
      ),
    );
  }

  Future<void> _onDelete(
    DeleteBusiness event,
    Emitter<BusinessState> emit,
  ) async {
    final loadedState = state as BusinessLoaded;
    emit(loadedState.copyWith(isStackLoading: true));
    final failOrSuccess = await businessRepository.deleteBusiness(businessId: event.businessId);
    emit(
      failOrSuccess.fold((l) => BusinessError(failure: l), (r) {
        final updatedBusinesses = loadedState.businesses.where((element) => element.id != event.businessId).toList();
        Utility.toast(message: r.message);

        return loadedState.copyWith(businesses: updatedBusinesses, isStackLoading: false);
      }),
    );
  }
}
