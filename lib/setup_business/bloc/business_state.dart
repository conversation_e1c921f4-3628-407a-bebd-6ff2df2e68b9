part of 'business_bloc.dart';

sealed class BusinessState extends Equatable {
  const BusinessState();

  R when<R>({
    required R Function() initial,
    required R Function() loading,
    required R Function(BusinessLoaded) loaded,
    required R Function(HttpFailure message) error,
  }) {
    if (this is BusinessInitial) return initial();
    if (this is BusinessLoading) return loading();
    if (this is BusinessLoaded) return loaded((this as BusinessLoaded));
    if (this is BusinessError) return error((this as BusinessError).failure);
    throw Exception('Unrecognized BusinessState: $runtimeType');
  }

  R? whenOrNull<R>({
    R Function()? initial,
    R Function()? loading,
    R Function(List<BusinessModel>)? loaded,
    R Function(HttpFailure? failure)? error,
  }) {
    if (this is BusinessInitial) return initial?.call();
    if (this is BusinessLoading) return loading?.call();
    if (this is BusinessLoaded) return loaded?.call((this as BusinessLoaded).businesses);
    if (this is BusinessError) return error?.call((this as BusinessError).failure);
    return null;
  }

  @override
  List<Object?> get props => [];
}

class BusinessInitial extends BusinessState {}

class BusinessLoading extends BusinessState {}

class BusinessLoaded extends BusinessState {
  final List<BusinessModel> businesses;
  final bool isStackLoading;
  final bool hasReachedMax;
  final int? page;
  final bool isLoadingMore;
  final HttpFailure? loadMoreFailure;

  const BusinessLoaded({
    this.businesses = const [],
    this.isStackLoading = false,
    this.hasReachedMax = false,
    this.page = 1,
    this.isLoadingMore = false,
    this.loadMoreFailure,
  });

  BusinessLoaded copyWith({
    List<BusinessModel>? businesses,
    bool? isStackLoading,
    bool? hasReachedMax,
    int? page,
    bool? isLoadingMore,
    HttpFailure? loadMoreFailure,
  }) {
    return BusinessLoaded(
      businesses: businesses ?? this.businesses,
      isStackLoading: isStackLoading ?? this.isStackLoading,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      page: page ?? this.page,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreFailure: loadMoreFailure ?? this.loadMoreFailure,
    );
  }

  @override
  List<Object?> get props => [businesses, isStackLoading, hasReachedMax, page, isLoadingMore, loadMoreFailure];
}

class BusinessError extends BusinessState {
  final HttpFailure failure;
  const BusinessError({required this.failure});

  BusinessError copyWith({
    HttpFailure? message,
  }) {
    return BusinessError(
      failure: message ?? failure,
    );
  }
}
