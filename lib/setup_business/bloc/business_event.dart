part of 'business_bloc.dart';

sealed class BusinessEvent extends Equatable {
  const BusinessEvent();

  @override
  List<Object> get props => [];
}

class LoadBusinesses extends BusinessEvent {
  final String? search;
  const LoadBusinesses({this.search});
}

class LoadMoreBusinesses extends BusinessEvent {
  const LoadMoreBusinesses();
}

class AddBusiness extends BusinessEvent {
  final BusinessModel business;

  const AddBusiness({required this.business});
}

class EditBusiness extends BusinessEvent {
  final BusinessModel business;

  const EditBusiness({required this.business});
}

class DeleteBusiness extends BusinessEvent {
  final int businessId;
  const DeleteBusiness({required this.businessId});
}
