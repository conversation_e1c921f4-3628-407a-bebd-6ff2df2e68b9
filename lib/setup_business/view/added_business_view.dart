import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/setup_business/widget/business_info_widget.dart';
import 'package:leiuniverse/widget/common_button.dart';

class AddedBusinessView extends StatelessWidget {
  const AddedBusinessView({super.key, this.businessList = const []});
  final List<BusinessModel> businessList;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider.value(
                    value: context.read<BusinessBloc>(),
                    child: const AddBusinessServicePage(),
                  ),
                ),
              );
            },
            text: 'Add Business/Service',
            removeShadow: true,
          ),
          const Gap(20),
          ListView.separated(
            separatorBuilder: (context, index) => const Gap(16),
            itemCount: businessList.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return BusinessInfoWidget(
                business: businessList[index],
              );
            },
          ),
        ],
      ),
    );
  }
}
