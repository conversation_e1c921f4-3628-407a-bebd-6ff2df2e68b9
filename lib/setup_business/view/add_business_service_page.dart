import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/category_dropdown.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class AddBusinessServicePage extends StatefulWidget {
  const AddBusinessServicePage({
    super.key,
    this.business,
  });
  final BusinessModel? business;

  @override
  State<AddBusinessServicePage> createState() => _AddBusinessServicePageState();
}

class _AddBusinessServicePageState extends State<AddBusinessServicePage> {
  final _formKey = GlobalKey<FormState>();
  final shortDiscriptionController = TextEditingController();
  final selectedTypeOfBusinessOrService = ValueNotifier<CategoryModel?>(null);
  final selectedSubCategory = ValueNotifier<CategoryModel?>(null);
  final selectedChildSubCategory = ValueNotifier<CategoryModel?>(null);
  final imageList = ValueNotifier<List<PickedFileModel>>([]);
  final deletedImageList = ValueNotifier<List<int>>([]);

  final isButtonLoading = ValueNotifier<bool>(false);
  final subCategoryKey = GlobalKey<CategoryDropdownState>();
  final childCategoryKey = GlobalKey<CategoryDropdownState>();

  @override
  void initState() {
    super.initState();
    setData();

    if (widget.business == null) {
      selectedTypeOfBusinessOrService.value = CategoryModel.all;
    }
  }

  void setData() {
    final category = widget.business?.category;

    if (category != null) {
      if (category.parentCategory?.parentCategory != null) {
        // 3 Levels: Type > Sub > Child
        selectedTypeOfBusinessOrService.value = category.parentCategory!.parentCategory!;
        selectedSubCategory.value = category.parentCategory!;
        selectedChildSubCategory.value = category;
      } else if (category.parentCategory != null) {
        // 2 Levels: Type > Sub
        selectedTypeOfBusinessOrService.value = category.parentCategory!;
        selectedSubCategory.value = category;
      } else {
        // 1 Level: Only Type
        selectedTypeOfBusinessOrService.value = category;
      }
    }

    if (widget.business?.shortDescription != null) {
      shortDiscriptionController.text = widget.business!.shortDescription!;
    }

    if (widget.business?.images != null) {
      for (var element in widget.business!.images!) {
        imageList.value = [...imageList.value, PickedFileModel(networkFile: element)];
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: widget.business != null ? 'Edit Business/Service' : 'Add Business/Service',
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    ValueListenableBuilder<CategoryModel?>(
                      valueListenable: selectedTypeOfBusinessOrService,
                      builder: (context, typeOfBusinessOrService, _) {
                        return CategoryDropdown(
                          isRequired: true,
                          title: 'Type of business or service',
                          selectedCategory: [if (typeOfBusinessOrService != null) typeOfBusinessOrService],
                          onCategoryChanged: (categories) {
                            selectedTypeOfBusinessOrService.value =
                                categories?.firstOrNull?.id == CategoryModel.all.id ? null : categories?.firstOrNull;
                            selectedSubCategory.value = null;
                            selectedChildSubCategory.value = null;

                            subCategoryKey.currentState?.resetSelection();
                            childCategoryKey.currentState?.resetSelection();
                          },
                        );
                      },
                    ),
                    const Gap(14),
                    ValueListenableBuilder<CategoryModel?>(
                        valueListenable: selectedTypeOfBusinessOrService,
                        builder: (context, typeOrBusiness, _) {
                          return ValueListenableBuilder<CategoryModel?>(
                              valueListenable: selectedSubCategory,
                              builder: (context, subCategory, _) {
                                return CategoryDropdown(
                                  key: subCategoryKey,
                                  isRequired: false,
                                  isOptional: true,
                                  title: 'Sub category',
                                  parentCategoryName: 'Type of business or service',
                                  parentId: typeOrBusiness?.id,
                                  selectedCategory: [if (subCategory != null) subCategory],
                                  onCategoryChanged: (categories) {
                                    selectedSubCategory.value = categories?.firstOrNull?.id == CategoryModel.all.id
                                        ? null
                                        : categories?.firstOrNull;
                                    selectedChildSubCategory.value = null;

                                    childCategoryKey.currentState?.resetSelection();
                                  },
                                );
                              });
                        }),
                    const Gap(14),
                    ValueListenableBuilder<CategoryModel?>(
                      valueListenable: selectedSubCategory,
                      builder: (context, subCategory, _) {
                        return ValueListenableBuilder<CategoryModel?>(
                            valueListenable: selectedChildSubCategory,
                            builder: (context, childSubCategory, _) {
                              return CategoryDropdown(
                                key: childCategoryKey,
                                isOptional: true,
                                title: 'Child sub category',
                                isMulti: false, // Or true if you later want multi-select
                                parentCategoryName: 'Sub category',
                                parentId: subCategory?.id,
                                selectedCategory: [if (childSubCategory != null) childSubCategory],
                                onCategoryChanged: (categories) {
                                  selectedChildSubCategory.value = categories?.firstOrNull;
                                },
                              );
                            });
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Short description',
                      isRequired: true,
                      controller: shortDiscriptionController,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter short description';
                        }
                        return null;
                      },
                      maxLines: 5,
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 0,
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Images of business or service*',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: AppColors.primary, fontSize: 16),
                    ),
                    const Gap(20),
                    // Business Image View
                    BusinessImageView(
                      images: imageList.value,
                      onImageSelected: (images) {
                        imageList.value = [...images];
                      },
                      onDeletedImages: (deletedImages) {
                        deletedImageList.value = [...deletedImageList.value, deletedImages];
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: ValueListenableBuilder<bool>(
          valueListenable: isButtonLoading,
          builder: (context, loading, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CommonButton(
                  onTap: () {
                    if (imageList.value.isEmpty) {
                      return Utility.toast(message: 'Please add at least one image');
                    }
                    if (!_formKey.currentState!.validate()) {
                      return;
                    }
                    widget.business != null ? updateBusiness() : createBusiness();
                  },
                  margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                  text: widget.business != null ? 'Update' : 'Continue',
                  isLoading: loading,
                ),
              ],
            );
          }),
    );
  }

  Future<void> createBusiness() async {
    isButtonLoading.value = true;
    final response = await getIt<IBusinessRepository>().createBusiness(
      categoryId: selectedChildSubCategory.value?.id.toString() ??
          selectedSubCategory.value?.id.toString() ??
          selectedTypeOfBusinessOrService.value?.id.toString() ??
          '',
      description: shortDiscriptionController.text,
      images: imageList.value,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1 && r.data != null) {
          context.read<BusinessBloc>().add(AddBusiness(business: r.data!));
          Navigator.pop(context);
        }
        Utility.toast(message: r.message);
      },
    );
  }

  Future<void> updateBusiness() async {
    isButtonLoading.value = true;
    final response = await getIt<IBusinessRepository>().updateBusiness(
      businessId: widget.business?.id ?? 0,
      categoryId: selectedChildSubCategory.value?.id.toString() ??
          selectedSubCategory.value?.id.toString() ??
          selectedTypeOfBusinessOrService.value?.id.toString() ??
          '',
      description: shortDiscriptionController.text,
      images: imageList.value,
      deleteImages: deletedImageList.value,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1 && r.data != null) {
          context.read<BusinessBloc>().add(EditBusiness(business: r.data!));
          Navigator.pop(context);
        }
        Utility.toast(message: r.message);
      },
    );
  }
}
