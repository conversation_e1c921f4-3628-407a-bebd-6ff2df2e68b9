import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/payment_done/view/payment_done_page.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/setup_business/view/added_business_view.dart';
import 'package:leiuniverse/setup_business/view/empty_business_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/common_button_without_shadow.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class SetUpBusinessPage2Wrapper extends StatelessWidget {
  const SetUpBusinessPage2Wrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<BusinessBloc>()..add(const LoadBusinesses()),
      child: const SetUpBusinessPage2(),
    );
  }
}

class SetUpBusinessPage2 extends StatefulWidget {
  const SetUpBusinessPage2({super.key});

  @override
  State<SetUpBusinessPage2> createState() => _SetUpBusinessPage2State();
}

class _SetUpBusinessPage2State extends State<SetUpBusinessPage2> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessBloc, BusinessState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor:
              state.whenOrNull(loaded: (businesses) => businesses.isEmpty ? AppColors.white : AppColors.background) ??
                  AppColors.white,
          appBar: CommonAppBar(
            onBackTap: () {
              Navigator.pop(context);
            },
            title: 'Setup Business',
          ),
          body: Stack(
            children: [
              state.when(
                error: (message) => const Center(child: Text('Error')),
                initial: () => const SizedBox(),
                loading: () => const Center(
                  child: CustomProgressIndecator(
                    color: AppColors.primary,
                    size: 40,
                  ),
                ),
                loaded: (newState) {
                  if (newState.businesses.isEmpty) {
                    return EmptyBusinessView(
                      onTap: () async {
                        Navigator.push<bool>(
                          context,
                          MaterialPageRoute(
                            builder: (_) => BlocProvider.value(
                              value: context.read<BusinessBloc>(),
                              child: const AddBusinessServicePage(),
                            ),
                          ),
                        );
                      },
                    );
                  }
                  return SingleChildScrollView(
                    child: AddedBusinessView(businessList: newState.businesses),
                  );
                },
              ),
            ],
          ),
          bottomNavigationBar: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Builder(
                builder: (context) {
                  if (state is BusinessLoaded && state.businesses.isNotEmpty) {
                    return CommonButton(
                      onTap: state.isStackLoading
                          ? null
                          : () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const PaymentDonePage(),
                                ),
                              );
                            },
                      margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                      text: 'Continue',
                    );
                  }
                  return CommonButtonWithoutShadow(
                    onTap: () {
                      return Utility.toast(message: 'Please add a business');
                    },
                    margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                    text: 'Continue',
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
