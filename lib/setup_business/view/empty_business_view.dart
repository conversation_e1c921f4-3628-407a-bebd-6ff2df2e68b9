import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/widget/common_button.dart';

class EmptyBusinessView extends StatelessWidget {
  const EmptyBusinessView({super.key, this.onTap});
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            AppAssets.addBusinessImage,
            height: 150,
            width: 150,
          ),
          const Gap(25),
          CommonButton(
            onTap: onTap,
            text: 'Add Business/Service',
            width: 238,
          ),
        ],
      ),
    );
  }
}
