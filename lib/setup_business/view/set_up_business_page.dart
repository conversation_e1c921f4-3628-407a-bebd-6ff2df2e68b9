import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page_2.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/city_dropdown.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/country_dropdown.dart';
import 'package:leiuniverse/widget/state_dropdown.dart';

class SetUpBusinessPage extends StatefulWidget {
  const SetUpBusinessPage({super.key});

  @override
  State<SetUpBusinessPage> createState() => _SetUpBusinessPageState();
}

class _SetUpBusinessPageState extends State<SetUpBusinessPage> {
  final _formKey = GlobalKey<FormState>();
  final instagramController = TextEditingController();
  final facebookController = TextEditingController();
  final pincodeController = TextEditingController();

  List<CountryModel> countryList = <CountryModel>[];
  List<CountryModel> stateList = <CountryModel>[];
  List<CountryModel> cityList = <CountryModel>[];

  final selectedCity = ValueNotifier<CountryModel?>(null);
  final selectedState = ValueNotifier<CountryModel?>(null);
  final selectedCountry = ValueNotifier<CountryModel?>(null);
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        leadingWidget: SizedBox.shrink(),
        centerTitle: true,
        title: 'Setup Business',
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Social Media Links',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Instagram',
                      controller: instagramController,
                      validator: Utility.validateUrl,
                      textInputAction: TextInputAction.next,
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Facebook',
                      controller: facebookController,
                      validator: Utility.validateUrl,
                      textInputAction: TextInputAction.next,
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 0,
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Address',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                    ),
                    const Gap(14),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: AppTextFormField(
                            title: 'Pincode',
                            controller: pincodeController,
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(10)
                            ],
                            isRequired: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter pincode';
                              }
                              return null;
                            },
                          ),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedCountry,
                              builder: (context, selected, _) {
                                return CountryDropdown(
                                  isRequired: true,
                                  selectedCountry: selected,
                                  onCountryChanged: (country) {
                                    countryList = [country];
                                    selectedCountry.value = country;
                                    selectedState.value = null;
                                    selectedCity.value = null;
                                  },
                                );
                              }),
                        ),
                      ],
                    ),
                    const Gap(14),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedCountry,
                              builder: (context, country, _) {
                                return ValueListenableBuilder<CountryModel?>(
                                    valueListenable: selectedState,
                                    builder: (context, selected, _) {
                                      return StateDropdown(
                                        isRequired: true,
                                        countryId: country?.id,
                                        selectedState: selected,
                                        onStateChanged: (state) {
                                          stateList = [state];
                                          selectedState.value = state;
                                          selectedCity.value = null;
                                        },
                                      );
                                    });
                              }),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedState,
                              builder: (context, state, _) {
                                return CityDropdown(
                                  isRequired: true,
                                  stateId: state?.id,
                                  onCityChanged: (city) {
                                    cityList = city;
                                    selectedCity.value = city.firstOrNull;
                                  },
                                  selectedCity: cityList,
                                );
                              }),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: ValueListenableBuilder<bool>(
          valueListenable: isButtonLoading,
          builder: (context, loading, _) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CommonButton(
                  onTap: () {
                    if (_formKey.currentState!.validate()) {
                      setUpBusiness();
                    }
                  },
                  isLoading: loading,
                  margin: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                  text: 'Continue',
                ),
              ],
            );
          }),
    );
  }

  Future<void> setUpBusiness() async {
    isButtonLoading.value = true;
    final response = await getIt<IUserRepository>().update(
      pincode: pincodeController.text,
      cityId: selectedCity.value?.id?.toString(),
      facebookUrl: facebookController.text,
      instagramUrl: instagramController.text,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data != null) {
          context.read<AuthenticationBloc>().add(UpdateUser(user: r.data));
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SetUpBusinessPage2Wrapper(),
              ),
            );
          }
        }
        isButtonLoading.value = false;
        Utility.toast(message: r.message);
      },
    );
  }
}
