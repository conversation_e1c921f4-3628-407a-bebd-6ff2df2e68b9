import 'package:equatable/equatable.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class BusinessUserListResponse extends Equatable {
  const BusinessUserListResponse({
    this.status,
    this.message,
    this.data,
    this.currentPage,
    this.perPage,
    this.total,
    this.lastPage,
  });

  final int? status;
  final String? message;
  final List<UserModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;
  final int? lastPage;

  BusinessUserListResponse copyWith({
    int? status,
    String? message,
    List<UserModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
    int? lastPage,
  }) {
    return BusinessUserListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      lastPage: lastPage ?? this.lastPage,
    );
  }

  factory BusinessUserListResponse.fromJson(Map<String, dynamic> json) {
    return BusinessUserListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<UserModel>.from(json["data"]!.map((x) => UserModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
      lastPage: json["last_page"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
        "last_page": lastPage,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, $lastPage, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
        lastPage,
      ];
}
