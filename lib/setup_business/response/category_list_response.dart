import 'package:equatable/equatable.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';

class CategoryListResponse extends Equatable {
  const CategoryListResponse({
    this.status,
    this.message,
    required this.data,
  });

  final int? status;
  final String? message;
  final List<CategoryModel> data;

  CategoryListResponse copyWith({
    int? status,
    String? message,
    List<CategoryModel>? data,
  }) {
    return CategoryListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory CategoryListResponse.fromJson(Map<String, dynamic> json) {
    return CategoryListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<CategoryModel>.from(json["data"]!.map((x) => CategoryModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
