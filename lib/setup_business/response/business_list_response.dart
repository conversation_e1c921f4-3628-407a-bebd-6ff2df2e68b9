import 'package:equatable/equatable.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';

class BusinessListResponse extends Equatable {
  const BusinessListResponse({
    this.status,
    this.message,
    this.data,
    this.currentPage,
    this.perPage,
    this.total,
    this.lastPage,
  });

  final int? status;
  final String? message;
  final List<BusinessModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;
  final int? lastPage;

  BusinessListResponse copyWith({
    int? status,
    String? message,
    List<BusinessModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
    int? lastPage,
  }) {
    return BusinessListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      lastPage: lastPage ?? this.lastPage,
    );
  }

  factory BusinessListResponse.fromJson(Map<String, dynamic> json) {
    return BusinessListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<BusinessModel>.from(json["data"]!.map((x) => BusinessModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
      lastPage: json["last_page"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data!.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
        "last_page": lastPage,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, $lastPage, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
        lastPage,
      ];
}
