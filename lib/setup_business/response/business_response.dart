import 'package:equatable/equatable.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';

class BusinessResponse extends Equatable {
  const BusinessResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final BusinessModel? data;

  BusinessResponse copyWith({
    int? status,
    String? message,
    BusinessModel? data,
  }) {
    return BusinessResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory BusinessResponse.fromJson(Map<String, dynamic> json) {
    return BusinessResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : BusinessModel.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
