import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/model/image_model.dart';

class BusinessModel extends Equatable {
  const BusinessModel({
    this.id,
    this.userId,
    this.categoryId,
    this.shortDescription,
    this.isActive,
    this.expiryDate,
    this.createdAt,
    this.updatedAt,
    this.images,
    this.category,
    this.user,
  });

  final int? id;
  final int? userId;
  final int? categoryId;
  final String? shortDescription;
  final int? isActive;
  final DateTime? expiryDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ImageModel>? images;
  final CategoryModel? category;
  final UserModel? user;

  BusinessModel copyWith({
    int? id,
    int? userId,
    int? categoryId,
    String? shortDescription,
    int? isActive,
    DateTime? expiryDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ImageModel>? images,
    CategoryModel? category,
    UserModel? user,
  }) {
    return BusinessModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      shortDescription: shortDescription ?? this.shortDescription,
      isActive: isActive ?? this.isActive,
      expiryDate: expiryDate ?? this.expiryDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      images: images ?? this.images,
      category: category ?? this.category,
      user: user ?? this.user,
    );
  }

  factory BusinessModel.fromJson(Map<String, dynamic> json) {
    return BusinessModel(
      id: json["id"],
      userId: json["user_id"],
      categoryId: json["category_id"],
      shortDescription: json["short_description"],
      isActive: json["is_active"],
      expiryDate: DateTime.tryParse(json["expiry_date"] ?? ""),
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      images: json["images"] == null ? [] : List<ImageModel>.from(json["images"]!.map((x) => ImageModel.fromJson(x))),
      category: json["category"] == null ? null : CategoryModel.fromJson(json["category"]),
      user: json["user"] == null ? null : UserModel.fromJson(json["user"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "category_id": categoryId,
        "short_description": shortDescription,
        "is_active": isActive,
        "expiry_date": expiryDate?.toIso8601String(),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "images": images == null ? [] : images!.map((x) => x.toJson()).toList(),
        "category": category?.toJson(),
        "user": user?.toJson(),
      };

  @override
  String toString() {
    return "$id, $userId, $categoryId, $shortDescription, $isActive, $expiryDate, $createdAt, $updatedAt, $images, $category $user";
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        categoryId,
        shortDescription,
        isActive,
        expiryDate,
        createdAt,
        updatedAt,
        images,
        category,
        user,
      ];

  String get validTillDate {
    if (expiryDate != null) {
      return DateFormat('dd/MM/yyyy').format(expiryDate!.toLocal());
    }
    return "";
  }
}
