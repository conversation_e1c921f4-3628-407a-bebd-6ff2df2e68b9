import 'package:equatable/equatable.dart';

class ImageModel extends Equatable {
  const ImageModel({
    this.id,
    this.businessId,
    this.imagePath,
    this.imageUrl,
    this.createdAt,
    this.updatedAt,
    this.eventId,
  });

  final int? id;
  final int? businessId;
  final int? eventId;
  final String? imagePath;
  final String? imageUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ImageModel copyWith({
    int? id,
    int? businessId,
    int? eventId,
    String? imagePath,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ImageModel(
      id: id ?? this.id,
      businessId: businessId ?? this.businessId,
      eventId: eventId ?? this.eventId,
      imagePath: imagePath ?? this.imagePath,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json["id"],
      businessId: json["business_id"],
      imagePath: json["image_path"],
      imageUrl: json["image_url"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "business_id": businessId,
        "event_id": eventId,
        "image_path": imagePath,
        "image_url": imageUrl,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  String toString() {
    return "$id, $businessId, $eventId, $imagePath, $imageUrl, $createdAt, $updatedAt, ";
  }

  @override
  List<Object?> get props => [
        id,
        businessId,
        eventId,
        imagePath,
        imageUrl,
        createdAt,
        updatedAt,
      ];
}
