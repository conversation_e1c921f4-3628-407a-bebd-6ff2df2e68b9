import 'package:equatable/equatable.dart';

class CategoryModel extends Equatable {
  const CategoryModel({
    this.id,
    this.name,
    this.imageUrl,
    this.parentId,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.parentCategory,
  });

  final int? id;
  final String? name;
  final String? imageUrl;
  final int? parentId;
  final int? isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CategoryModel? parentCategory;

  CategoryModel copyWith({
    int? id,
    String? name,
    String? imageUrl,
    int? parentId,
    int? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    CategoryModel? parentCategory,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      imageUrl: imageUrl ?? this.imageUrl,
      parentId: parentId ?? this.parentId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      parentCategory: parentCategory ?? this.parentCategory,
    );
  }

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json["id"],
      name: json["name"],
      imageUrl: json["image_url"],
      parentId: json["parent_id"],
      isActive: json["is_active"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      parentCategory: json["parent"] == null ? null : CategoryModel.fromJson(json["parent"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "image_url": imageUrl,
        "parent_id": parentId,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "parent": parentCategory?.toJson(),
      };

  @override
  String toString() {
    return "CategoryModel(id: $id, name: $name, imageUrl: $imageUrl, parentId: $parentId, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, parentCategory: $parentCategory)";
  }

  @override
  List<Object?> get props => [
        id,
        name,
        imageUrl,
        parentId,
        isActive,
        createdAt,
        updatedAt,
        parentCategory,
      ];

  static const all = CategoryModel(id: null, name: 'All');
}
