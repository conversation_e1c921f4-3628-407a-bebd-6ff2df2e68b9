part of 'i_business_repository.dart';

@Injectable(as: IBusinessRepository)
class BusinessRepository extends IBusinessRepository {
  BusinessRepository(super.client);

  @override
  ApiResult<CategoryListResponse> categoryList({int? parentId, int page = 1, String? search}) async {
    final response = await client.get(
      url: AppStrings.categoryList,
      params: {
        if (parentId != null) 'parent_id': parentId.toString(),
        'page': page.toString(),
        'per_page': '10',
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(CategoryListResponse.fromJson);
  }

  @override
  ApiResult<BusinessListResponse> businessList({int page = 1, int isOwned = 0, int? userId, String? search}) async {
    final response = await client.get(
      url: AppStrings.businessList,
      params: {
        'page': page.toString(),
        'per_page': '10',
        'is_owned': isOwned.toString(),
        if (userId != null) 'user_id': userId.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(BusinessListResponse.fromJson);
  }

  @override
  ApiResult<BusinessUserListResponse> businessUserList({int page = 1, int? categoryId, String? search}) async {
    final response = await client.get(
      url: AppStrings.businessUsers,
      params: {
        'page': page.toString(),
        'per_page': '10',
        if (categoryId != null) 'category_id': categoryId.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(BusinessUserListResponse.fromJson);
  }

  @override
  ApiResult<BusinessResponse> createBusiness({
    String? categoryId,
    required String description,
    List<PickedFileModel>? images,
  }) async {
    final response = await client.multipart(
      url: AppStrings.createBusiness,
      files: [
        if (images != null)
          for (var i = 0; i < images.length; i++)
            if (images[i].file != null) MapEntry('images[$i]', await images[i].file!.compressFile()),
      ],
      requests: {
        if (categoryId != null && categoryId.trim().isNotEmpty) 'category_id': categoryId.trim(),
        'short_description': description.trim(),
      },
    );

    return response.parseResponse(BusinessResponse.fromJson);
  }

  @override
  ApiResult<BusinessResponse> updateBusiness({
    required String categoryId,
    required String description,
    required int businessId,
    List<PickedFileModel>? images,
    List<int>? deleteImages,
  }) async {
    final finalImages = images?.where((element) => element.file != null).toList();

    final response = await client.multipart(
      url: AppStrings.updateBusiness(businessId),
      files: [
        if (finalImages != null)
          for (var i = 0; i < finalImages.length; i++)
            MapEntry('images[$i]', await finalImages[i].file!.compressFile()),
      ],
      requests: {
        'category_id': categoryId.trim(),
        'short_description': description.trim(),
        if (deleteImages != null && deleteImages.isNotEmpty)
          for (var i = 0; i < deleteImages.length; i++) 'deleted_images[$i]': deleteImages[i].toString(),
      },
    );

    return response.parseResponse(BusinessResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteBusiness({required int businessId}) async {
    final response = await client.delete(
      url: AppStrings.deleteBusiness(businessId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }
}
