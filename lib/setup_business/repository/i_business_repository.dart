import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/file_extention.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/setup_business/response/business_list_response.dart';
import 'package:leiuniverse/setup_business/response/business_response.dart';
import 'package:leiuniverse/setup_business/response/business_user_list_response.dart';
import 'package:leiuniverse/setup_business/response/category_list_response.dart';

part 'business_repository.dart';

abstract class IBusinessRepository {
  IBusinessRepository(this.client);
  final Client client;

  ApiResult<CategoryListResponse> categoryList({
    int? parentId,
    int page = 1,
    String? search,
  });

  ApiResult<BusinessListResponse> businessList({
    int page = 1,
    int isOwned = 0,
    int? userId,
    String? search,
  });

  ApiResult<BusinessUserListResponse> businessUserList({
    int page = 1,
    int? categoryId,
    String? search,
  });

  ApiResult<BusinessResponse> createBusiness({
    String? categoryId,
    required String description,
    List<PickedFileModel>? images,
  });

  ApiResult<BusinessResponse> updateBusiness({
    required String categoryId,
    required String description,
    required int businessId,
    List<PickedFileModel>? images,
    List<int>? deleteImages,
  });

  ApiResult<CommonResponse> deleteBusiness({
    required int businessId,
  });
}
