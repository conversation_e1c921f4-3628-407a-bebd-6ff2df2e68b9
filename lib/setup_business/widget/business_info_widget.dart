import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/view/full_screen_image_view.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class BusinessInfoWidget extends StatelessWidget {
  const BusinessInfoWidget({super.key, this.business});

  final BusinessModel? business;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            business?.category?.parentCategory?.parentCategory?.name ??
                business?.category?.parentCategory?.name ??
                business?.category?.name ??
                '',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(10),
          if (business?.category?.parentCategory?.parentCategory?.name != null &&
              (business?.category?.parentCategory?.name != null || business?.category?.name != null)) ...[
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.start,
              runSpacing: 10,
              spacing: 10,
              children: [
                if (business?.category?.parentCategory?.parentCategory?.name != null &&
                    business?.category?.parentCategory?.name != null)
                  CategoryTypeWidget(
                      title: business?.category?.parentCategory?.name ?? '', color: AppColors.turquoiseGreen),
                if (business?.category?.parentCategory?.name != null && business?.category?.name != null)
                  CategoryTypeWidget(title: business?.category?.name ?? '', color: AppColors.turquoiseGreen),
              ],
            ),
            const Gap(20),
          ],
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                business?.images?.length ?? 0,
                (index) {
                  return InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => FullScreenImageViewer(
                            images: business?.images ?? [],
                            initialIndex: index,
                            isProfile: true,
                          ),
                        ),
                      );
                    },
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: business?.images?[index].imagePath ?? '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 93,
                        width: 93,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          const Gap(14),
          const Divider(
            height: 0,
          ),
          const Gap(12),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Rs.150 per year',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                ),
              ),
              const Gap(4),
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => BlocProvider.value(
                        value: context.read<BusinessBloc>(),
                        child: AddBusinessServicePage(business: business),
                      ),
                    ),
                  );
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: const AppSvgImage(
                  AppAssets.editIcon,
                ),
              ),
              const Gap(16),
              InkWell(
                onTap: () {
                  AppDailogs.deleteBusinessDailog(context, businessId: business?.id ?? 0);
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: const AppSvgImage(
                  AppAssets.deleteIcon,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
