import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:visibility_detector/visibility_detector.dart';

class SelectCategoryDialog extends StatefulWidget {
  const SelectCategoryDialog({
    super.key,
    this.selectedCategory,
    this.onCategoryChanged,
    this.parentId,
    required this.title,
    this.isMulti = false,
  });
  final int? parentId;
  final String title;
  final List<CategoryModel>? selectedCategory;
  final void Function(List<CategoryModel>? cities)? onCategoryChanged;
  final bool isMulti;

  @override
  State<SelectCategoryDialog> createState() => SelectCategoryDialogState();
}

class SelectCategoryDialogState extends State<SelectCategoryDialog> {
  final categoryList = ValueNotifier<List<CategoryModel>>([]);
  final selectedCategoryList = ValueNotifier<List<CategoryModel>>([]);

  int page = 0;
  final int perPage = 10;
  bool stop = false;

  final searchController = TextEditingController();

  final isLoading = ValueNotifier<bool>(false);
  final isPageLoading = ValueNotifier<bool>(false);

  // Local representation of "All" (compare by id == null)
  CategoryModel get _allOption => const CategoryModel(id: null, name: 'All');

  @override
  void initState() {
    super.initState();
    // Initialize runtime selection once from the incoming prop
    selectedCategoryList.value = [...(widget.selectedCategory ?? [])];
    getCategoryList(isFirst: true);
  }

  CategoryModel? _findById(List<CategoryModel> list, int? id) {
    try {
      return list.firstWhere((e) => e.id == id);
    } catch (_) {
      return null;
    }
  }

  bool _selectedContains(CategoryModel item) {
    if (item.id == null) {
      return selectedCategoryList.value.any((e) => e.id == null);
    } else {
      return selectedCategoryList.value.any((e) => e.id == item.id);
    }
  }

  Future<void> getCategoryList({bool isFirst = false}) async {
    if (isFirst) {
      isLoading.value = true;
      categoryList.value = [];
      page = 0;
      stop = false;
    } else {
      isPageLoading.value = true;
    }

    page += 1;

    final response = await getIt<IBusinessRepository>().categoryList(
      parentId: widget.parentId,
      page: page,
      search: searchController.text.trim(),
    );

    await response.fold(
      (l) {
        isLoading.value = false;
        isPageLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data.isNotEmpty) {
          if (r.data.length < perPage) {
            stop = true;
          }

          List<CategoryModel> newData = r.data;

          // Add All only on first page and when search is empty
          if (isFirst && searchController.text.trim().isEmpty) {
            newData = [_allOption, ...r.data];
          }

          if (isFirst) {
            // Replace the list
            categoryList.value = [...newData];

            // Preserve runtime selection by mapping existing selected ids to newData instances when possible.
            if (selectedCategoryList.value.any((e) => e.id == null)) {
              // If runtime selection already had All and we support All -> select All + all loaded
              final preserved = <CategoryModel>[];
              preserved.add(_allOption);
              preserved.addAll(newData.where((e) => e.id != null));
              selectedCategoryList.value = preserved;
            } else {
              final preserved = <CategoryModel>[];
              for (final sel in selectedCategoryList.value) {
                if (sel.id == null) {
                  // only add All if this dialog has All option
                  if (searchController.text.trim().isEmpty) preserved.add(_allOption);
                } else {
                  final match = _findById(newData, sel.id);
                  preserved.add(match ?? sel);
                }
              }
              // IMPORTANT: do NOT fall back to widget.selectedCategory here — respect the user's runtime choice.
              selectedCategoryList.value = preserved;
            }
          } else {
            // Pagination -> append
            categoryList.value = [...categoryList.value, ...newData];

            // If "All" was selected earlier, keep appending newly fetched items to selection
            if (selectedCategoryList.value.any((e) => e.id == null)) {
              final idsAlready = selectedCategoryList.value.where((e) => e.id != null).map((e) => e.id).toSet();
              final toAdd = r.data.where((d) => d.id != null && !idsAlready.contains(d.id)).toList();
              selectedCategoryList.value = [...selectedCategoryList.value, ...toAdd];
            }
          }
        }

        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      },
    );
    // No setState() needed — ValueNotifiers notify listeners.
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                const Spacer(),
                Text(widget.title, style: Theme.of(context).textTheme.headlineMedium),
                const Spacer(),
                InkWell(
                  onTap: () => Navigator.pop(context),
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const Icon(Icons.close, color: AppColors.subText),
                ),
              ],
            ),
            const Gap(20),
            AppTextFormField(
              controller: searchController,
              hintText: 'Search',
              onChanged: (value) {
                EasyDebounce.debounce('category_search', const Duration(milliseconds: 500), () {
                  categoryList.value = [];
                  page = 0;
                  stop = false;
                  getCategoryList(isFirst: true);
                });
              },
            ),
            const Gap(16),
            ValueListenableBuilder<bool>(
              valueListenable: isLoading,
              builder: (context, loading, _) {
                return Expanded(
                  child: loading
                      ? const CustomProgressIndecator(color: AppColors.subText, size: 40)
                      : ValueListenableBuilder<List<CategoryModel>>(
                          valueListenable: categoryList,
                          builder: (context, category, _) {
                            return category.isEmpty
                                ? Utility.noDataWidget(context: context, text: 'No ${widget.title} found')
                                : ValueListenableBuilder<List<CategoryModel>>(
                                    valueListenable: selectedCategoryList,
                                    builder: (context, selected, __) {
                                      return ListView.builder(
                                        padding: EdgeInsets.zero,
                                        itemCount: category.length,
                                        itemBuilder: (context, index) {
                                          final cat = category[index];

                                          final child = Padding(
                                            padding: EdgeInsets.only(bottom: widget.isMulti ? 4 : 0),
                                            child: InkWell(
                                              onTap: () {
                                                if (widget.isMulti) {
                                                  if (cat.id == null) {
                                                    // "All" category toggle — All exists only when search is empty (we add it only then)
                                                    if (selectedCategoryList.value.any((e) => e.id == null)) {
                                                      selectedCategoryList.value = [];
                                                    } else {
                                                      selectedCategoryList.value = [
                                                        _allOption,
                                                        ...category.where((e) => e.id != null)
                                                      ];
                                                    }
                                                  } else {
                                                    final alreadySelected =
                                                        selectedCategoryList.value.any((e) => e.id == cat.id);
                                                    if (alreadySelected) {
                                                      selectedCategoryList.value = [
                                                        ...selectedCategoryList.value.where((e) => e.id != cat.id)
                                                      ];
                                                    } else {
                                                      selectedCategoryList.value = [...selectedCategoryList.value, cat];
                                                    }

                                                    // Remove "All" when any individual selection exists
                                                    selectedCategoryList.value = [
                                                      ...selectedCategoryList.value.where((e) => e.id != null)
                                                    ];

                                                    // Auto-select "All" only if search is empty (so visible list == unfiltered list)
                                                    final allCategories = category.where((e) => e.id != null).toList();
                                                    final currSelected = selectedCategoryList.value;
                                                    final isSearchEmpty = searchController.text.trim().isEmpty;
                                                    final allSelected = allCategories.isNotEmpty &&
                                                        allCategories
                                                            .every((c) => currSelected.any((s) => s.id == c.id)) &&
                                                        isSearchEmpty;

                                                    if (allSelected) {
                                                      selectedCategoryList.value = [_allOption, ...allCategories];
                                                    }
                                                  }
                                                } else {
                                                  // Single selection
                                                  widget.onCategoryChanged?.call([cat]);
                                                  Navigator.pop(context);
                                                }
                                              },
                                              child: Container(
                                                padding: const EdgeInsets.all(16),
                                                decoration: BoxDecoration(
                                                  borderRadius: BorderRadius.circular(8),
                                                  color: widget.isMulti
                                                      ? _selectedContains(cat)
                                                          ? AppColors.primary.withOpacity2(0.3)
                                                          : null
                                                      : (selectedCategoryList.value.isNotEmpty
                                                              ? (selectedCategoryList.value.first.id == cat.id)
                                                              : widget.selectedCategory?.firstOrNull?.id == cat.id)
                                                          ? AppColors.primary.withOpacity2(0.3)
                                                          : null,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child:
                                                          Text(cat.name!, style: Theme.of(context).textTheme.bodyLarge),
                                                    ),
                                                    if (widget.isMulti)
                                                      Icon(
                                                        _selectedContains(cat)
                                                            ? Icons.check_box
                                                            : Icons.check_box_outline_blank,
                                                        color: AppColors.primary,
                                                      ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          );

                                          if (index == category.length - 1 && !stop) {
                                            return VisibilityDetector(
                                              key: Key('${cat.id}_$index'),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  child,
                                                  ValueListenableBuilder<bool>(
                                                    valueListenable: isPageLoading,
                                                    builder: (context, pageLoading, _) {
                                                      return pageLoading
                                                          ? const Padding(
                                                              padding: EdgeInsets.only(top: 30),
                                                              child: CustomProgressIndecator(
                                                                color: AppColors.subText,
                                                                size: 40,
                                                              ),
                                                            )
                                                          : const SizedBox.shrink();
                                                    },
                                                  )
                                                ],
                                              ),
                                              onVisibilityChanged: (VisibilityInfo info) {
                                                if (!stop &&
                                                    index == (category.length - 1) &&
                                                    !isLoading.value &&
                                                    !isPageLoading.value) {
                                                  getCategoryList(isFirst: false);
                                                }
                                              },
                                            );
                                          }

                                          return child;
                                        },
                                      );
                                    },
                                  );
                          },
                        ),
                );
              },
            ),
            if (widget.isMulti) ...[
              const Gap(10),
              CommonButton(
                onTap: () {
                  widget.onCategoryChanged?.call(selectedCategoryList.value);
                  Navigator.pop(context);
                },
                text: 'Submit',
              ),
              const Gap(16),
            ],
          ],
        ),
      ),
    );
  }
}
