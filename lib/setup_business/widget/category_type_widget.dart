import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';

class CategoryTypeWidget extends StatelessWidget {
  const CategoryTypeWidget({super.key, this.title, this.color});

  final String? title;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color ?? AppColors.offGreen,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        title ?? '',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 15, height: 1.2),
      ),
    );
  }
}
