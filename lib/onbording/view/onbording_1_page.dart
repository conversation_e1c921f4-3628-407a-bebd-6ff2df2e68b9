import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/onbording/view/onbording_2_page.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class Onbording1Page extends StatelessWidget {
  const Onbording1Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CommonAppBar(
        hideDivider: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(AppAssets.onboardingImage1),
            const Gap(30),
            Text(
              'Welcome to Lei-Universe!',
              style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: AppColors.black),
            ),
            const Gap(10),
            Text(
              'Connect with artists, agencies, and\nevent professionals from around the\nworld — all in one place.',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.black),
              textAlign: TextAlign.center,
            ),
            const Gap(76),
            CommonButton(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const Onbording2Page(),
                  ),
                );
              },
              text: 'Next',
              width: 165,
            ),
          ],
        ),
      ),
    );
  }
}
