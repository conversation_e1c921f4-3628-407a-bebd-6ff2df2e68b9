// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/url_manager.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/view/full_screen_image_view.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/market_place/market_detail_bloc/marketplace_detail_bloc.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/view/post_ad_page.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class MarkDetailUpdateData {
  final Advertisemnet advertisement;
  final bool isForDelete;
  const MarkDetailUpdateData({required this.advertisement, this.isForDelete = false});
}

class MarketPlaceDetailsPage extends StatelessWidget {
  const MarketPlaceDetailsPage({
    super.key,
    required this.advertisementId,
  });
  final int advertisementId;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<MarketplaceDetailBloc>(param1: advertisementId)..add(const MarketplaceDetailLoad()),
      child: const _MarketPlaceDetailsPage(key: Key('MarketPlaceDetailsPage')),
    );
  }
}

class _MarketPlaceDetailsPage extends StatelessWidget {
  const _MarketPlaceDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Ad Detail',
        onBackTap: () {
          final advertisement =
              context.read<MarketplaceDetailBloc>().state.mapOrNull(loaded: (state) => state.advertisement);
          final data = MarkDetailUpdateData(advertisement: advertisement!);
          Navigator.pop(context, data);
        },
        actions: [
          InkWell(
            onTap: () {
              context.read<MarketplaceDetailBloc>().add(const MarketplaceDetailFavouriteToggle());
            },
            overlayColor: WidgetStatePropertyAll(AppColors.transparent),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: BlocSelector<MarketplaceDetailBloc, MarketplaceDetailState, bool>(
                selector: (state) => state.mapOrNull(loaded: (state) => state.advertisement.isFavourite == 1) ?? false,
                builder: (context, isFavourite) {
                  return AppSvgImage(
                    isFavourite ? AppAssets.favouriteIcon : AppAssets.unfavouriteIcon,
                    height: 24,
                    width: 24,
                  );
                },
              ),
            ),
          ),
          const Gap(20),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 16.0),
            child: AppSvgImage(AppAssets.shareIcon, height: 24, width: 24),
          ),
          BlocSelector<AuthenticationBloc, AuthenticationState, int>(
            selector: (state) => state.user.id,
            builder: (context, user) {
              return BlocSelector<MarketplaceDetailBloc, MarketplaceDetailState, int>(
                selector: (state) => state.mapOrNull(loaded: (state) => state.advertisement.userId) ?? 0,
                builder: (context, advertisementUserId) {
                  return Visibility(
                    visible: user == advertisementUserId,
                    replacement: const Gap(4),
                    child: PopupMenuButton(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        color: AppColors.white,
                        elevation: 6,
                        position: PopupMenuPosition.under,
                        onSelected: (value) async {
                          final advModel = context
                              .read<MarketplaceDetailBloc>()
                              .state
                              .mapOrNull(loaded: (state) => state.advertisement);
                          if (value == 0) {
                            final refresh = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => PostAdPage(
                                  advertisement: advModel,
                                ),
                              ),
                            );

                            if (refresh != null && refresh is Advertisemnet && context.mounted) {
                              context
                                  .read<MarketplaceDetailBloc>()
                                  .add(MarketplaceDetailUpdate(advertisement: refresh));
                            }
                          }
                          if (value == 1 && context.mounted) {
                            AppDailogs.deleteAdvertisementDailog(
                              context,
                              advertisementId: advModel?.id ?? 0,
                              onDelete: () {
                                final deletedAdvertisement = context.read<MarketplaceDetailBloc>().state.mapOrNull(
                                      loaded: (state) => state.advertisement,
                                    );

                                final data =
                                    MarkDetailUpdateData(advertisement: deletedAdvertisement!, isForDelete: true);
                                Navigator.pop(context, data);
                              },
                            );
                          }
                        },
                        itemBuilder: (context) {
                          return [
                            const PopupMenuItem(
                              value: 0,
                              child: Text('Edit'),
                            ),
                            const PopupMenuItem(
                              value: 1,
                              child: Text('Delete'),
                            ),
                          ];
                        },
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 10),
                          child: AppSvgImage(AppAssets.menuDotsIcon, height: 24, width: 24),
                        )),
                  );
                },
              );
            },
          ),
          const Gap(4),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: BlocConsumer<MarketplaceDetailBloc, MarketplaceDetailState>(
            listenWhen: (previous, current) =>
                previous.mapOrNull(loaded: (state) => state.actionFailure) !=
                current.mapOrNull(loaded: (state) => state.actionFailure),
            listener: (context, state) {
              state.mapOrNull(loaded: (state) {
                if (state.actionFailure != null) {
                  Utility.toast(message: state.actionFailure!.message);
                }
              });
            },
            builder: (context, state) {
              return state.map(
                initial: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40)),
                loading: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40)),
                loaded: (state) {
                  final parentCategory = state.advertisement.parentCategory;
                  final subCategory = state.advertisement.subCategory;
                  final childSubCategory = state.advertisement.categories.firstOrNull;
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        borderRadius: BorderRadius.circular(12),
                        overlayColor: WidgetStateProperty.all(AppColors.transparent),
                        onTap: () async {
                          final newUser = await Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ProfileDetailPage(userId: state.advertisement.user?.id),
                            ),
                          );
                          if (newUser != null &&
                              newUser.hashCode != state.advertisement.user?.hashCode &&
                              context.mounted) {
                            context.read<MarketplaceDetailBloc>().add(
                                MarketplaceDetailUpdate(advertisement: state.advertisement.copyWith(user: newUser)));
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(14),
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Utility.imageLoader(
                                    url: state.advertisement.user?.profileImage ?? '',
                                    placeholder: AppAssets.userAvtarImage,
                                    height: 44,
                                    width: 44,
                                    fit: BoxFit.cover,
                                    borderRadius: BorderRadius.circular(6),
                                    boxShadow: [
                                      BoxShadow(
                                        offset: const Offset(0, 4),
                                        blurRadius: 4,
                                        spreadRadius: 0,
                                        color: AppColors.black.withOpacity2(0.15),
                                      ),
                                    ],
                                  ),
                                  const Gap(14),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Flexible(
                                              child: Text(
                                                state.advertisement.user?.name ?? '',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                style: Theme.of(context).textTheme.bodySmall,
                                              ),
                                            ),
                                            const Gap(3),
                                            if ((state.advertisement.user?.likesCount ?? 0) > 500)
                                              const AppSvgImage(AppAssets.verifyIcon),
                                          ],
                                        ),
                                        if (state.advertisement.user?.companyName.isPureValid ?? false)
                                          Text(
                                            state.advertisement.user?.companyName ?? '',
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: Theme.of(context)
                                                .textTheme
                                                .labelLarge
                                                ?.copyWith(color: AppColors.subText),
                                          ),
                                        const Gap(12),
                                        if (state.advertisement.user?.role == AppConstants.businessRole)
                                          Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  context.read<MarketplaceDetailBloc>().add(
                                                      MarketplaceDetailLikeToggle(state.advertisement.user?.id ?? 0));
                                                },
                                                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                                child: AppSvgImage(
                                                  state.advertisement.user?.isLiked == 1
                                                      ? AppAssets.likeHandIcon
                                                      : AppAssets.dislikeHandIcon,
                                                ),
                                              ),
                                              const Gap(6),
                                              Text(state.advertisement.user?.formmtedLikeCount ?? '',
                                                  maxLines: 1,
                                                  overflow: TextOverflow.ellipsis,
                                                  style: Theme.of(context).textTheme.bodySmall),
                                              // const Gap(20),
                                              // Flexible(
                                              //   child: Row(
                                              //     mainAxisSize: MainAxisSize.min,
                                              //     children: [
                                              //       const AppSvgImage(AppAssets.fillStarIcon, height: 24, width: 24),
                                              //       const Gap(6),
                                              //       Text(
                                              //         '4.6',
                                              //         style:
                                              //             Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                                              //       ),
                                              //       const Gap(6),
                                              //       Flexible(
                                              //         child: Text(
                                              //           '(15 reviews)',
                                              //           maxLines: 1,
                                              //           overflow: TextOverflow.ellipsis,
                                              //           style: Theme.of(context)
                                              //               .textTheme
                                              //               .bodySmall
                                              //               ?.copyWith(color: AppColors.subText),
                                              //         ),
                                              //       ),
                                              //     ],
                                              //   ),
                                              // )
                                            ],
                                          ),
                                      ],
                                    ),
                                  ),
                                  const Gap(10),
                                  const Padding(
                                    padding: EdgeInsets.only(top: 8.0),
                                    child: AppSvgImage(AppAssets.rightArrowIconIos, height: 24, width: 24),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const Gap(20),
                      Text(
                        'Business/Services Ad'.toUpperCase(),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.subText,
                            ),
                      ),
                      const Gap(20),
                      Container(
                        padding: const EdgeInsets.all(14),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              state.advertisement.title ?? '',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.headlineMedium,
                            ),
                            const Gap(10),
                            if (state.advertisement.advLocation.isNotEmpty) ...[
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const AppSvgImage(
                                    AppAssets.locationIcon,
                                  ),
                                  const Gap(6),
                                  Flexible(
                                    child: Text(
                                      state.advertisement.advLocation,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.2),
                                    ),
                                  ),
                                ],
                              ),
                              const Gap(10),
                            ],
                            if (parentCategory != null || subCategory != null || childSubCategory != null) ...[
                              Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: [
                                  if (parentCategory != null)
                                    CategoryTypeWidget(title: parentCategory.name ?? '', color: AppColors.background),
                                  if (subCategory != null)
                                    CategoryTypeWidget(title: subCategory.name ?? '', color: AppColors.background),
                                  if (childSubCategory != null)
                                    CategoryTypeWidget(
                                        title: childSubCategory.category?.name ?? '', color: AppColors.background),
                                ],
                              ),
                              const Gap(10),
                            ],
                            Text(
                              '\$${state.advertisement.price}',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            const Gap(14),
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: List.generate(
                                  state.advertisement.images.length,
                                  (index) {
                                    final image = state.advertisement.images[index];
                                    return InkWell(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => FullScreenImageViewer(
                                              images: state.advertisement.images,
                                              initialIndex: index,
                                            ),
                                          ),
                                        );
                                      },
                                      overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                      child: Padding(
                                        padding: const EdgeInsets.only(right: 14),
                                        child: Utility.imageLoader(
                                          url: image.imageUrl ?? '',
                                          placeholder: AppAssets.placeholderImage,
                                          borderRadius: BorderRadius.circular(8),
                                          height: 93,
                                          width: 93,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                            const Gap(10),
                            Text(state.advertisement.description ?? '', style: Theme.of(context).textTheme.titleSmall),
                            BlocSelector<AuthenticationBloc, AuthenticationState, UserModel>(
                              selector: (state) => state.user,
                              builder: (context, authUser) {
                                final showMessageButton = state.advertisement.user?.canSendMessage == 1;
                                final showCallButton = state.advertisement.user?.canCall == 1;
                                return Visibility(
                                  visible: authUser.id != state.advertisement.userId &&
                                      (showCallButton || showMessageButton),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Gap(14),
                                      const Divider(
                                        height: 0,
                                      ),
                                      const Gap(14),
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          if (showMessageButton)
                                            Expanded(
                                              child: CommonButton(
                                                onTap: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) => ChatDetailWrapperPage(
                                                        userId: state.advertisement.user?.id ?? 0,
                                                        advertisementId: state.advertisement.id,
                                                        advertisement: state.advertisement,
                                                      ),
                                                    ),
                                                  );
                                                },
                                                text: 'Message',
                                                removeShadow: true,
                                                height: 42,
                                                padding: EdgeInsets.zero,
                                                borderRadius: 200,
                                                fontSize: 16,
                                                icon: const AppSvgImage(AppAssets.messageIcon),
                                              ),
                                            ),
                                          if (showMessageButton && showCallButton) const Gap(15),
                                          if (showCallButton)
                                            Expanded(
                                              child: CommonButton(
                                                onTap: () {
                                                  UrlManager.openDialpad(state.advertisement.user?.mobileNumber);
                                                },
                                                text: 'Call',
                                                borderRadius: 200,
                                                fontSize: 16,
                                                height: 42,
                                                isLessShadow: true,
                                                padding: EdgeInsets.zero,
                                                icon: const AppSvgImage(AppAssets.callIcon),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ],
                                  ),
                                );
                              },
                            )
                          ],
                        ),
                      ),
                    ],
                  );
                },
                error: (error) => Center(
                  child: Utility.noDataWidget(context: context, text: error.failure?.message ?? 'Something went wrong'),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
