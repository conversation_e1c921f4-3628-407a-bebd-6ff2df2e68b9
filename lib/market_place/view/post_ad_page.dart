import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart';
import 'package:leiuniverse/market_place/view/ad_duration_view.dart';
import 'package:leiuniverse/market_place/view/target_buyers_view.dart';
import 'package:leiuniverse/market_place/view/target_location_view.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class PostAdPage extends StatefulWidget {
  const PostAdPage({super.key, this.advertisement});
  final Advertisemnet? advertisement;

  @override
  State<PostAdPage> createState() => _PostAdPageState();
}

class _PostAdPageState extends State<PostAdPage> {
  final _formKey = GlobalKey<FormState>();
  final adTitleController = TextEditingController();
  final adDescriptionController = TextEditingController();
  final priceController = TextEditingController();
  final contactNumberController = TextEditingController();
  final postAdModel = ValueNotifier<PostOrEventModel>(PostOrEventModel());

  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    setInitialValues();
  }

  @override
  void dispose() {
    adTitleController.dispose();
    adDescriptionController.dispose();
    priceController.dispose();
    contactNumberController.dispose();
    postAdModel.dispose();
    isButtonLoading.dispose();
    super.dispose();
  }

  void setInitialValues() {
    final user = context.read<AuthenticationBloc>().state.user;

    contactNumberController.text = user.mobileNumber ?? '';

    if (widget.advertisement == null) return;

    adTitleController.text = widget.advertisement!.title ?? '';
    adDescriptionController.text = widget.advertisement!.description ?? '';
    priceController.text = widget.advertisement!.price ?? '';
    postAdModel.value = postAdModel.value.copyWith(duration: widget.advertisement!.duration);

    if (widget.advertisement?.parentCategory != null) {
      postAdModel.value = postAdModel.value.copyWith(category: widget.advertisement!.parentCategory!);
    }
    if (widget.advertisement?.subCategory != null) {
      postAdModel.value = postAdModel.value.copyWith(subCategory: widget.advertisement!.subCategory!);
    }

    if (widget.advertisement?.categories != null) {
      for (var element in widget.advertisement!.categories) {
        postAdModel.value = postAdModel.value
            .copyWith(childSubCategory: [...postAdModel.value.childSubCategory ?? [], element.category!]);
      }
    }

    if (widget.advertisement?.images != null) {
      for (var element in widget.advertisement!.images) {
        postAdModel.value = postAdModel.value
            .copyWith(images: [...postAdModel.value.images ?? [], PickedFileModel(networkFile: element)]);
      }
    }
    if (widget.advertisement?.cities != null) {
      for (var element in widget.advertisement!.cities) {
        postAdModel.value = postAdModel.value
            .copyWith(cities: [...postAdModel.value.cities ?? [], if (element.city != null) element.city!]);
      }
    }

    if (widget.advertisement?.country != null) {
      postAdModel.value = postAdModel.value.copyWith(country: widget.advertisement!.country!);
    }
    if (widget.advertisement?.state != null) {
      postAdModel.value = postAdModel.value.copyWith(state: widget.advertisement!.state!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Post Ad',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Images of business or Service* - 5 max',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppColors.primary,
                          ),
                    ),
                    const Gap(20),
                    ValueListenableBuilder(
                        valueListenable: postAdModel,
                        builder: (_, postAdValues, __) {
                          return BusinessImageView(
                            maxImage: 5,
                            images: postAdValues.images ?? <PickedFileModel>[],
                            onDeletedImages: (value) {
                              postAdModel.value = postAdModel.value.copyWith(
                                deleteImages: [...postAdModel.value.deleteImages ?? [], value],
                              );
                            },
                            onImageSelected: (images) {
                              postAdModel.value = postAdModel.value.copyWith(
                                images: images,
                              );
                            },
                          );
                        }),
                    const Gap(20),
                    AppTextFormField(
                      title: 'Ad title',
                      controller: adTitleController,
                      inputFormatters: [LengthLimitingTextInputFormatter(50)],
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a title';
                        }
                        return null;
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Short description',
                      isRequired: true,
                      controller: adDescriptionController,
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a description';
                        }
                        return null;
                      },
                    ),
                    const Gap(14),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: AppTextFormField(
                            title: 'Price',
                            controller: priceController,
                            keyboardType: TextInputType.number,
                            isRequired: true,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(8),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a price';
                              }
                              return null;
                            },
                          ),
                        ),
                        const Gap(14),
                        Expanded(
                          child: AppTextFormField(
                            title: 'Contact number',
                            controller: contactNumberController,
                            enabled: contactNumberController.text.trim().isEmpty,
                            keyboardType: TextInputType.phone,
                            titleColor: AppColors.subText,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  color: AppColors.subText,
                                ),
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            isRequired: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a contact number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 0,
              ),
              ValueListenableBuilder(
                  valueListenable: postAdModel,
                  builder: (_, postAdValues, __) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TargetBuyersView(
                          title: 'Target Your Buyers',
                          postOrEventModel: postAdValues,
                          isOptional: true,
                          onModelUpdated: (targetBuyers) {
                            postAdModel.value = postAdModel.value.copyWith(
                              category: targetBuyers.category,
                              subCategory: targetBuyers.subCategory,
                              childSubCategory: targetBuyers.childSubCategory,
                            );
                          },
                        ),
                        const Divider(
                          height: 0,
                        ),
                        TargetLocationView(
                          postAdModel: postAdValues,
                          isOptional: true,
                          onModelUpdated: (targetLocation) {
                            postAdModel.value = postAdModel.value.copyWith(
                              country: targetLocation.country,
                              state: targetLocation.state,
                              cities: targetLocation.cities,
                            );
                          },
                        ),
                        const Divider(
                          height: 0,
                        ),
                        InkWell(
                          onTap: widget.advertisement == null
                              ? null
                              : () {
                                  Utility.toast(message: "Can't edit duration");
                                },
                          overlayColor: WidgetStatePropertyAll(AppColors.transparent),
                          child: IgnorePointer(
                            ignoring: widget.advertisement != null,
                            child: AdDurationView(
                              postAdModel: postAdValues,
                              onModelUpdated: (targetDuration) {
                                postAdModel.value = postAdModel.value.copyWith(
                                  duration: targetDuration.duration,
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    );
                  }),
              const Gap(10),
              ValueListenableBuilder(
                valueListenable: isButtonLoading,
                builder: (_, value, __) {
                  return CommonButton(
                    text: 'Pay & Publish Ad',
                    isLoading: value,
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    onTap: () {
                      if (postAdModel.value.images?.isEmpty ?? true) {
                        return Utility.toast(message: 'Please add at least one image');
                      }
                      if (!_formKey.currentState!.validate()) {
                        return;
                      }
                      if (widget.advertisement == null) {
                        createAdvertisement();
                        return;
                      }
                      updateAdvertisement();
                    },
                  );
                },
              ),
              const Gap(30),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> createAdvertisement() async {
    isButtonLoading.value = true;
    final childSubCategories = postAdModel.value.childSubCategory;
    final fallbackCategoryId = postAdModel.value.subCategory?.id ?? postAdModel.value.category?.id;

    final List<int> categoryIds = (childSubCategories != null && childSubCategories.isNotEmpty)
        ? childSubCategories.map((e) => e.id ?? 0).where((id) => id != 0).toList()
        : (fallbackCategoryId != null && fallbackCategoryId != 0)
            ? [fallbackCategoryId]
            : [];

    final response = await getIt<IMarketplaceRepository>().createAdvertisement(
      title: adTitleController.text.trim(),
      shortDescription: adDescriptionController.text.trim(),
      price: priceController.text.trim(),
      categoryIds: categoryIds,
      countryId: postAdModel.value.country?.id,
      stateId: postAdModel.value.state?.id,
      cityIds: postAdModel.value.cities?.map((e) => e.id ?? 0).where((id) => id != 0).toList() ?? [],
      images: postAdModel.value.images?.where((e) => e.file != null).map((e) => e.file!).toList() ?? <File>[],
      duration: postAdModel.value.duration ?? 7,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;

        Utility.toast(message: r.message);
        Navigator.pop(context, r.data);
      },
    );
  }

  Future<void> updateAdvertisement() async {
    isButtonLoading.value = true;
    final childSubCategories = postAdModel.value.childSubCategory;
    final fallbackCategoryId = postAdModel.value.subCategory?.id ?? postAdModel.value.category?.id;

    final List<int> categoryIds = (childSubCategories != null && childSubCategories.isNotEmpty)
        ? childSubCategories.map((e) => e.id ?? 0).where((id) => id != 0).toList()
        : (fallbackCategoryId != null && fallbackCategoryId != 0)
            ? [fallbackCategoryId]
            : [];
    final response = await getIt<IMarketplaceRepository>().updateAdvertisement(
      advertisementId: widget.advertisement!.id!,
      title: adTitleController.text.trim(),
      shortDescription: adDescriptionController.text.trim(),
      price: priceController.text.trim(),
      categoryIds: categoryIds,
      countryId: postAdModel.value.country?.id,
      stateId: postAdModel.value.state?.id,
      cityIds: postAdModel.value.cities?.map((e) => e.id ?? 0).toList() ?? [],
      images: postAdModel.value.images?.where((e) => e.file != null).map((e) => e.file!).toList() ?? <File>[],
      deleteImages: postAdModel.value.deleteImages?.map((e) => e).toList() ?? [],
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;

        Utility.toast(message: r.message);
        Navigator.pop(context, r.data);
      },
    );
  }
}
