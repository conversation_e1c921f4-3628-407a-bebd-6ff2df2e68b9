import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/city_dropdown.dart';
import 'package:leiuniverse/widget/country_dropdown.dart';
import 'package:leiuniverse/widget/state_dropdown.dart';

class TargetLocationView extends StatefulWidget {
  const TargetLocationView(
      {super.key, required this.postAdModel, required this.onModelUpdated, this.isOptional = false});
  final PostOrEventModel? postAdModel;
  final Function(PostOrEventModel) onModelUpdated;
  final bool isOptional;

  @override
  State<TargetLocationView> createState() => _TargetLocationViewState();
}

class _TargetLocationViewState extends State<TargetLocationView> {
  final selectedCountry = ValueNotifier<CountryModel?>(null);
  final selectedState = ValueNotifier<CountryModel?>(null);
  final selectedCity = ValueNotifier<List<CountryModel>>(<CountryModel>[]);

  @override
  void initState() {
    super.initState();
    selectedCountry.value = widget.postAdModel?.country;
    selectedState.value = widget.postAdModel?.state;
    selectedCity.value = [...widget.postAdModel?.cities ?? []];
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Target Location',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primary,
                ),
          ),
          const Gap(14),
          ValueListenableBuilder<CountryModel?>(
              valueListenable: selectedCountry,
              builder: (context, selected, _) {
                return CountryDropdown(
                  isAddAll: true,
                  key: ValueKey('${selected?.id}_CountryDropdown'),
                  isOptional: widget.isOptional,
                  selectedCountry: selected,
                  onCountryChanged: (country) {
                    selectedCountry.value = country;
                    selectedState.value = null;
                    selectedCity.value.clear();
                    widget.onModelUpdated(widget.postAdModel!.copyWith(country: country, state: null, cities: []));
                  },
                );
              }),
          const Gap(14),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ValueListenableBuilder<CountryModel?>(
                valueListenable: selectedState,
                builder: (context, selected, _) {
                  return ValueListenableBuilder<CountryModel?>(
                      valueListenable: selectedCountry,
                      builder: (context, country, _) {
                        return Expanded(
                          child: StateDropdown(
                            isAddAll: true,
                            key: ValueKey('${selected?.id}_StateDropdown_${country?.id}'),
                            countryId: country?.id,
                            isOptional: widget.isOptional,
                            selectedState: selected,
                            onStateChanged: (state) {
                              selectedCountry.value = selectedCountry.value;
                              selectedState.value = state;
                              selectedCity.value.clear();
                              widget.onModelUpdated(widget.postAdModel!.copyWith(state: state, cities: []));
                            },
                          ),
                        );
                      });
                },
              ),
              const Gap(15),
              ValueListenableBuilder<CountryModel?>(
                  valueListenable: selectedState,
                  builder: (context, state, _) {
                    return ValueListenableBuilder<List<CountryModel>>(
                        valueListenable: selectedCity,
                        builder: (context, cities, _) {
                          return Expanded(
                            child: CityDropdown(
                              isAddAll: true,
                              key: ValueKey('${cities.firstOrNull?.id}_CityDropdown_${state?.id}'),
                              isMulti: true,
                              isOptional: widget.isOptional,
                              stateId: state?.id,
                              onCityChanged: (city) {
                                log(city.toString());
                                widget.onModelUpdated(
                                  widget.postAdModel!.copyWith(cities: city),
                                );
                                selectedCity.value = city;
                              },
                              selectedCity: cities,
                            ),
                          );
                        });
                  }),
            ],
          )
        ],
      ),
    );
  }
}
