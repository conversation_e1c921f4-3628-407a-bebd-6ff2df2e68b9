import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';

class AdDurationView extends StatefulWidget {
  const AdDurationView({super.key, required this.postAdModel, required this.onModelUpdated});
  final PostOrEventModel? postAdModel;
  final Function(PostOrEventModel) onModelUpdated;

  @override
  State<AdDurationView> createState() => _AdDurationViewState();
}

class _AdDurationViewState extends State<AdDurationView> {
  final durationList = ValueNotifier<List<int>>([
    7,
    15,
    30,
  ]);
  late final selectedDuration = ValueNotifier<int>(widget.postAdModel?.duration ?? 7);

  @override
  void initState() {
    super.initState();
    log(widget.postAdModel?.duration.toString() ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Ad Duration',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primary,
                ),
          ),
          const Gap(14),
          ValueListenableBuilder<List<int>>(
            valueListenable: durationList,
            builder: (context, duration, _) {
              return ValueListenableBuilder<int?>(
                valueListenable: selectedDuration,
                builder: (context, selectValue, _) {
                  return AppDropDown(
                    isRequired: true,
                    onSelect: (value) {
                      selectedDuration.value = value ?? 7;
                      widget.onModelUpdated(widget.postAdModel!.copyWith(duration: value));
                    },
                    items: duration
                        .map((e) => DropdownMenuItem(value: e, child: Text(e == 7 ? '7 Day' : '$e Days')))
                        .toList(),
                    selectedValue: selectValue,
                    hintText: 'Select',
                    title: 'Ad duration',
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a duration';
                      }
                      return null;
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
