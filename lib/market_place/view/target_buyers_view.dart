import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/widget/category_dropdown.dart';

class TargetBuyersView extends StatefulWidget {
  const TargetBuyersView(
      {super.key,
      required this.postOrEventModel,
      required this.onModelUpdated,
      this.title,
      this.onCategoryChanged,
      this.isOptional = false});
  final PostOrEventModel? postOrEventModel;
  final Function(PostOrEventModel) onModelUpdated;
  final void Function(List<CategoryModel>? categories)? onCategoryChanged;
  final String? title;
  final bool isOptional;

  @override
  State<TargetBuyersView> createState() => _TargetBuyersViewState();
}

class _TargetBuyersViewState extends State<TargetBuyersView> {
  final selectedCategory = ValueNotifier<CategoryModel?>(null);
  final selectedSubCategory = ValueNotifier<CategoryModel?>(null);
  final selectedChildSubCategory = ValueNotifier<CategoryModel?>(null);

  @override
  void initState() {
    selectedCategory.value = widget.postOrEventModel?.category;
    selectedSubCategory.value = widget.postOrEventModel?.subCategory;
    selectedChildSubCategory.value = widget.postOrEventModel?.childSubCategory?.firstOrNull;
    super.initState();
  }

  @override
  void dispose() {
    selectedCategory.dispose();
    selectedSubCategory.dispose();
    selectedChildSubCategory.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.title ?? '',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.primary,
                ),
          ),
          const Gap(14),
          ValueListenableBuilder<CategoryModel?>(
            valueListenable: selectedCategory,
            builder: (context, selectedValue, _) {
              return CategoryDropdown(
                key: ValueKey('${selectedValue?.id}_CategoryDropdown'),
                title: 'Category',
                isOptional: widget.isOptional,
                onCategoryChanged: (categories) {
                  log(categories.toString());
                  selectedCategory.value = categories?.firstOrNull;
                  selectedSubCategory.value = null;
                  selectedChildSubCategory.value = null;
                  widget.onModelUpdated(widget.postOrEventModel!
                      .copyWith(category: categories?.firstOrNull, subCategory: null, childSubCategory: []));
                },
                selectedCategory: [if (selectedValue != null) selectedValue],
              );
            },
          ),
          const Gap(14),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: ValueListenableBuilder<CategoryModel?>(
                  valueListenable: selectedCategory,
                  builder: (context, selectedCategoryValue, _) {
                    return ValueListenableBuilder<CategoryModel?>(
                      valueListenable: selectedSubCategory,
                      builder: (context, selectedValue, _) {
                        return CategoryDropdown(
                          key: ValueKey('${selectedValue?.id}_SubCategoryDropdown_${selectedCategoryValue?.id}'),
                          title: 'Sub category',
                          isOptional: widget.isOptional,
                          parentCategoryName: 'Category',
                          parentId: selectedCategoryValue?.id,
                          onCategoryChanged: (categories) {
                            selectedSubCategory.value = categories?.firstOrNull;
                            selectedChildSubCategory.value = null;
                            widget.onModelUpdated(widget.postOrEventModel!
                                .copyWith(subCategory: categories?.firstOrNull, childSubCategory: []));
                          },
                          selectedCategory: [if (selectedValue != null) selectedValue],
                        );
                      },
                    );
                  },
                ),
              ),
              const Gap(15),
              Expanded(
                child: ValueListenableBuilder<CategoryModel?>(
                  valueListenable: selectedSubCategory,
                  builder: (context, selectedCategoryValue, _) {
                    return ValueListenableBuilder<CategoryModel?>(
                      valueListenable: selectedChildSubCategory,
                      builder: (context, selectedValue, _) {
                        return CategoryDropdown(
                          key: ValueKey('${selectedValue?.id}_ChildSubCategoryDropdown_${selectedCategoryValue?.id}'),
                          title: 'Child sub category',
                          parentCategoryName: 'Sub category',
                          isOptional: widget.isOptional,
                          parentId: selectedCategoryValue?.id,
                          isMulti: true,
                          onCategoryChanged: (categories) {
                            selectedChildSubCategory.value = categories?.firstOrNull;
                            widget.onCategoryChanged?.call(categories);
                            widget.onModelUpdated(widget.postOrEventModel!.copyWith(childSubCategory: categories));
                          },
                          selectedCategory: [if (selectedValue != null) selectedValue],
                        );
                      },
                    );
                  },
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
