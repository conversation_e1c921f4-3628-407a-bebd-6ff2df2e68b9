// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/chat/view/all_chat_list_page.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/market_place/bloc/marketplace_bloc.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/my_market/my_market_bloc.dart';
import 'package:leiuniverse/market_place/view/market_place_details_page.dart';
import 'package:leiuniverse/market_place/view/post_ad_page.dart';
import 'package:leiuniverse/market_place/widget/my_ad_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class MyAdPage extends StatelessWidget {
  const MyAdPage({super.key, this.marketplaceBloc});
  final MarketplaceBloc? marketplaceBloc;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<MyMarketBloc>()..add(const MyMarketAdvertisementLoad()),
        ),
        if (marketplaceBloc != null)
          BlocProvider.value(
            value: marketplaceBloc!,
          ),
      ],
      child: _MyAdPage(key: const Key('MyAdPage'), marketplaceBloc: marketplaceBloc),
    );
  }
}

class _MyAdPage extends StatefulWidget {
  const _MyAdPage({
    super.key,
    this.marketplaceBloc,
  });
  final MarketplaceBloc? marketplaceBloc;

  @override
  State<_MyAdPage> createState() => _MyAdPageState();
}

class _MyAdPageState extends State<_MyAdPage> with PaginationMixin {
  final searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    initiatePagination();
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'My Ads',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: BlocBuilder<MyMarketBloc, MyMarketState>(
          builder: (context, state) {
            return state.map(
              initial: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 50)),
              loading: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 50)),
              loaded: (state) {
                if (state.advertisements.isEmpty && state.searchQuery.isNotPureValid) {
                  return Utility.noDataWidget(context: context, text: 'No Advertisement Found');
                }
                return Column(
                  children: [
                    AppTextFormField(
                      controller: searchController,
                      hintText: 'Search',
                      fillColor: AppColors.white,
                      onChanged: (value) {
                        context.read<MyMarketBloc>().add(MyMarketAdvertisementSearch(query: value));
                      },
                      prefixIcon: const Padding(
                        padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                        child: AppSvgImage(
                          AppAssets.searchIcon,
                          color: AppColors.subText,
                        ),
                      ),
                    ),
                    if (state.searchLoading)
                      const Expanded(child: Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40)))
                    else if (state.advertisements.isEmpty && (state.searchQuery.isPureValid))
                      Flexible(child: Utility.noDataWidget(context: context, text: 'No Search Result Found'))
                    else
                      Flexible(
                        child: RefreshIndicator(
                          onRefresh: () {
                            context
                                .read<MyMarketBloc>()
                                .add(MyMarketAdvertisementSearch(query: searchController.text.trim()));
                            return Future.value();
                          },
                          child: Column(
                            children: [
                              ListView.separated(
                                separatorBuilder: (context, index) => const Gap(20),
                                itemCount: state.advertisements.length,
                                controller: scrollPaginationController,
                                shrinkWrap: true,
                                padding: const EdgeInsets.symmetric(vertical: 20),
                                physics: const AlwaysScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  final advertisement = state.advertisements[index];
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      MyAdWidget(
                                        onTap: () async {
                                          final refresh = await Navigator.push<MarkDetailUpdateData?>(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  MarketPlaceDetailsPage(advertisementId: advertisement.id!),
                                            ),
                                          );

                                          if (refresh != null && refresh.isForDelete && context.mounted) {
                                            context.read<MyMarketBloc>().add(MyMarketAdvertisementDelete(
                                                advertisementId: refresh.advertisement.id!));
                                            if (widget.marketplaceBloc != null) {
                                              context.read<MarketplaceBloc>().add(MarketplaceAdvertisementDelete(
                                                  advertisementId: refresh.advertisement.id!));
                                            }
                                            return;
                                          }
                                          if (refresh != null && context.mounted) {
                                            context
                                                .read<MyMarketBloc>()
                                                .add(MyMarketAdvertisementUpdate(advertisement: refresh.advertisement));
                                            if (widget.marketplaceBloc != null) {
                                              context.read<MarketplaceBloc>().add(
                                                  MarketplaceAdvertisementUpdate(advertisement: refresh.advertisement));
                                            }
                                          }
                                        },
                                        advertisement: advertisement,
                                        onMessageTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => ChatWrapperPage(
                                                advertisementId: advertisement.id!,
                                              ),
                                            ),
                                          );
                                        },
                                        onEditTap: () async {
                                          final refresh = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => PostAdPage(
                                                advertisement: advertisement,
                                              ),
                                            ),
                                          );

                                          if (refresh != null && refresh is Advertisemnet && context.mounted) {
                                            context.read<MyMarketBloc>().add(
                                                  MyMarketAdvertisementUpdate(advertisement: refresh),
                                                );

                                            if (widget.marketplaceBloc != null) {
                                              context
                                                  .read<MarketplaceBloc>()
                                                  .add(MarketplaceAdvertisementUpdate(advertisement: refresh));
                                            }
                                          }
                                        },
                                        onDeleteTap: () {
                                          AppDailogs.deleteAdvertisementDailog(
                                            context,
                                            advertisementId: advertisement.id!,
                                            onDelete: () {
                                              context
                                                  .read<MyMarketBloc>()
                                                  .add(MyMarketAdvertisementDelete(advertisementId: advertisement.id!));

                                              if (widget.marketplaceBloc != null) {
                                                context.read<MarketplaceBloc>().add(
                                                    MarketplaceAdvertisementDelete(advertisementId: advertisement.id!));
                                              }
                                            },
                                          );
                                        },
                                      ),
                                      if (state.isLoadingMore) ...[
                                        const Gap(20),
                                        const CustomProgressIndecator(
                                          color: AppColors.primary,
                                          size: 50,
                                        ),
                                      ]
                                    ],
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                );
              },
              error: (error) => Center(
                child: Utility.noDataWidget(context: context, text: error.failure?.message ?? 'Something went wrong'),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  void onReachedLast() {
    final state = context.read<MyMarketBloc>().state;
    if (state is! MyMarketLoaded) return;
    if (state.hasReachedMaxPage) return;
    if (state.isLoadingMore) return;
    context.read<MyMarketBloc>().add(const MyMarketAdvertisementLoadMore());
  }
}
