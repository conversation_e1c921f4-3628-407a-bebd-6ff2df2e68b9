import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/market_place/bloc/marketplace_bloc.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/view/market_place_details_page.dart';
import 'package:leiuniverse/market_place/view/my_ad_page.dart';
import 'package:leiuniverse/market_place/view/post_ad_page.dart';
import 'package:leiuniverse/market_place/widget/ad_post_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class MarketPlacePage extends StatelessWidget {
  const MarketPlacePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<MarketplaceBloc>()..add(const MarketplaceAdvertisementLoad()),
      child: const _MarketPlacePage(key: Key('MarketPlacePage')),
    );
  }
}

class _MarketPlacePage extends StatefulWidget {
  const _MarketPlacePage({super.key});

  @override
  State<_MarketPlacePage> createState() => _MarketPlacePageState();
}

class _MarketPlacePageState extends State<_MarketPlacePage> with PaginationMixin {
  final searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    initiatePagination();
  }

  @override
  void dispose() {
    searchController.dispose();
    disposePagination();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Market Place',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (___) => MyAdPage(
                    marketplaceBloc: context.read<MarketplaceBloc>(),
                  ),
                ),
              );
            },
            fontSize: 14,
            text: 'My Ads',
            removeShadow: true,
            width: 68,
            padding: const EdgeInsets.symmetric(
              vertical: 4,
            ),
            margin: const EdgeInsets.only(right: 24, top: 13, bottom: 13),
            borderRadius: 6,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: BlocBuilder<MarketplaceBloc, MarketplaceState>(
          builder: (context, state) {
            return state.map(
              initial: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 50)),
              loading: (_) => const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 50)),
              loaded: (state) {
                if (state.advertisements.isEmpty && state.searchQuery.isNotPureValid) {
                  return Utility.noDataWidget(context: context, text: 'No Advertisement Found');
                }
                return Column(
                  children: [
                    AppTextFormField(
                      controller: searchController,
                      hintText: 'Search',
                      fillColor: AppColors.white,
                      onChanged: (value) {
                        context.read<MarketplaceBloc>().add(MarketplaceSearch(query: value));
                      },
                      prefixIcon: const Padding(
                        padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                        child: AppSvgImage(
                          AppAssets.searchIcon,
                          color: AppColors.subText,
                        ),
                      ),
                    ),
                    if (state.searchLoading)
                      const Expanded(child: Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40)))
                    else if (state.advertisements.isEmpty &&
                        (state.searchQuery != null && state.searchQuery!.isNotEmpty))
                      Flexible(child: Utility.noDataWidget(context: context, text: 'No Search Result Found'))
                    else if (state.advertisements.isEmpty)
                      Flexible(child: Utility.noDataWidget(context: context, text: 'No Advertisement Found'))
                    else
                      Flexible(
                        child: RefreshIndicator(
                          onRefresh: () {
                            // context.read<MarketplaceBloc>().add(const MarketplaceAdvertisementLoad());
                            context.read<MarketplaceBloc>().add(MarketplaceSearch(query: searchController.text.trim()));
                            return Future.value();
                          },
                          child: ListView.separated(
                            separatorBuilder: (context, index) => const Gap(20),
                            controller: scrollPaginationController,
                            itemCount: state.advertisements.length,
                            shrinkWrap: true,
                            padding: const EdgeInsets.symmetric(vertical: 20),
                            physics: const AlwaysScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              final advertisement = state.advertisements[index];
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  AdPostWidget(
                                    advertisement: advertisement,
                                    onTap: () async {
                                      final refresh = await Navigator.push<MarkDetailUpdateData?>(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              MarketPlaceDetailsPage(advertisementId: advertisement.id!),
                                        ),
                                      );

                                      if (refresh != null && refresh.isForDelete && context.mounted) {
                                        log('refresh $refresh');
                                        context.read<MarketplaceBloc>().add(
                                            MarketplaceAdvertisementDelete(advertisementId: refresh.advertisement.id!));
                                        return;
                                      }
                                      if (refresh != null && context.mounted) {
                                        context
                                            .read<MarketplaceBloc>()
                                            .add(MarketplaceAdvertisementUpdate(advertisement: refresh.advertisement));
                                      }
                                    },
                                  ),
                                  if (state.isLoadingMore) ...[
                                    const Gap(20),
                                    const CustomProgressIndecator(
                                      color: AppColors.primary,
                                      size: 50,
                                    ),
                                  ]
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                  ],
                );
              },
              error: (error) => Center(
                child: Utility.noDataWidget(context: context, text: error.failure?.message ?? 'Something went wrong'),
              ),
            );
          },
        ),
      ),
      floatingActionButton: CommonButton(
        width: 119,
        borderRadius: 200,
        fontSize: 15,
        height: 40,
        isLessShadow: true,
        padding: EdgeInsets.zero,
        onTap: () async {
          final refresh = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PostAdPage(),
            ),
          );

          if (refresh != null && refresh is Advertisemnet && context.mounted) {
            context.read<MarketplaceBloc>().add(MarketplaceAdvertisementAdd(advertisement: refresh));
          }
        },
        icon: const AppSvgImage(
          AppAssets.postEventIcon,
        ),
        text: 'Post Ad',
      ),
    );
  }

  @override
  void onReachedLast() {
    final state = context.read<MarketplaceBloc>().state;
    if (state is! MarketplaceLoaded) return;
    if (state.hasReachedMaxPage) return;
    if (state.isLoadingMore) return;
    context.read<MarketplaceBloc>().add(const MarketplaceAdvertisementLoadMore());
  }
}
