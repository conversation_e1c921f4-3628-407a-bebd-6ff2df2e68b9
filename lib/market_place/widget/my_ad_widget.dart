// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class MyAdWidget extends StatelessWidget {
  const MyAdWidget({
    super.key,
    required this.advertisement,
    this.onTap,
    this.onMessageTap,
    this.onEditTap,
    this.onDeleteTap,
  });
  final Advertisemnet advertisement;
  final void Function()? onTap;
  final void Function()? onMessageTap;
  final void Function()? onEditTap;
  final void Function()? onDeleteTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              advertisement.title ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(4),
            Text(
              '\$${advertisement.price}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(14),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  advertisement.images.length,
                  (index) {
                    final image = advertisement.images[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: image.imageUrl ?? '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 93,
                        width: 93,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            const Gap(12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Text(
                    advertisement.validTillDate,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: advertisement.isExpired ? AppColors.subText : AppColors.primary,
                        ),
                  ),
                ),
                InkWell(
                  onTap: onMessageTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const AppSvgImage(AppAssets.messagesIcon),
                ),
                const Gap(16),
                InkWell(
                  onTap: onEditTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const AppSvgImage(AppAssets.editIcon),
                ),
                const Gap(16),
                InkWell(
                  onTap: onDeleteTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const AppSvgImage(AppAssets.deleteIcon),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
