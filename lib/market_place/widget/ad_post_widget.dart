// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class AdPostWidget extends StatelessWidget {
  const AdPostWidget({super.key, required this.advertisement, this.onTap});

  final Advertisemnet advertisement;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (advertisement.id == null) return;
        onTap?.call();
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              advertisement.title ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(4),
            Text(
              '\$${advertisement.price}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const Gap(14),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  advertisement.images.length,
                  (index) {
                    final image = advertisement.images[index];
                    return Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: image.imageUrl ?? '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 93,
                        width: 93,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            const Gap(12),
            UserInfoWidget(user: advertisement.user),
          ],
        ),
      ),
    );
  }
}
