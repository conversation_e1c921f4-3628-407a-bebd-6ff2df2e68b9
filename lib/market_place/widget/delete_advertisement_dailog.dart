import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart';
import 'package:leiuniverse/widget/common_button.dart';

class DeleteAdvertisementDialog extends StatefulWidget {
  const DeleteAdvertisementDialog({super.key, required this.id, this.onDelete});
  final int id;
  final void Function()? onDelete;

  @override
  State<DeleteAdvertisementDialog> createState() => _DeleteAdvertisementDialogState();
}

class _DeleteAdvertisementDialogState extends State<DeleteAdvertisementDialog> {
  final isButtonLoading = ValueNotifier(false);

  @override
  void dispose() {
    isButtonLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Delete Business',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(10),
            Text(
              'Are you sure you want to delete?',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(20),
            Row(
              children: [
                Expanded(
                  child: CommonButton(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    text: 'Cancel',
                  ),
                ),
                const Gap(15),
                Expanded(
                  child: ValueListenableBuilder<bool>(
                    valueListenable: isButtonLoading,
                    builder: (context, isActionLoading, __) {
                      return CommonButton(
                        isLoading: isActionLoading,
                        onTap: _deleteAdvertisement,
                        text: 'Delete',
                        removeShadow: true,
                      );
                    },
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Future<void> _deleteAdvertisement() async {
    isButtonLoading.value = true;
    final failOrSuccess = await getIt<IMarketplaceRepository>().deleteAdvertisement(advertisementId: widget.id);
    failOrSuccess.fold((l) {
      Utility.toast(message: l.message);
      isButtonLoading.value = false;
      return;
    }, (r) {
      Utility.toast(message: 'Deleted Successfully');
      isButtonLoading.value = false;
      Navigator.pop(context);
      widget.onDelete?.call();
    });
  }
}
