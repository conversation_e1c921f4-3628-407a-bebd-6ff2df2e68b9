import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/utils/bloc_tranformers.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart';

part 'my_market_event.dart';
part 'my_market_state.dart';

@injectable
class MyMarketBloc extends Bloc<MyMarketEvent, MyMarketState> {
  MyMarketBloc({
    required this.repository,
  }) : super(const MyMarketInitial()) {
    on<MyMarketAdvertisementLoad>(_onMyMarketAdvertisementLoad);
    on<MyMarketAdvertisementSearch>(_onMyMarketAdvertisementSearch, transformer: loadMore());
    on<MyMarketAdvertisementLoadMore>(_onMyMarketAdvertisementLoadMore, transformer: loadMore());
    on<MyMarketAdvertisementDelete>(_onMyMarketAdvertisementDelete);
    on<MyMarketAdvertisementUpdate>(_onMyMarketAdvertisementUpdate);
  }
  final IMarketplaceRepository repository;

  Future<void> _onMyMarketAdvertisementLoad(
    MyMarketAdvertisementLoad event,
    Emitter<MyMarketState> emit,
  ) async {
    emit(const MyMarketLoading());
    final response = await repository.getAdvertisementList(
      isOwn: 1,
    );

    emit(
      response.fold(
        MyMarketError.new,
        (advertisement) => MyMarketLoaded(
          advertisements: advertisement.data,
          hasReachedMaxPage: advertisement.data.length < 10,
        ),
      ),
    );
  }

  Future<void> _onMyMarketAdvertisementSearch(
    MyMarketAdvertisementSearch event,
    Emitter<MyMarketState> emit,
  ) async {
    if (state is! MyMarketLoaded) return;
    final newState = state as MyMarketLoaded;

    emit(newState.copyWith(searchLoading: true, searchFailure: null, searchQuery: event.query));

    final response = await repository.getAdvertisementList(
      page: 1,
      isOwn: 1,
      search: event.query,
    );

    emit(
      response.fold(
        (error) => newState.copyWith(searchLoading: false, searchFailure: error, searchQuery: event.query),
        (advertisement) => newState.copyWith(
          advertisements: advertisement.data,
          currentPage: 1,
          isLoadingMore: false,
          hasReachedMaxPage: advertisement.data.length < 10,
          searchLoading: false,
          searchQuery: event.query,
        ),
      ),
    );
  }

  Future<void> _onMyMarketAdvertisementLoadMore(
    MyMarketAdvertisementLoadMore event,
    Emitter<MyMarketState> emit,
  ) async {
    if (state is! MyMarketLoaded) return;
    final newState = state as MyMarketLoaded;

    emit(newState.copyWith(isLoadingMore: true, loadMoreFailure: null));

    final response = await repository.getAdvertisementList(
      page: newState.currentPage + 1,
      isOwn: 1,
      search: null,
    );

    emit(
      response.fold(
        (error) => newState.copyWith(loadMoreFailure: error, isLoadingMore: false),
        (advertisement) => newState.copyWith(
          advertisements: [...newState.advertisements, ...advertisement.data],
          currentPage: newState.currentPage + 1,
          isLoadingMore: false,
          hasReachedMaxPage: advertisement.data.length < 10,
        ),
      ),
    );
  }

  void _onMyMarketAdvertisementDelete(
    MyMarketAdvertisementDelete event,
    Emitter<MyMarketState> emit,
  ) {
    if (state is! MyMarketLoaded) return;
    final newState = state as MyMarketLoaded;

    emit(
      newState.copyWith(
        advertisements: [...newState.advertisements].where((e) => e.id != event.advertisementId).toList(),
      ),
    );
  }

  Future<void> _onMyMarketAdvertisementUpdate(
    MyMarketAdvertisementUpdate event,
    Emitter<MyMarketState> emit,
  ) async {
    if (state is! MyMarketLoaded) return;
    final newState = state as MyMarketLoaded;

    emit(newState.copyWith(
      advertisements: [...newState.advertisements].map((e) {
        if (e.id == event.advertisement.id) {
          return event.advertisement;
        }
        return e;
      }).toList(),
    ));
  }
}
