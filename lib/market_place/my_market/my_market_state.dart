part of 'my_market_bloc.dart';

sealed class MyMarketState extends Equatable {
  const MyMarketState();

  @override
  List<Object?> get props => [];

  R map<R>({
    required R Function(MyMarketInitial state) initial,
    required R Function(MyMarketLoading state) loading,
    required R Function(MyMarketLoaded state) loaded,
    required R Function(MyMarketError state) error,
  }) {
    return switch (this) {
      MyMarketInitial() => initial(this as MyMarketInitial),
      MyMarketLoading() => loading(this as MyMarketLoading),
      MyMarketLoaded() => loaded(this as MyMarketLoaded),
      MyMarketError() => error(this as MyMarketError),
    };
  }

  R? mapOrNull<R>({
    R? Function(MyMarketInitial state)? initial,
    R? Function(MyMarketLoading state)? loading,
    R? Function(MyMarketLoaded state)? loaded,
    R? Function(MyMarketError state)? error,
  }) {
    return switch (this) {
      MyMarketInitial() => initial?.call(this as MyMarketInitial),
      MyMarketLoading() => loading?.call(this as MyMarketLoading),
      MyMarketLoaded() => loaded?.call(this as MyMarketLoaded),
      MyMarketError() => error?.call(this as MyMarketError),
    };
  }

  R maybeMap<R>({
    required R Function() orElse,
    R? Function(MyMarketInitial state)? initial,
    R? Function(MyMarketLoading state)? loading,
    R? Function(MyMarketLoaded state)? loaded,
    R? Function(MyMarketError state)? error,
  }) {
    return switch (this) {
      MyMarketInitial() => initial?.call(this as MyMarketInitial) ?? orElse(),
      MyMarketLoading() => loading?.call(this as MyMarketLoading) ?? orElse(),
      MyMarketLoaded() => loaded?.call(this as MyMarketLoaded) ?? orElse(),
      MyMarketError() => error?.call(this as MyMarketError) ?? orElse(),
    };
  }
}

final class MyMarketInitial extends MyMarketState {
  const MyMarketInitial();

  @override
  String toString() {
    return 'MyMarketInitial()';
  }
}

final class MyMarketLoading extends MyMarketState {
  const MyMarketLoading();

  @override
  String toString() {
    return 'MyMarketLoading()';
  }
}

final class MyMarketLoaded extends MyMarketState {
  const MyMarketLoaded({
    this.advertisements = const <Advertisemnet>[],
    this.currentPage = 1,
    this.hasReachedMaxPage = false,
    this.isLoadingMore = false,
    this.loadMoreFailure,
    this.searchFailure,
    this.searchLoading = false,
    this.searchQuery,
    this.isActionLoading = false,
    this.actionFailure,
  });

  final List<Advertisemnet> advertisements;
  final int currentPage;
  final bool hasReachedMaxPage;
  final bool isLoadingMore;
  final bool searchLoading;
  final HttpFailure? searchFailure;
  final String? searchQuery;
  final HttpFailure? loadMoreFailure;
  final bool isActionLoading;
  final HttpFailure? actionFailure;

  MyMarketLoaded copyWith({
    List<Advertisemnet>? advertisements,
    int? currentPage,
    bool? hasReachedMaxPage,
    bool? isLoadingMore,
    HttpFailure? loadMoreFailure,
    HttpFailure? searchFailure,
    bool? searchLoading,
    String? searchQuery,
    bool? isActionLoading,
    HttpFailure? actionFailure,
  }) {
    return MyMarketLoaded(
      advertisements: advertisements ?? this.advertisements,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMaxPage: hasReachedMaxPage ?? this.hasReachedMaxPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreFailure: loadMoreFailure ?? this.loadMoreFailure,
      searchFailure: searchFailure ?? this.searchFailure,
      searchLoading: searchLoading ?? this.searchLoading,
      searchQuery: searchQuery ?? this.searchQuery,
      isActionLoading: isActionLoading ?? this.isActionLoading,
      actionFailure: actionFailure ?? this.actionFailure,
    );
  }

  @override
  List<Object?> get props => [
        advertisements,
        currentPage,
        hasReachedMaxPage,
        isLoadingMore,
        loadMoreFailure,
        searchFailure,
        searchLoading,
        searchQuery,
        isActionLoading,
        actionFailure,
      ];

  @override
  String toString() {
    return 'MyMarketLoaded(advertisements: $advertisements, currentPage: $currentPage, hasReachedMaxPage: $hasReachedMaxPage, isLoadingMore: $isLoadingMore, loadMoreFailure: $loadMoreFailure, searchFailure: $searchFailure, searchLoading: $searchLoading, searchQuery: $searchQuery, isActionLoading: $isActionLoading, actionFailure: $actionFailure)';
  }
}

final class MyMarketError extends MyMarketState {
  const MyMarketError(this.failure);
  final HttpFailure? failure;

  @override
  List<Object?> get props => [failure];

  @override
  String toString() {
    return 'MyMarketError(failure: $failure)';
  }
}
