part of 'my_market_bloc.dart';

sealed class MyMarketEvent extends Equatable {
  const MyMarketEvent();

  @override
  List<Object?> get props => [];
}

final class MyMarketAdvertisementLoad extends MyMarketEvent {
  const MyMarketAdvertisementLoad();

  @override
  String toString() {
    return 'MyMarketAdvertisementLoad()';
  }
}

final class MyMarketAdvertisementSearch extends MyMarketEvent {
  const MyMarketAdvertisementSearch({required this.query});
  final String? query;

  @override
  String toString() {
    return 'MyMarketAdvertisementSearch(query: $query)';
  }
}

final class MyMarketAdvertisementLoadMore extends MyMarketEvent {
  const MyMarketAdvertisementLoadMore();

  @override
  String toString() {
    return 'MyMarketAdvertisementLoadMore()';
  }
}

final class MyMarketAdvertisementDelete extends MyMarketEvent {
  const MyMarketAdvertisementDelete({required this.advertisementId});
  final int advertisementId;

  @override
  String toString() {
    return 'MyMarketAdvertisementDelete(advertisement: $advertisementId)';
  }
}

final class MyMarketAdvertisementUpdate extends MyMarketEvent {
  const MyMarketAdvertisementUpdate({required this.advertisement});
  final Advertisemnet advertisement;

  @override
  String toString() {
    return 'MyMarketAdvertisementUpdate(advertisement: $advertisement)';
  }
}
