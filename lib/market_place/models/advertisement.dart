import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/event_promotion/model/category_element_model.dart';
import 'package:leiuniverse/event_promotion/model/city_element_model.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/model/image_model.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class Advertisemnet extends Equatable {
  const Advertisemnet({
    this.id,
    this.userId,
    this.title,
    this.description,
    this.price,
    this.expiryDate,
    this.isActive,
    this.isFavourite,
    this.createdAt,
    this.updatedAt,
    this.images = const [],
    this.cities = const [],
    this.user,
    this.categories = const [],
    this.state,
    this.country,
    this.subCategory,
    this.parentCategory,
  });

  final int? id;
  final int? userId;
  final String? title;
  final String? description;
  final String? price;
  final DateTime? expiryDate;
  final int? isActive;
  final int? isFavourite;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ImageModel> images;
  final List<CityElementModel> cities;
  final UserModel? user;
  final List<CategoryElementModel> categories;
  final CountryModel? state;
  final CountryModel? country;
  final CategoryModel? subCategory;
  final CategoryModel? parentCategory;

  Advertisemnet copyWith({
    int? id,
    int? userId,
    String? title,
    String? description,
    String? price,
    DateTime? expiryDate,
    int? isActive,
    int? isFavourite,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<ImageModel>? images,
    List<CityElementModel>? cities,
    UserModel? user,
    List<CategoryElementModel>? categories,
    CountryModel? state,
    CountryModel? country,
    CategoryModel? subCategory,
    CategoryModel? parentCategory,
  }) {
    return Advertisemnet(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      expiryDate: expiryDate ?? this.expiryDate,
      isActive: isActive ?? this.isActive,
      isFavourite: isFavourite ?? this.isFavourite,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      images: images ?? this.images,
      cities: cities ?? this.cities,
      user: user ?? this.user,
      categories: categories ?? this.categories,
      state: state ?? this.state,
      country: country ?? this.country,
      subCategory: subCategory ?? this.subCategory,
      parentCategory: parentCategory ?? this.parentCategory,
    );
  }

  factory Advertisemnet.fromJson(Map<String, dynamic> json) {
    return Advertisemnet(
      id: json["id"],
      userId: json["user_id"],
      title: json["title"],
      description: json["description"],
      price: json["price"],
      expiryDate: DateTime.tryParse(json["expiry_date"] ?? ""),
      isActive: json["is_active"],
      isFavourite: json["is_favourited"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      images: json["images"] == null ? [] : List<ImageModel>.from(json["images"]!.map((x) => ImageModel.fromJson(x))),
      cities: json["cities"] == null
          ? []
          : List<CityElementModel>.from(json["cities"]!.map((x) => CityElementModel.fromJson(x))),
      user: json["user"] == null ? null : UserModel.fromJson(json["user"]),
      categories: json["categories"] == null
          ? []
          : List<CategoryElementModel>.from(json["categories"]!.map((x) => CategoryElementModel.fromJson(x))),
      state: json["state"] == null ? null : CountryModel.fromJson(json["state"]),
      country: json["country"] == null ? null : CountryModel.fromJson(json["country"]),
      subCategory: json["sub_category"] == null ? null : CategoryModel.fromJson(json["sub_category"]),
      parentCategory: json["parent_category"] == null ? null : CategoryModel.fromJson(json["parent_category"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "title": title,
        "description": description,
        "price": price,
        "expiry_date": expiryDate?.toIso8601String(),
        "is_active": isActive,
        "is_favourited": isFavourite,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "images": images.map((x) => x.toJson()).toList(),
        "cities": cities.map((x) => x.toJson()).toList(),
        "user": user?.toJson(),
        "categories": categories.map((x) => x.toJson()).toList(),
        "state": state?.toJson(),
        "country": country?.toJson(),
        "sub_category": subCategory?.toJson(),
        "parent_category": parentCategory?.toJson(),
      };

  @override
  String toString() {
    return 'Advertisemnet(id: $id, userId: $userId, title: $title, description: $description, price: $price, expiryDate: $expiryDate, isActive: $isActive, createdAt: $createdAt, updatedAt: $updatedAt, images: $images, cities: $cities, user: $user, categories: $categories, state: $state, country: $country, subCategory: $subCategory, parentCategory: $parentCategory, isFavourite: $isFavourite)';
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        title,
        description,
        price,
        expiryDate,
        isActive,
        isFavourite,
        createdAt,
        updatedAt,
        images,
        cities,
        user,
        categories,
        state,
        country,
        subCategory,
        parentCategory,
      ];

  bool get isExpired => expiryDate != null && expiryDate!.isBefore(DateTime.now());

  String get advLocation {
    final city = cities.firstOrNull?.city?.name;
    final stateName = state?.name;
    final countryName = country?.name;
    final List<String> location = [];
    if (city != null) location.add(city);
    if (stateName != null) location.add(stateName);
    if (countryName != null) location.add(countryName);
    return location.join(', ');
  }

  String get validTillDate {
    if (expiryDate == null) return "";

    if (isExpired) {
      return 'Expired on ${DateFormat('dd/MM/yyyy').format(expiryDate!.toLocal())}';
    }

    return 'Valid till ${DateFormat('dd/MM/yyyy').format(expiryDate!.toLocal())}';
  }

  int get duration => expiryDate == null || createdAt == null ? 7 : expiryDate!.difference(createdAt!).inDays;
}
