import 'package:equatable/equatable.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';

class AdvertisementListResponse extends Equatable {
  const AdvertisementListResponse({
    this.status,
    this.message,
    this.data = const <Advertisemnet>[],
  });

  final int? status;
  final String? message;
  final List<Advertisemnet> data;

  AdvertisementListResponse copyWith({
    int? status,
    String? message,
    List<Advertisemnet>? data,
  }) {
    return AdvertisementListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory AdvertisementListResponse.fromJson(Map<String, dynamic> json) {
    return AdvertisementListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<Advertisemnet>.from(json["data"]!.map((x) => Advertisemnet.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data,
      };

  @override
  String toString() {
    return 'AdvertisementListResponse(status: $status, message: $message, data: $data)';
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
