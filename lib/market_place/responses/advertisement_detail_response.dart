import 'package:equatable/equatable.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';

class AdvertisementDetailResponse extends Equatable {
  const AdvertisementDetailResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final Advertisemnet? data;

  AdvertisementDetailResponse copyWith({
    int? status,
    String? message,
    Advertisemnet? data,
  }) {
    return AdvertisementDetailResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory AdvertisementDetailResponse.fromJson(Map<String, dynamic> json) {
    return AdvertisementDetailResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : Advertisemnet.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  String toString() {
    return 'AdvertisementDetailResponse(status: $status, message: $message, data: $data)';
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
