part of 'marketplace_detail_bloc.dart';

sealed class MarketplaceDetailState extends Equatable {
  const MarketplaceDetailState();

  @override
  List<Object?> get props => [];

  R map<R>({
    required R Function(MarketplaceDetailInitial state) initial,
    required R Function(MarketplaceDetailLoading state) loading,
    required R Function(MarketplaceDetailLoaded state) loaded,
    required R Function(MarketplaceDetailError state) error,
  }) {
    return switch (this) {
      MarketplaceDetailInitial() => initial(this as MarketplaceDetailInitial),
      MarketplaceDetailLoading() => loading(this as MarketplaceDetailLoading),
      MarketplaceDetailLoaded() => loaded(this as MarketplaceDetailLoaded),
      MarketplaceDetailError() => error(this as MarketplaceDetailError),
    };
  }

  R? mapOrNull<R>({
    R? Function(MarketplaceDetailInitial state)? initial,
    R? Function(MarketplaceDetailLoading state)? loading,
    R? Function(MarketplaceDetailLoaded state)? loaded,
    R? Function(MarketplaceDetailError state)? error,
  }) {
    return switch (this) {
      MarketplaceDetailInitial() => initial?.call(this as MarketplaceDetailInitial),
      MarketplaceDetailLoading() => loading?.call(this as MarketplaceDetailLoading),
      MarketplaceDetailLoaded() => loaded?.call(this as MarketplaceDetailLoaded),
      MarketplaceDetailError() => error?.call(this as MarketplaceDetailError),
    };
  }

  R maybeMap<R>({
    required R Function() orElse,
    R? Function(MarketplaceDetailInitial state)? initial,
    R? Function(MarketplaceDetailLoading state)? loading,
    R? Function(MarketplaceDetailLoaded state)? loaded,
    R? Function(MarketplaceDetailError state)? error,
  }) {
    return switch (this) {
      MarketplaceDetailInitial() => initial?.call(this as MarketplaceDetailInitial) ?? orElse(),
      MarketplaceDetailLoading() => loading?.call(this as MarketplaceDetailLoading) ?? orElse(),
      MarketplaceDetailLoaded() => loaded?.call(this as MarketplaceDetailLoaded) ?? orElse(),
      MarketplaceDetailError() => error?.call(this as MarketplaceDetailError) ?? orElse(),
    };
  }
}

final class MarketplaceDetailInitial extends MarketplaceDetailState {
  const MarketplaceDetailInitial();

  @override
  String toString() {
    return 'MarketplaceDetailInitial()';
  }
}

final class MarketplaceDetailLoading extends MarketplaceDetailState {
  const MarketplaceDetailLoading();

  @override
  String toString() {
    return 'MarketplaceDetailLoading()';
  }
}

final class MarketplaceDetailLoaded extends MarketplaceDetailState {
  final Advertisemnet advertisement;
  final HttpFailure? actionFailure;
  const MarketplaceDetailLoaded({required this.advertisement, this.actionFailure});

  @override
  List<Object?> get props => [advertisement, actionFailure];

  @override
  String toString() {
    return 'MarketplaceDetailLoaded(advertisement: $advertisement, likeFailure: $actionFailure)';
  }

  MarketplaceDetailLoaded copyWith({
    Advertisemnet? advertisement,
    HttpFailure? actionFailure,
  }) {
    return MarketplaceDetailLoaded(
      advertisement: advertisement ?? this.advertisement,
      actionFailure: actionFailure ?? actionFailure,
    );
  }
}

final class MarketplaceDetailError extends MarketplaceDetailState {
  final HttpFailure? failure;
  const MarketplaceDetailError({this.failure});

  @override
  List<Object?> get props => [failure];

  @override
  String toString() {
    return 'MarketplaceDetailError(failure: $failure)';
  }
}
