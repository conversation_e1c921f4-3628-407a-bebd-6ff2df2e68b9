part of 'marketplace_detail_bloc.dart';

sealed class MarketplaceDetailEvent extends Equatable {
  const MarketplaceDetailEvent();

  @override
  List<Object?> get props => [];
}

final class MarketplaceDetailLoad extends MarketplaceDetailEvent {
  const MarketplaceDetailLoad();

  @override
  String toString() {
    return 'MarketplaceDetailLoad()';
  }
}

final class MarketplaceDetailLikeToggle extends MarketplaceDetailEvent {
  const MarketplaceDetailLikeToggle(this.id);
  final int id;

  @override
  String toString() {
    return 'MarketplaceDetailLikeToggle(id: $id)';
  }

  @override
  List<Object?> get props => [id];
}

final class MarketplaceDetailUpdate extends MarketplaceDetailEvent {
  const MarketplaceDetailUpdate({required this.advertisement});
  final Advertisemnet advertisement;

  @override
  String toString() {
    return 'MarketplaceDetailUPdate(advertisement: $advertisement)';
  }

  @override
  List<Object?> get props => [advertisement];
}

final class MarketplaceDetailFavouriteToggle extends MarketplaceDetailEvent {
  const MarketplaceDetailFavouriteToggle();

  @override
  String toString() {
    return 'MarketplaceDetailFavouriteToggle()';
  }
}
