import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';

part 'marketplace_detail_event.dart';
part 'marketplace_detail_state.dart';

@injectable
class MarketplaceDetailBloc extends Bloc<MarketplaceDetailEvent, MarketplaceDetailState> {
  MarketplaceDetailBloc({
    required this.repository,
    required this.userRepository,
    @factoryParam required int advertisementId,
  }) : super(const MarketplaceDetailInitial()) {
    _advertisementId = advertisementId;
    on<MarketplaceDetailLoad>(_onMarketplaceDetailLoad);
    on<MarketplaceDetailLikeToggle>(_onMarketplaceDetailLikeToggle);
    on<MarketplaceDetailUpdate>(_onMarketplaceDetailUpdate);
    on<MarketplaceDetailFavouriteToggle>(_onMarketplaceDetailFavouriteToggle);
  }
  final IMarketplaceRepository repository;
  final IUserRepository userRepository;
  late int _advertisementId;

  Future<void> _onMarketplaceDetailLoad(
    MarketplaceDetailLoad event,
    Emitter<MarketplaceDetailState> emit,
  ) async {
    emit(const MarketplaceDetailLoading());
    final response = await repository.detailAdvertisement(_advertisementId);

    emit(
      response.fold(
        (l) => MarketplaceDetailError(failure: l),
        (r) => MarketplaceDetailLoaded(advertisement: r.data!),
      ),
    );
  }

  Future<void> _onMarketplaceDetailLikeToggle(
    MarketplaceDetailLikeToggle event,
    Emitter<MarketplaceDetailState> emit,
  ) async {
    if (state is! MarketplaceDetailLoaded) return;
    final newState = state as MarketplaceDetailLoaded;

    final updatedUser = newState.advertisement.user?.copyWith(
      isLiked: newState.advertisement.user?.isLiked == 1 ? 0 : 1,
      likesCount: newState.advertisement.user?.isLiked == 1
          ? newState.advertisement.user!.likesCount! - 1
          : newState.advertisement.user!.likesCount! + 1,
    );

    if (updatedUser == null) return;

    emit(newState.copyWith(advertisement: newState.advertisement.copyWith(user: updatedUser), actionFailure: null));

    final response = await userRepository.likeUser(id: updatedUser.id);

    if (response.isLeft()) {
      response
          .getLeft()
          .fold(() {}, (l) => emit(newState.copyWith(actionFailure: l, advertisement: newState.advertisement)));
    }
  }

  void _onMarketplaceDetailUpdate(
    MarketplaceDetailUpdate event,
    Emitter<MarketplaceDetailState> emit,
  ) {
    if (state is! MarketplaceDetailLoaded) return;
    final newState = state as MarketplaceDetailLoaded;
    emit(newState.copyWith(advertisement: event.advertisement));
  }

  Future<void> _onMarketplaceDetailFavouriteToggle(
    MarketplaceDetailFavouriteToggle event,
    Emitter<MarketplaceDetailState> emit,
  ) async {
    if (state is! MarketplaceDetailLoaded) return;
    final newState = state as MarketplaceDetailLoaded;

    final updatedAdvertisement =
        newState.advertisement.copyWith(isFavourite: newState.advertisement.isFavourite == 0 ? 1 : 0);

    emit(newState.copyWith(advertisement: updatedAdvertisement));

    final response = await repository.favouriteAdvertisement(updatedAdvertisement.id!);

    if (response.isLeft()) {
      response
          .getLeft()
          .fold(() {}, (l) => emit(newState.copyWith(actionFailure: l, advertisement: newState.advertisement)));
    }
  }
}
