import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/file_extention.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/market_place/responses/advertisement_detail_response.dart';
import 'package:leiuniverse/market_place/responses/advertisement_list_response.dart';

part 'marketplace_repository.dart';

abstract class IMarketplaceRepository {
  IMarketplaceRepository(this.client);
  final Client client;

  ApiResult<AdvertisementListResponse> getAdvertisementList({
    int page = 1,
    int perPage = 10,
    String? search,
    int? isOwn,
  });

  ApiResult<AdvertisementDetailResponse> createAdvertisement({
    required String title,
    required String shortDescription,
    required String price,
    int? countryId,
    int? stateId,
    List<int> categoryIds = const [],
    List<int> cityIds = const [],
    List<File> images = const [],
    int duration = 1,
  });

  ApiResult<AdvertisementDetailResponse> updateAdvertisement({
    required int advertisementId,
    String? title,
    String? shortDescription,
    String? price,
    int? countryId,
    int? stateId,
    List<int> categoryIds = const [],
    List<int> cityIds = const [],
    List<File> images = const [],
    List<int> deleteImages = const <int>[],
  });

  ApiResult<AdvertisementDetailResponse> detailAdvertisement(int advertisementId);

  ApiResult<CommonResponse> deleteAdvertisement({required int advertisementId});

  ApiResult<CommonResponse> favouriteAdvertisement(int advertisementId);
}
