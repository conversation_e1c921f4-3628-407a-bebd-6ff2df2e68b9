part of 'i_marketplace_repository.dart';

@Injectable(as: IMarketplaceRepository)
class MarketplaceRepository extends IMarketplaceRepository {
  MarketplaceRepository(super.client);

  @override
  ApiResult<AdvertisementListResponse> getAdvertisementList({
    int page = 1,
    int perPage = 10,
    String? search,
    int? isOwn,
  }) async {
    final response = await client.get(url: AppStrings.advertisementList, params: {
      'page': page.toString(),
      'per_page': perPage.toString(),
      if (search != null && search.trim().isNotEmpty) 'search': search,
      if (isOwn != null) 'is_owned': isOwn.toString(),
    });

    return response.parseResponse(AdvertisementListResponse.fromJson);
  }

  @override
  ApiResult<AdvertisementDetailResponse> createAdvertisement({
    required String title,
    required String shortDescription,
    required String price,
    int? countryId,
    int? stateId,
    List<int> categoryIds = const [],
    List<int> cityIds = const [],
    List<File> images = const [],
    int duration = 1,
  }) async {
    final response = await client.multipart(
      url: AppStrings.createAdvertisement,
      files: [
        if (images.isNotEmpty)
          for (int i = 0; i < images.length; i++) ...[
            MapEntry('images[$i]', await images[i].compressFile()),
          ],
      ],
      requests: {
        'title': title.trim(),
        'description': shortDescription.trim(),
        'price': price.trim(),
        if (countryId != null) 'country_id': countryId.toString(),
        if (stateId != null) 'state_id': stateId.toString(),
        if (categoryIds.isNotEmpty)
          for (int i = 0; i < categoryIds.length; i++) 'category_ids[$i]': categoryIds[i].toString(),
        if (cityIds.isNotEmpty)
          for (int i = 0; i < cityIds.length; i++) 'city_ids[$i]': cityIds[i].toString(),
        'duration': duration.toString(),
      },
    );

    return response.parseResponse(AdvertisementDetailResponse.fromJson);
  }

  @override
  ApiResult<AdvertisementDetailResponse> detailAdvertisement(int advertisementId) async {
    final response = await client.get(url: AppStrings.detailAdvertisement(advertisementId));

    return response.parseResponse(AdvertisementDetailResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteAdvertisement({required int advertisementId}) async {
    final response = await client.delete(url: AppStrings.deleteAdvertisement(advertisementId));
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<AdvertisementDetailResponse> updateAdvertisement(
      {required int advertisementId,
      String? title,
      String? shortDescription,
      String? price,
      int? countryId,
      int? stateId,
      List<int> categoryIds = const [],
      List<int> cityIds = const [],
      List<File> images = const [],
      List<int> deleteImages = const <int>[]}) async {
    final response = await client.multipart(
      url: AppStrings.updateAdvertisement(advertisementId),
      files: [
        if (images.isNotEmpty)
          for (int i = 0; i < images.length; i++) ...[
            MapEntry('images[$i]', await images[i].compressFile()),
          ],
      ],
      requests: {
        if (title.isPureValid) 'title': title!.trim(),
        if (shortDescription.isPureValid) 'description': shortDescription!.trim(),
        if (price != null) 'price': price.trim(),
        if (countryId != null) 'country_id': countryId.toString(),
        if (stateId != null) 'state_id': stateId.toString(),
        if (categoryIds.isNotEmpty)
          for (int i = 0; i < categoryIds.length; i++) 'category_ids[$i]': categoryIds[i].toString(),
        if (cityIds.isNotEmpty)
          for (int i = 0; i < cityIds.length; i++) 'city_ids[$i]': cityIds[i].toString(),
        if (deleteImages.isNotEmpty)
          for (int i = 0; i < deleteImages.length; i++) 'deleted_images[$i]': deleteImages[i].toString(),
      },
    );

    return response.parseResponse(AdvertisementDetailResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> favouriteAdvertisement(int advertisementId) async {
    final response = await client.post(url: AppStrings.toogleFavorite, requests: {
      'favourite_ad': advertisementId.toString(),
    });
    return response.parseResponse(CommonResponse.fromJson);
  }
}
