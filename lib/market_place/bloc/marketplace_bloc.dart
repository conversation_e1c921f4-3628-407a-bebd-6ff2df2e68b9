import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/utils/bloc_tranformers.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/market_place/models/advertisement.dart';
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart';

part 'marketplace_event.dart';
part 'marketplace_state.dart';

@injectable
class MarketplaceBloc extends Bloc<MarketplaceEvent, MarketplaceState> {
  MarketplaceBloc({required this.repository}) : super(const MarketplaceInitial()) {
    on<MarketplaceAdvertisementLoad>(_onMarketplaceAdvertisementLoad);
    on<MarketplaceSearch>(_onMarketplaceSearch, transformer: loadMore());
    on<MarketplaceAdvertisementLoadMore>(_onMarketplaceAdvertisementLoadMore, transformer: loadMore());
    on<MarketplaceAdvertisementAdd>(_onMarketplaceAdvertisementAdd);
    on<MarketplaceAdvertisementUpdate>(_onMarketplaceAdvertisementUpdate);
    on<MarketplaceAdvertisementDelete>(_onMarketplaceAdvertisementDelete);
  }
  final IMarketplaceRepository repository;

  Future<void> _onMarketplaceAdvertisementLoad(
    MarketplaceAdvertisementLoad event,
    Emitter<MarketplaceState> emit,
  ) async {
    emit(const MarketplaceLoading());
    final response = await repository.getAdvertisementList();

    emit(
      response.fold(
        MarketplaceError.new,
        (advertisement) => MarketplaceLoaded(
          advertisements: advertisement.data,
          hasReachedMaxPage: advertisement.data.length < 10,
        ),
      ),
    );
  }

  Future<void> _onMarketplaceSearch(
    MarketplaceSearch event,
    Emitter<MarketplaceState> emit,
  ) async {
    if (state is! MarketplaceLoaded) return;
    final newState = state as MarketplaceLoaded;

    emit(newState.copyWith(searchLoading: true, searchFailure: null, searchQuery: event.query));

    final response = await repository.getAdvertisementList(
      page: 1,
      search: event.query,
    );

    emit(
      response.fold(
        (error) => newState.copyWith(searchLoading: false, searchFailure: error, searchQuery: event.query),
        (advertisement) => newState.copyWith(
          advertisements: advertisement.data,
          currentPage: 1,
          isLoadingMore: false,
          hasReachedMaxPage: advertisement.data.length < 10,
          searchLoading: false,
          searchQuery: event.query,
        ),
      ),
    );
  }

  Future<void> _onMarketplaceAdvertisementLoadMore(
    MarketplaceAdvertisementLoadMore event,
    Emitter<MarketplaceState> emit,
  ) async {
    if (state is! MarketplaceLoaded) return;
    final newState = state as MarketplaceLoaded;

    emit(newState.copyWith(isLoadingMore: true, loadMoreFailure: null));

    final response = await repository.getAdvertisementList(
      page: newState.currentPage + 1,
      search: newState.searchQuery,
    );

    emit(
      response.fold(
        (error) => newState.copyWith(loadMoreFailure: error, isLoadingMore: false),
        (advertisement) => newState.copyWith(
          advertisements: [...newState.advertisements, ...advertisement.data],
          currentPage: newState.currentPage + 1,
          isLoadingMore: false,
          hasReachedMaxPage: advertisement.data.length < 10,
        ),
      ),
    );
  }

  void _onMarketplaceAdvertisementAdd(
    MarketplaceAdvertisementAdd event,
    Emitter<MarketplaceState> emit,
  ) {
    if (state is! MarketplaceLoaded) return;
    final newState = state as MarketplaceLoaded;

    emit(newState.copyWith(
      advertisements: [
        event.advertisement,
        ...newState.advertisements,
      ],
    ));
  }

  void _onMarketplaceAdvertisementUpdate(
    MarketplaceAdvertisementUpdate event,
    Emitter<MarketplaceState> emit,
  ) {
    if (state is! MarketplaceLoaded) return;
    final newState = state as MarketplaceLoaded;

    final isSameModel =
        newState.advertisements.firstWhereOrNull((element) => element.id == event.advertisement.id).hashCode ==
            event.advertisement.hashCode;

    if (isSameModel) return;

    emit(newState.copyWith(
      advertisements: [
        ...newState.advertisements,
      ].map((e) => e.id == event.advertisement.id ? event.advertisement : e).toList(),
    ));
  }

  void _onMarketplaceAdvertisementDelete(
    MarketplaceAdvertisementDelete event,
    Emitter<MarketplaceState> emit,
  ) {
    if (state is! MarketplaceLoaded) return;
    final newState = state as MarketplaceLoaded;

    emit(newState.copyWith(
      advertisements: [
        ...newState.advertisements.where((e) => e.id != event.advertisementId),
      ],
    ));
  }
}
