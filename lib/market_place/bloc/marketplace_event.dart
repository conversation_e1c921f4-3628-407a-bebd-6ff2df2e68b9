part of 'marketplace_bloc.dart';

sealed class MarketplaceEvent extends Equatable {
  const MarketplaceEvent();

  @override
  List<Object?> get props => [];
}

final class MarketplaceAdvertisementLoad extends MarketplaceEvent {
  const MarketplaceAdvertisementLoad();

  @override
  String toString() {
    return 'MarketplaceAdvertisementLoad()';
  }
}

final class MarketplaceAdvertisementLoadMore extends MarketplaceEvent {
  const MarketplaceAdvertisementLoadMore();

  @override
  String toString() {
    return 'MarketplaceAdvertisementLoadMore()';
  }
}

final class MarketplaceSearch extends MarketplaceEvent {
  const MarketplaceSearch({required this.query});
  final String? query;

  @override
  String toString() {
    return 'MarketplaceSearch(query: $query)';
  }
}

final class MarketplaceAdvertisementAdd extends MarketplaceEvent {
  const MarketplaceAdvertisementAdd({required this.advertisement});
  final Advertisemnet advertisement;

  @override
  String toString() {
    return 'MarketplaceAdvertisementAdd(advertisement: $advertisement)';
  }
}

final class MarketplaceAdvertisementUpdate extends MarketplaceEvent {
  const MarketplaceAdvertisementUpdate({required this.advertisement});
  final Advertisemnet advertisement;

  @override
  String toString() {
    return 'MarketplaceAdvertisementUpdate(advertisement: $advertisement)';
  }
}

final class MarketplaceAdvertisementDelete extends MarketplaceEvent {
  const MarketplaceAdvertisementDelete({required this.advertisementId});
  final int advertisementId;

  @override
  String toString() {
    return 'MarketplaceAdvertisementDelete(advertisement: $advertisementId)';
  }
}
