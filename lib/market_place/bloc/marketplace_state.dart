// ignore_for_file: public_member_api_docs, sort_constructors_first
part of 'marketplace_bloc.dart';

sealed class MarketplaceState extends Equatable {
  const MarketplaceState();

  @override
  List<Object?> get props => [];

  R map<R>({
    required R Function(MarketplaceInitial state) initial,
    required R Function(MarketplaceLoading state) loading,
    required R Function(MarketplaceLoaded state) loaded,
    required R Function(MarketplaceError state) error,
  }) {
    return switch (this) {
      MarketplaceInitial() => initial(this as MarketplaceInitial),
      MarketplaceLoading() => loading(this as MarketplaceLoading),
      MarketplaceLoaded() => loaded(this as MarketplaceLoaded),
      MarketplaceError() => error(this as MarketplaceError),
    };
  }

  R? mapOrNull<R>({
    R? Function(MarketplaceInitial state)? initial,
    R? Function(MarketplaceLoading state)? loading,
    R? Function(MarketplaceLoaded state)? loaded,
    R? Function(MarketplaceError state)? error,
  }) {
    return switch (this) {
      MarketplaceInitial() => initial?.call(this as MarketplaceInitial),
      MarketplaceLoading() => loading?.call(this as MarketplaceLoading),
      MarketplaceLoaded() => loaded?.call(this as MarketplaceLoaded),
      MarketplaceError() => error?.call(this as MarketplaceError),
    };
  }

  R maybeMap<R>({
    required R Function() orElse,
    R? Function(MarketplaceInitial state)? initial,
    R? Function(MarketplaceLoading state)? loading,
    R? Function(MarketplaceLoaded state)? loaded,
    R? Function(MarketplaceError state)? error,
  }) {
    return switch (this) {
      MarketplaceInitial() => initial?.call(this as MarketplaceInitial) ?? orElse(),
      MarketplaceLoading() => loading?.call(this as MarketplaceLoading) ?? orElse(),
      MarketplaceLoaded() => loaded?.call(this as MarketplaceLoaded) ?? orElse(),
      MarketplaceError() => error?.call(this as MarketplaceError) ?? orElse(),
    };
  }
}

final class MarketplaceInitial extends MarketplaceState {
  const MarketplaceInitial();

  @override
  String toString() {
    return 'MarketplaceInitial()';
  }
}

final class MarketplaceLoading extends MarketplaceState {
  const MarketplaceLoading();

  @override
  String toString() {
    return 'MarketplaceLoading()';
  }
}

final class MarketplaceLoaded extends MarketplaceState {
  const MarketplaceLoaded({
    this.advertisements = const <Advertisemnet>[],
    this.currentPage = 1,
    this.hasReachedMaxPage = false,
    this.isLoadingMore = false,
    this.loadMoreFailure,
    this.searchFailure,
    this.searchQuery,
    this.searchLoading = false,
  });
  final List<Advertisemnet> advertisements;
  final int currentPage;
  final bool hasReachedMaxPage;
  final bool isLoadingMore;
  final bool searchLoading;
  final HttpFailure? searchFailure;
  final String? searchQuery;
  final HttpFailure? loadMoreFailure;

  MarketplaceLoaded copyWith({
    List<Advertisemnet>? advertisements,
    int? currentPage,
    bool? hasReachedMaxPage,
    bool? isLoadingMore,
    HttpFailure? loadMoreFailure,
    HttpFailure? searchFailure,
    bool? searchLoading,
    String? searchQuery,
  }) {
    return MarketplaceLoaded(
      advertisements: advertisements ?? this.advertisements,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMaxPage: hasReachedMaxPage ?? this.hasReachedMaxPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreFailure: loadMoreFailure ?? this.loadMoreFailure,
      searchFailure: searchFailure ?? this.searchFailure,
      searchLoading: searchLoading ?? this.searchLoading,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        advertisements,
        currentPage,
        hasReachedMaxPage,
        isLoadingMore,
        loadMoreFailure,
        searchFailure,
        searchLoading,
        searchQuery,
      ];

  @override
  String toString() {
    return 'MarketplaceLoaded(advertisements: $advertisements, currentPage: $currentPage, hasReachedMaxPage: $hasReachedMaxPage, isLoadingMore: $isLoadingMore, loadMoreFailure: $loadMoreFailure, searchFailure: $searchFailure, searchLoading: $searchLoading, searchQuery: $searchQuery)';
  }
}

final class MarketplaceError extends MarketplaceState {
  const MarketplaceError(this.failure);
  final HttpFailure? failure;

  @override
  List<Object?> get props => [failure];

  @override
  String toString() {
    return 'MarketplaceError(message: $failure)';
  }
}
