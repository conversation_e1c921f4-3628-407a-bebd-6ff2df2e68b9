import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/view/full_screen_image_view.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ProfileCategoryView extends StatelessWidget {
  const ProfileCategoryView({
    super.key,
    this.isEditable = false,
    this.business,
    this.onDeleteTap,
    this.onEditTap,
  });
  final bool isEditable;
  final void Function()? onDeleteTap;
  final void Function()? onEditTap;

  final BusinessModel? business;

  @override
  Widget build(BuildContext context) {
    final parentCategory = business?.category?.parentCategory?.parentCategory?.name;
    final subCategory = business?.category?.parentCategory?.name;
    final childCategory = business?.category?.name;

    final availableCategories = [
      if (parentCategory != null && subCategory != null) subCategory,
      if (subCategory != null && childCategory != null) childCategory,
    ];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(14),
      margin: isEditable ? null : const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: AppColors.offGreen,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parentCategory ?? subCategory ?? childCategory ?? '',
            style: Theme.of(context).textTheme.headlineMedium,
          ),
          const Gap(10),
          if (availableCategories.isNotEmpty) ...[
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.start,
              runSpacing: 10,
              spacing: 10,
              children: [
                if (parentCategory != null && subCategory != null)
                  CategoryTypeWidget(title: subCategory, color: AppColors.turquoiseGreen),
                if (subCategory != null && childCategory != null)
                  CategoryTypeWidget(title: childCategory, color: AppColors.turquoiseGreen),
              ],
            ),
            const Gap(20),
          ],
          Text(
            business?.shortDescription ?? '',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const Gap(14),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                business?.images?.length ?? 0,
                (index) {
                  return InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => FullScreenImageViewer(
                            images: business?.images ?? [],
                            initialIndex: index,
                            isProfile: true,
                          ),
                        ),
                      );
                    },
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: business?.images?[index].imagePath ?? '',
                        placeholder: AppAssets.whiteBgImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 93,
                        width: 93,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          if (isEditable) ...[
            const Gap(14),
            const Divider(
              height: 1,
              thickness: 1,
              color: AppColors.subText,
            ),
            const Gap(14),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Valid till ${business?.validTillDate ?? ''}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                      ),
                      Text(
                        'trxn id : ----', // TODO
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.subText),
                      )
                    ],
                  ),
                ),
                InkWell(
                  onTap: onEditTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8),
                    child: AppSvgImage(AppAssets.editIcon),
                  ),
                ),
                InkWell(
                  onTap: onDeleteTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const Padding(
                    padding: EdgeInsets.only(left: 8),
                    child: AppSvgImage(AppAssets.deleteIcon),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
