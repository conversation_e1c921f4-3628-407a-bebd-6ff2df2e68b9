import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/report/view/report_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class ProfileBackgroundImage extends StatelessWidget {
  const ProfileBackgroundImage({super.key, this.user, this.onBackTap, this.onFavouriteTap});
  final UserModel? user;
  final void Function()? onBackTap;
  final void Function()? onFavouriteTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 185,
      width: MediaQuery.sizeOf(context).width,
      child: Stack(
        children: [
          SizedBox(
            width: MediaQuery.sizeOf(context).width,
            child: Utility.imageLoader(
              url: user?.profileImage ?? '',
              placeholder: AppAssets.placeholderImage,
              borderRadius: BorderRadius.circular(0),
            ),
          ),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              color: AppColors.mirage.withOpacity2(0.44),
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 40, 20, 0),
            child: Row(
              children: [
                InkWell(
                  onTap: onBackTap,
                  overlayColor: WidgetStateProperty.all(AppColors.transparent),
                  child: const AppSvgImage(AppAssets.roundBackIcon),
                ),
                const Spacer(),
                if (user?.role == AppConstants.businessRole)
                  InkWell(
                    onTap: onFavouriteTap,
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Container(
                      height: 46,
                      width: 46,
                      padding: const EdgeInsets.all(11),
                      decoration: BoxDecoration(
                        color: AppColors.black.withOpacity2(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: AppSvgImage(
                        user?.isFavourite == 1 ? AppAssets.favouriteIcon : AppAssets.unfavouriteIcon,
                        color: user?.isFavourite == 1 ? AppColors.primary : AppColors.white,
                      ),
                    ),
                  ),
                const Gap(20),
                BlocSelector<AuthenticationBloc, AuthenticationState, int>(
                  selector: (state) => state.user.id,
                  builder: (context, userId) {
                    return PopupMenuButton<int>(
                      tooltip: 'More options',
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      color: AppColors.white,
                      elevation: 6,
                      position: PopupMenuPosition.under,
                      constraints: const BoxConstraints(minWidth: 151, minHeight: 62),
                      onSelected: (value) {
                        if (value == 0) {
                          // share
                        }
                        if (value == 1) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (_) => ReportPage(userId: user?.id ?? 0)),
                          );
                        }
                      },
                      itemBuilder: (context) => [
                        PopupMenuItem<int>(
                          value: 0,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              const Padding(
                                padding: EdgeInsets.only(top: 2.0),
                                child: AppSvgImage(AppAssets.shareIcon, height: 20, width: 20),
                              ),
                              const Gap(11),
                              Text(
                                "Share",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        if (userId != user?.id)
                          PopupMenuItem<int>(
                            value: 1,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(top: 2.0),
                                  child: AppSvgImage(AppAssets.reportIcon, height: 20, width: 20),
                                ),
                                const Gap(11),
                                Text(
                                  "Report",
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                      ],
                      child: const AppSvgImage(AppAssets.roundMenuIcon), // Your icon
                    );
                  },
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
