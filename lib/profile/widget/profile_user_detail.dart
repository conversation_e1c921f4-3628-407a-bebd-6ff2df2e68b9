import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/url_manager.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/profile/widget/profile_category_view.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class ProfileUserDetail extends StatefulWidget {
  const ProfileUserDetail({super.key, this.user, this.onLikeTap, this.onFavoriteTap});
  final UserModel? user;
  final void Function()? onLikeTap;
  final void Function()? onFavoriteTap;

  @override
  State<ProfileUserDetail> createState() => _ProfileUserDetailState();
}

class _ProfileUserDetailState extends State<ProfileUserDetail> with PaginationMixin {
  final businessList = ValueNotifier<List<BusinessModel>>(<BusinessModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    if (widget.user?.role == AppConstants.businessRole) load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    businessList.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IBusinessRepository>().businessList(userId: widget.user?.id);

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      businessList.value = [...r.data ?? []];
      hasReachedMax = (r.data?.length ?? 0) < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IBusinessRepository>().businessList(page: page, userId: widget.user?.id);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = (r.data?.length ?? 0) < 10;
      businessList.value = [...businessList.value, ...r.data ?? []];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  widget.user?.name ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(height: 1.2),
                ),
              ),
              if ((widget.user?.likesCount ?? 0) > 500) ...[
                const Gap(5),
                const AppSvgImage(
                  AppAssets.verifyIcon,
                  height: 22,
                  width: 22,
                )
              ],
            ],
          ),
        ),
        if (widget.user?.companyName != null)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              widget.user?.companyName ?? '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.subText),
            ),
          ),
        if (widget.user?.fullAddress != null) ...[
          const Gap(7),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100),
              color: AppColors.background,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const AppSvgImage(AppAssets.locationIcon, height: 18, width: 18),
                const Gap(2),
                Text(
                  widget.user?.fullAddress ?? '',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.2),
                )
              ],
            ),
          ),
        ],
        if (widget.user?.role == AppConstants.businessRole) ...[
          const Gap(20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: widget.onLikeTap,
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AppSvgImage(widget.user?.isLiked == 1 ? AppAssets.likeHandIcon : AppAssets.dislikeHandIcon,
                        height: 24, width: 24),
                    const Gap(6),
                    Text(
                      widget.user?.formmtedLikeCount ?? '',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                    )
                  ],
                ),
              ),
              // const Gap(20),
              // InkWell(
              //   onTap: () {
              //     Navigator.push(
              //       context,
              //       MaterialPageRoute(
              //         builder: (context) => const RatingPage(),
              //       ),
              //     );
              //   },
              //   overlayColor: WidgetStateProperty.all(AppColors.transparent),
              //   child: Row(
              //     children: [
              //       const AppSvgImage(AppAssets.fillStarIcon, height: 24, width: 24),
              //       const Gap(6),
              //       Text(
              //         '4.6',
              //         style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
              //       ),
              //       const Gap(6),
              //       Text(
              //         '(15 reviews)',
              //         style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.subText, height: 1.2),
              //       ),
              //     ],
              //   ),
              // )
            ],
          ),
        ],
        const Gap(20),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.user?.role == AppConstants.businessRole && widget.user?.facebookUrl != null) ...[
              InkWell(
                onTap: () {
                  UrlManager.openUrl(widget.user?.facebookUrl);
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: Container(
                  height: 42,
                  width: 42,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.subText.withOpacity2(0.24), width: 1),
                  ),
                  child: const AppSvgImage(AppAssets.facebookIcon),
                ),
              ),
              const Gap(16),
            ],
            if (widget.user?.role == AppConstants.businessRole && widget.user?.instagramUrl != null) ...[
              InkWell(
                onTap: () {
                  UrlManager.openUrl(widget.user?.instagramUrl);
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: Container(
                  height: 42,
                  width: 42,
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: AppColors.subText.withOpacity2(0.24), width: 1),
                  ),
                  child: const AppSvgImage(AppAssets.instagramIcon),
                ),
              ),
              const Gap(16),
            ],
            if (widget.user?.canSendMessage == 1 &&
                context.read<AuthenticationBloc>().state.user.id != widget.user?.id) ...[
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatDetailWrapperPage(
                        userId: widget.user?.id ?? 0,
                      ),
                    ),
                  );
                },
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                child: const AppSvgImage(
                  AppAssets.roundMessageIcon,
                  height: 42,
                  width: 42,
                ),
              ),
              const Gap(16),
            ],
            if (widget.user?.canCall == 1 && widget.user?.mobileNumber != null)
              callButton(
                context: context,
                onTap: () {
                  UrlManager.openDialpad(widget.user?.mobileNumber);
                },
              ),
          ],
        ),
        if (widget.user?.role == AppConstants.businessRole) ...[
          const Gap(24),
          const Divider(
            height: 4,
            thickness: 4,
            color: AppColors.porcelain,
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('BUSINESS / SERVICES',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.subText)),
                ValueListenableBuilder<bool>(
                    valueListenable: isLoading,
                    builder: (context, loading, _) {
                      if (loading) {
                        return const Center(
                            child: CustomProgressIndecator(
                          color: AppColors.primary,
                          size: 40,
                        ));
                      }
                      return ValueListenableBuilder<List<BusinessModel>>(
                          valueListenable: businessList,
                          builder: (context, businesses, _) {
                            if (!loading && businesses.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.only(top: 20),
                                child: Utility.noDataWidget(context: context, text: 'No Business Found'),
                              );
                            }
                            return ListView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              itemCount: businesses.length,
                              controller: scrollPaginationController,
                              padding: const EdgeInsets.only(top: 20),
                              itemBuilder: (context, index) {
                                return ValueListenableBuilder<bool>(
                                    valueListenable: isLoadingMore,
                                    builder: (context, loadingMore, _) {
                                      return Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          ProfileCategoryView(business: businesses[index]),
                                          if (!hasReachedMax && loadingMore && index == businesses.length - 1)
                                            const CustomProgressIndecator(color: AppColors.primary, size: 40),
                                        ],
                                      );
                                    });
                              },
                            );
                          });
                    }),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget callButton({required BuildContext context, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        height: 42,
        width: 118,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100),
          color: AppColors.primary,
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withOpacity2(0.25),
              spreadRadius: 0,
              blurRadius: 14,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const AppSvgImage(
              AppAssets.callIcon,
              height: 24,
              width: 24,
            ),
            const Gap(6),
            Text(
              'Call',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.white, height: 1.2),
            )
          ],
        ),
      ),
    );
  }
}
