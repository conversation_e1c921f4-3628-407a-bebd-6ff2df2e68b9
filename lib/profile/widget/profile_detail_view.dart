import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/url_manager.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';

class ProfileDetailView extends StatelessWidget {
  const ProfileDetailView({super.key, this.user, this.onLikeTap});
  final UserModel? user;
  final VoidCallback? onLikeTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                height: 76,
                width: 76,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(0, 4),
                      blurRadius: 4,
                      spreadRadius: 0,
                      color: AppColors.black.withOpacity2(0.15),
                    ),
                  ],
                ),
                child: Utility.imageLoader(url: user?.profileImage ?? '', placeholder: AppAssets.placeholderImage),
              ),
              const Gap(12),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            user?.name ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.headlineMedium?.copyWith(height: 1.2),
                          ),
                        ),
                        const Gap(5),
                        if ((user?.likesCount ?? 0) > 500)
                          const AppSvgImage(
                            AppAssets.verifyIcon,
                            height: 18,
                            width: 18,
                          ),
                      ],
                    ),
                    if (user?.companyName != null && user!.companyName!.trim().isNotEmpty)
                      Flexible(
                        child: Text(
                          user?.companyName ?? '',
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                      ),
                    if (user?.role == AppConstants.businessRole) ...[
                      Row(
                        children: [
                          InkWell(
                            onTap: onLikeTap,
                            overlayColor: WidgetStateProperty.all(AppColors.transparent),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                AppSvgImage(
                                  user?.isLiked == 1 ? AppAssets.likeHandIcon : AppAssets.dislikeHandIcon,
                                  height: 20,
                                  width: 20,
                                ),
                                const Gap(6),
                                Text(
                                  '${user?.likesCount ?? 0}',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                                ),
                              ],
                            ),
                          ),
                          // const Gap(20),
                          // InkWell(
                          //   onTap: () {
                          //     Navigator.push(
                          //       context,
                          //       MaterialPageRoute(
                          //         builder: (context) => const RatingPage(),
                          //       ),
                          //     );
                          //   },
                          //   overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          //   child: Row(
                          //     children: [
                          //       const AppSvgImage(AppAssets.fillStarIcon, height: 18, width: 18),
                          //       const Gap(6),
                          //       Text(
                          //         '4.6',
                          //         style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                          //       ),
                          //       const Gap(6),
                          //       Text(
                          //         '(15 reviews)',
                          //         style: Theme.of(context)
                          //             .textTheme
                          //             .bodySmall
                          //             ?.copyWith(color: AppColors.subText, height: 1.2),
                          //       ),
                          //     ],
                          //   ),
                          // ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          const Gap(20),
          personalInfoView(
              onTap: () {
                UrlManager.openDialpad(user?.mobileNumber);
              },
              context: context,
              title: 'Mobile',
              value: user?.mobileNumber ?? ''),
          const Gap(20),
          personalInfoView(
              onTap: () {
                UrlManager.openEmail(user?.email);
              },
              context: context,
              title: 'Email',
              value: user?.email ?? ''),
          const Gap(20),
          if (user?.fullAddress != null) ...[
            personalInfoView(
              context: context,
              title: 'Address',
              value: user?.fullAddress ?? '',
            ),
            const Gap(20),
          ],
          if (user?.instagramUrl != null && user!.instagramUrl!.trim().isNotEmpty) ...[
            personalInfoView(
                onTap: () {
                  UrlManager.openUrl(user?.instagramUrl);
                },
                context: context,
                title: 'Instagram',
                icon: AppAssets.instagramIcon),
            const Gap(20),
          ],
          if (user?.facebookUrl != null && user!.facebookUrl!.trim().isNotEmpty) ...[
            personalInfoView(
                onTap: () {
                  UrlManager.openUrl(user?.facebookUrl);
                },
                context: context,
                title: 'Facebook',
                icon: AppAssets.facebookIcon),
            const Gap(20),
          ],
          personalInfoView(
              context: context,
              title: 'Profile Status',
              value: user?.isEmailVerified == 1 ? 'Verified' : 'Not Verified'),
          const Gap(8),
          if (user?.role == AppConstants.businessRole && (user?.likesCount ?? 0) < 500)
            AppTextFormField(
              maxLines: 2,
              readOnly: true,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 13),
              controller: TextEditingController(
                  text: 'When you get 500+ likes on your profile, your account receives a verification badge.'),
            ),
        ],
      ),
    );
  }

  Widget personalInfoView(
      {required BuildContext context, String? title, String? value, String? icon, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              title ?? '',
              maxLines: 1,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.subText),
            ),
          ),
          const Gap(10),
          if (value != null)
            Flexible(
              flex: 2,
              child: Text(
                value,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          if (icon != null) AppSvgImage(icon),
        ],
      ),
    );
  }
}
