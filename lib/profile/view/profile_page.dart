import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/profile/view/edit_profile_page.dart';
import 'package:leiuniverse/profile/widget/profile_category_view.dart';
import 'package:leiuniverse/profile/widget/profile_detail_view.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/view/add_business_service_page.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class ProfilePageWraper extends StatelessWidget {
  const ProfilePageWraper({super.key});

  @override
  Widget build(BuildContext context) {
    final authState = context.read<AuthenticationBloc>().state;
    final isBusinessUser = authState.user.role == AppConstants.businessRole;

    if (isBusinessUser) {
      return BlocProvider(
        create: (context) => getIt<BusinessBloc>()..add(const LoadBusinesses()),
        child: const ProfilePage(isBusiness: true),
      );
    }

    return const ProfilePage();
  }
}

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key, this.isBusiness = false});
  final bool isBusiness;

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with PaginationMixin {
  final isStackLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    initiatePagination();
    context.read<AuthenticationBloc>().add(const CheckAuthentication());
  }

  @override
  void dispose() {
    disposePagination();
    super.dispose();
  }

  Future<void> likeUser(int id) async {
    isStackLoading.value = true;

    final failOrSuccess = await getIt<IUserRepository>().likeUser(id: id);

    failOrSuccess.fold((l) {
      isStackLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isStackLoading.value = false;
      context.read<AuthenticationBloc>().add(LikeUser(id: id));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: 'Profile',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10),
            child: BlocBuilder<AuthenticationBloc, AuthenticationState>(
              builder: (context, state) {
                if (state.isAutheticated) {
                  return IconButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditProfilePage(user: state.user),
                        ),
                      );
                    },
                    icon: const AppSvgImage(AppAssets.editIcon),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          )
        ],
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BlocBuilder<AuthenticationBloc, AuthenticationState>(
                  builder: (context, state) {
                    return state.when(
                      unAutheticated: (failure) => Center(child: Text(failure?.message ?? '')),
                      loading: () => SizedBox(
                          height: MediaQuery.of(context).size.height,
                          child: const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40))),
                      autheticated: (user) => ProfileDetailView(
                        user: user,
                        onLikeTap: widget.isBusiness
                            ? () {
                                likeUser(user.id);
                              }
                            : null,
                      ),
                      unknown: () => const SizedBox.shrink(),
                    );
                  },
                ),
                if (widget.isBusiness) ...[
                  const Divider(
                    height: 4,
                    thickness: 4,
                    color: AppColors.border,
                  ),
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Business/Services'.toUpperCase(),
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.subText),
                            ),
                            CommonButton(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (_) => BlocProvider.value(
                                      value: context.read<BusinessBloc>(),
                                      child: const AddBusinessServicePage(),
                                    ),
                                  ),
                                );
                              },
                              text: 'Add Business',
                              borderRadius: 8,
                              removeShadow: true,
                              width: 109,
                              fontSize: 14,
                              height: 30,
                              padding: const EdgeInsets.symmetric(vertical: 4),
                            )
                          ],
                        ),
                        const Gap(20),
                        BlocBuilder<BusinessBloc, BusinessState>(
                          builder: (context, state) {
                            return state.when(
                              initial: () => const SizedBox.shrink(),
                              loading: () =>
                                  const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40)),
                              error: (message) => const Center(child: Text('Error')),
                              loaded: (newState) {
                                return ListView.separated(
                                  itemCount: newState.businesses.length,
                                  controller: scrollPaginationController,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        ProfileCategoryView(
                                          business: newState.businesses[index],
                                          isEditable: true,
                                          onDeleteTap: () {
                                            AppDailogs.deleteBusinessDailog(context,
                                                businessId: newState.businesses[index].id ?? 0);
                                          },
                                          onEditTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (_) => BlocProvider.value(
                                                  value: context.read<BusinessBloc>(),
                                                  child: AddBusinessServicePage(business: newState.businesses[index]),
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                        if (index == newState.businesses.length - 1 && newState.isLoadingMore)
                                          const Center(
                                              child: Padding(
                                            padding: EdgeInsets.only(top: 20),
                                            child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                                          ))
                                      ],
                                    );
                                  },
                                  separatorBuilder: (context, index) => const Gap(20),
                                );
                              },
                            );
                          },
                        )
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          ValueListenableBuilder(
            valueListenable: isStackLoading,
            builder: (context, loading, _) {
              return loading
                  ? const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40))
                  : const SizedBox.shrink();
            },
          )
        ],
      ),
    );
  }

  @override
  void onReachedLast() {
    context.read<BusinessBloc>().state.when(
          initial: () => const SizedBox.shrink(),
          loading: () => const SizedBox.shrink(),
          error: (message) => const SizedBox.shrink(),
          loaded: (newState) {
            if (!newState.isLoadingMore && !newState.hasReachedMax) {
              context.read<BusinessBloc>().add(const LoadMoreBusinesses());
            }
          },
        );
  }
}
