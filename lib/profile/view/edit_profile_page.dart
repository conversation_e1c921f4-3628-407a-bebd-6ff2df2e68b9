import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/setup_business/model/image_model.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/city_dropdown.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/country_dropdown.dart';
import 'package:leiuniverse/widget/photo_frame_widget.dart';
import 'package:leiuniverse/widget/state_dropdown.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key, this.user});
  final UserModel? user;

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final nameController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final pincodeController = TextEditingController();
  final instagramController = TextEditingController();
  final facebookController = TextEditingController();
  final companyNameController = TextEditingController();

  List<CountryModel> countryList = <CountryModel>[];
  List<CountryModel> stateList = <CountryModel>[];
  List<CountryModel> cityList = <CountryModel>[];

  final selectedCity = ValueNotifier<CountryModel?>(null);
  final selectedState = ValueNotifier<CountryModel?>(null);
  final selectedCountry = ValueNotifier<CountryModel?>(null);
  final pickedImageFile = ValueNotifier<PickedFileModel?>(null);
  final formKey = GlobalKey<FormState>();

  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    setData();
  }

  void setData() {
    if (widget.user?.name != null) {
      nameController.text = widget.user?.name ?? '';
    }
    if (widget.user?.mobileNumber != null) {
      mobileController.text = widget.user?.mobileNumber ?? '';
    }
    if (widget.user?.email != null) {
      emailController.text = widget.user?.email ?? '';
    }
    if (widget.user?.pincode != null) {
      pincodeController.text = widget.user?.pincode ?? '';
    }
    if (widget.user?.city != null) {
      cityList = [widget.user!.city!];
      selectedCity.value = widget.user?.city;
    }
    if (widget.user?.city?.state != null) {
      stateList = [widget.user!.city!.state!];
      selectedState.value = widget.user?.city?.state;
    }
    if (widget.user?.city?.country != null) {
      countryList = [widget.user!.city!.country!];
      selectedCountry.value = widget.user?.city?.country;
    }
    if (widget.user?.role == AppConstants.businessRole) {
      if (widget.user?.instagramUrl != null) {
        instagramController.text = widget.user?.instagramUrl ?? '';
      }
      if (widget.user?.facebookUrl != null) {
        facebookController.text = widget.user?.facebookUrl ?? '';
      }
      if (widget.user?.companyName != null) {
        companyNameController.text = widget.user?.companyName ?? '';
      }
    }
    if (widget.user?.profileImage != null) {
      pickedImageFile.value = PickedFileModel(networkFile: ImageModel(imageUrl: widget.user?.profileImage));
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    mobileController.dispose();
    emailController.dispose();
    selectedCity.dispose();
    selectedState.dispose();
    selectedCountry.dispose();
    pickedImageFile.dispose();
    instagramController.dispose();
    facebookController.dispose();
    companyNameController.dispose();
    pincodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Edit Profile',
      ),
      body: Form(
        key: formKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    ValueListenableBuilder<PickedFileModel?>(
                        valueListenable: pickedImageFile,
                        builder: (context, file, _) {
                          return PhotoFrameWidget(
                            onTap: () async {
                              final pickedImage = await ImagePicker().pickImage(source: ImageSource.gallery);
                              if (pickedImage != null) {
                                pickedImageFile.value = PickedFileModel(file: File(pickedImage.path));
                              }
                            },
                            onRemove: () {
                              pickedImageFile.value = null;
                            },
                            images: file,
                          );
                        }),
                    const Gap(30),
                    AppTextFormField(
                      title: 'Name',
                      controller: nameController,
                      textInputAction: TextInputAction.next,
                      inputFormatters: [LengthLimitingTextInputFormatter(50)],
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter name';
                        }
                        return null;
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Mobile',
                      controller: mobileController,
                      textInputAction: TextInputAction.next,
                      keyboardType: TextInputType.phone,
                      isRequired: true,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly, LengthLimitingTextInputFormatter(10)],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter mobile number';
                        }
                        return null;
                      },
                    ),
                    const Gap(14),
                    AppTextFormField(
                      title: 'Email',
                      controller: emailController,
                      isRequired: true,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.next,
                      readOnly: true,
                    ),
                    if (widget.user?.role == AppConstants.businessRole) ...[
                      const Gap(14),
                      AppTextFormField(
                        title: 'Name of company/ organisation/ firm',
                        controller: companyNameController,
                        textInputAction: TextInputAction.next,
                        inputFormatters: [LengthLimitingTextInputFormatter(70)],
                      ),
                      const Gap(14),
                      AppTextFormField(
                        title: 'Instagram',
                        controller: instagramController,
                        validator: Utility.validateUrl,
                        textInputAction: TextInputAction.next,
                      ),
                      const Gap(14),
                      AppTextFormField(
                        title: 'Facebook',
                        controller: facebookController,
                        validator: Utility.validateUrl,
                        textInputAction: TextInputAction.next,
                      ),
                    ],
                  ],
                ),
              ),
              const Divider(
                height: 1,
                color: AppColors.border,
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Address',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppColors.primary),
                    ),
                    const Gap(14),
                    Row(
                      children: [
                        Expanded(
                          child: AppTextFormField(
                            title: 'Pincode',
                            controller: pincodeController,
                            textInputAction: TextInputAction.next,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(10)
                            ],
                            isRequired: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter pincode';
                              }
                              return null;
                            },
                          ),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedCountry,
                              builder: (context, selected, _) {
                                return CountryDropdown(
                                  isRequired: true,
                                  selectedCountry: selected,
                                  onCountryChanged: (country) {
                                    countryList = [country];
                                    selectedCountry.value = country;
                                    selectedState.value = null;
                                    selectedCity.value = null;
                                  },
                                );
                              }),
                        ),
                      ],
                    ),
                    const Gap(14),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedCountry,
                              builder: (context, country, _) {
                                return ValueListenableBuilder<CountryModel?>(
                                    valueListenable: selectedState,
                                    builder: (context, selected, _) {
                                      return StateDropdown(
                                        isRequired: true,
                                        countryId: country?.id,
                                        selectedState: selected,
                                        onStateChanged: (state) {
                                          stateList = [state];
                                          selectedState.value = state;
                                          selectedCity.value = null;
                                        },
                                      );
                                    });
                              }),
                        ),
                        const Gap(15),
                        Expanded(
                          child: ValueListenableBuilder<CountryModel?>(
                              valueListenable: selectedState,
                              builder: (context, state, _) {
                                return CityDropdown(
                                  isRequired: true,
                                  stateId: state?.id,
                                  onCityChanged: (city) {
                                    cityList = city;
                                    selectedCity.value = city.firstOrNull;
                                  },
                                  selectedCity: cityList,
                                );
                              }),
                        ),
                      ],
                    ),
                    const Gap(30),
                    ValueListenableBuilder<bool>(
                        valueListenable: isButtonLoading,
                        builder: (context, loading, _) {
                          return CommonButton(
                            isLoading: loading,
                            onTap: loading
                                ? null
                                : () {
                                    validate();
                                  },
                            text: 'Update',
                          );
                        }),
                    const Gap(30),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  validate() {
    if (pickedImageFile.value == null) {
      Utility.toast(message: 'Please select profile image');
    } else if (formKey.currentState!.validate()) {
      updateUser();
    }
  }

  Future<void> updateUser() async {
    isButtonLoading.value = true;
    final response = await getIt<IUserRepository>().update(
      companyName: companyNameController.text,
      facebookUrl: facebookController.text,
      instagramUrl: instagramController.text,
      mobileNumber: mobileController.text,
      name: nameController.text,
      profileImage: pickedImageFile.value?.file,
      pincode: pincodeController.text,
      cityId: selectedCity.value?.id?.toString(),
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.status == 1 && r.data != null) {
          context.read<AuthenticationBloc>().add(UpdateUser(user: r.data));

          Navigator.pop(context);
        }
        isButtonLoading.value = false;
        Utility.toast(message: r.message);
      },
    );
  }
}
