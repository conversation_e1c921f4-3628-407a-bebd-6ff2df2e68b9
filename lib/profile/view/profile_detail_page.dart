import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/profile/widget/profile_background_image.dart';
import 'package:leiuniverse/profile/widget/profile_user_detail.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class ProfileDetailPage extends StatefulWidget {
  const ProfileDetailPage({super.key, this.userId});
  final int? userId;

  @override
  State<ProfileDetailPage> createState() => _ProfileDetailPageState();
}

class _ProfileDetailPageState extends State<ProfileDetailPage> {
  final user = ValueNotifier<UserModel?>(null);
  final isStackLoading = ValueNotifier<bool>(false);
  final isLoading = ValueNotifier<bool>(false);

  Future<void> userDetail() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IAuthRepository>().userDetail(userId: widget.userId?.toString());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      user.value = r.data;
    });
  }

  Future<void> likeUser() async {
    final previousUser = user.value;

    user.value = user.value?.copyWith(
      isLiked: user.value?.isLiked == 0 ? 1 : 0,
      likesCount: user.value?.isLiked == 0 ? (user.value?.likesCount ?? 0) + 1 : (user.value?.likesCount ?? 0) - 1,
    );

    final failOrSuccess = await getIt<IUserRepository>().likeUser(id: widget.userId ?? 0);

    failOrSuccess.fold((l) {
      user.value = previousUser;
      Utility.toast(message: l.message);
    }, (r) {});
  }

  Future<void> favouriteUser() async {
    final previousUser = user.value;

    user.value = user.value?.copyWith(
      isFavourite: user.value?.isFavourite == 0 ? 1 : 0,
    );

    final failOrSuccess = await getIt<IUserRepository>().favouriteUser(id: widget.userId ?? 0);

    failOrSuccess.fold((l) {
      user.value = previousUser;

      Utility.toast(message: l.message);
    }, (r) {});
  }

  @override
  void initState() {
    super.initState();
    userDetail();
  }

  @override
  Widget build(BuildContext context) {
    double statusBar = MediaQuery.of(context).padding.top;
    double avatarSize = 100;
    double avatarMarginTop = statusBar + 67;
    double contentMarginTop = avatarMarginTop + (avatarSize / 2);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop) {
          Navigator.pop(context, user.value);
        }
      },
      child: Scaffold(
        body: ValueListenableBuilder<bool>(
            valueListenable: isLoading,
            builder: (context, loading, _) {
              if (loading) {
                return const Center(
                  child: CustomProgressIndecator(
                    size: 40,
                    color: AppColors.primary,
                  ),
                );
              }
              return Stack(
                children: [
                  NestedScrollView(
                    headerSliverBuilder: (context, innerBoxIsScrolled) => [],
                    body: CustomScrollView(
                      slivers: [
                        ValueListenableBuilder<UserModel?>(
                            valueListenable: user,
                            builder: (context, userInfo, _) {
                              return SliverToBoxAdapter(
                                child: Stack(
                                  clipBehavior: Clip.none,
                                  children: [
                                    ValueListenableBuilder<bool>(
                                        valueListenable: isStackLoading,
                                        builder: (context, loading, _) {
                                          return ProfileBackgroundImage(
                                            user: userInfo,
                                            onBackTap: () {
                                              Navigator.pop(context, userInfo);
                                            },
                                            onFavouriteTap: loading
                                                ? null
                                                : () {
                                                    favouriteUser();
                                                  },
                                          );
                                        }),

                                    // Profile content card
                                    Container(
                                      width: MediaQuery.sizeOf(context).width,
                                      margin: EdgeInsets.only(top: contentMarginTop),
                                      padding: const EdgeInsets.only(top: 57), // space under avatar
                                      decoration: const BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(24),
                                          topLeft: Radius.circular(24),
                                        ),
                                        color: AppColors.white,
                                      ),
                                      child: ValueListenableBuilder<bool>(
                                          valueListenable: isStackLoading,
                                          builder: (context, loading, _) {
                                            return ProfileUserDetail(
                                              user: userInfo,
                                              onFavoriteTap: loading
                                                  ? null
                                                  : () {
                                                      favouriteUser();
                                                    },
                                              onLikeTap: loading
                                                  ? null
                                                  : () {
                                                      likeUser();
                                                    },
                                            );
                                          }),
                                    ),

                                    // Avatar Image Positioned Center
                                    Positioned(
                                      top: avatarMarginTop,
                                      left: (MediaQuery.of(context).size.width / 2) - (avatarSize / 2),
                                      child: Container(
                                        height: avatarSize,
                                        width: avatarSize,
                                        clipBehavior: Clip.hardEdge,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          boxShadow: [
                                            BoxShadow(
                                              offset: const Offset(0, 4),
                                              blurRadius: 4,
                                              spreadRadius: 0,
                                              color: AppColors.black.withOpacity2(0.15),
                                            ),
                                          ],
                                        ),
                                        child: Utility.imageLoader(
                                            url: userInfo?.profileImage ?? '',
                                            placeholder: AppAssets.placeholderImage,
                                            fit: BoxFit.fill),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                      ],
                    ),
                  ),
                  ValueListenableBuilder(
                    valueListenable: isStackLoading,
                    builder: (context, value, _) {
                      return value
                          ? const Center(child: CustomProgressIndecator(size: 40, color: AppColors.primary))
                          : const SizedBox();
                    },
                  )
                ],
              );
            }),
      ),
    );
  }
}
