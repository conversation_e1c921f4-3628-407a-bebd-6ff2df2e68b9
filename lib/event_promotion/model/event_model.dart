import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:leiuniverse/event_promotion/model/category_element_model.dart';
import 'package:leiuniverse/event_promotion/model/city_element_model.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/model/image_model.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class EventModel extends Equatable {
  const EventModel({
    this.likesCount,
    this.id,
    this.userId,
    this.name,
    this.shortDescription,
    this.isActive,
    this.createdAt,
    this.updatedAt,
    this.expiryDate,
    this.isLiked,
    this.user,
    this.images,
    this.cities,
    this.categories,
    this.state,
    this.country,
    this.subCategory,
    this.parentCategory,
    this.totalComments,
  });

  final int? id;
  final int? userId;
  final String? name;
  final String? shortDescription;
  final int? isActive;
  final int? isLiked;
  final int? likesCount;
  final int? totalComments;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? expiryDate;
  final UserModel? user;
  final List<ImageModel>? images;
  final List<CityElementModel>? cities;
  final List<CategoryElementModel>? categories;
  final CountryModel? state;
  final CountryModel? country;
  final CategoryModel? subCategory;
  final CategoryModel? parentCategory;

  EventModel copyWith({
    int? likesCount,
    int? id,
    int? userId,
    String? name,
    String? shortDescription,
    int? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiryDate,
    int? isLiked,
    int? totalComments,
    UserModel? user,
    List<ImageModel>? images,
    List<CityElementModel>? cities,
    List<CategoryElementModel>? categories,
    CountryModel? state,
    CountryModel? country,
    CategoryModel? subCategory,
    CategoryModel? parentCategory,
  }) {
    return EventModel(
      likesCount: likesCount ?? this.likesCount,
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      shortDescription: shortDescription ?? this.shortDescription,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiryDate: expiryDate ?? this.expiryDate,
      totalComments: totalComments ?? this.totalComments,
      isLiked: isLiked ?? this.isLiked,
      user: user ?? this.user,
      images: images ?? this.images,
      cities: cities ?? this.cities,
      categories: categories ?? this.categories,
      state: state ?? this.state,
      country: country ?? this.country,
      subCategory: subCategory ?? this.subCategory,
      parentCategory: parentCategory ?? this.parentCategory,
    );
  }

  factory EventModel.fromJson(Map<String, dynamic> json) {
    return EventModel(
      likesCount: json["likes_count"],
      id: json["id"],
      userId: json["user_id"],
      name: json["name"],
      shortDescription: json["short_description"],
      isActive: json["is_active"],
      totalComments: json["total_comments"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      expiryDate: DateTime.tryParse(json["expiry_date"] ?? ""),
      isLiked: json["is_liked"],
      user: json["user"] == null ? null : UserModel.fromJson(json["user"]),
      images: json["images"] == null ? [] : List<ImageModel>.from(json["images"]!.map((x) => ImageModel.fromJson(x))),
      cities: json["cities"] == null
          ? []
          : List<CityElementModel>.from(json["cities"]!.map((x) => CityElementModel.fromJson(x))),
      categories: json["categories"] == null
          ? []
          : List<CategoryElementModel>.from(json["categories"]!.map((x) => CategoryElementModel.fromJson(x))),
      state: json["state"] == null ? null : CountryModel.fromJson(json["state"]),
      country: json["country"] == null ? null : CountryModel.fromJson(json["country"]),
      subCategory: json["sub_category"] == null ? null : CategoryModel.fromJson(json["sub_category"]),
      parentCategory: json["parent_category"] == null ? null : CategoryModel.fromJson(json["parent_category"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "likes_count": likesCount,
        "id": id,
        "user_id": userId,
        "name": name,
        "short_description": shortDescription,
        "is_active": isActive,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "expiry_date": expiryDate?.toIso8601String(),
        "total_comments": totalComments,
        "is_liked": isLiked,
        "user": user?.toJson(),
        "images": images == null ? [] : images!.map((x) => x.toJson()).toList(),
        "cities": cities == null ? [] : cities!.map((x) => x.toJson()).toList(),
        "categories": categories == null ? [] : categories!.map((x) => x.toJson()).toList(),
        "state": state?.toJson(),
        "country": country?.toJson(),
        "sub_category": subCategory?.toJson(),
        "parent_category": parentCategory?.toJson(),
      };

  @override
  String toString() {
    return "$likesCount, $id, $userId, $name, $shortDescription, $totalComments, $isActive, $createdAt, $updatedAt, $expiryDate, $isLiked, $user, $images, $cities, $categories, $state, $country, $subCategory, $parentCategory, ";
  }

  @override
  List<Object?> get props => [
        likesCount,
        id,
        userId,
        name,
        shortDescription,
        totalComments,
        isActive,
        createdAt,
        updatedAt,
        expiryDate,
        isLiked,
        user,
        images,
        cities,
        categories,
        state,
        country,
        subCategory,
        parentCategory,
      ];

  bool get isExpired => expiryDate != null && expiryDate!.isBefore(DateTime.now());

  String get eventLocation {
    final city = cities?.firstOrNull?.city?.name;
    final stateName = state?.name;
    final countryName = country?.name;
    final List<String> location = [];
    if (city != null) location.add(city);
    if (stateName != null) location.add(stateName);
    if (countryName != null) location.add(countryName);
    return location.join(', ');
  }

  String get validTillDate {
    if (expiryDate == null) return "";

    if (isExpired) {
      return 'Expired on ${DateFormat('dd/MM/yyyy').format(expiryDate!.toLocal())}';
    }

    return 'Valid till ${DateFormat('dd/MM/yyyy').format(expiryDate!.toLocal())}';
  }
}
