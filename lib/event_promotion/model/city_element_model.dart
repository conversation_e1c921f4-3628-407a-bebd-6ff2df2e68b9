import 'package:equatable/equatable.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class CityElementModel extends Equatable {
  const CityElementModel({
    this.id,
    this.eventId,
    this.cityId,
    this.createdAt,
    this.updatedAt,
    this.city,
  });

  final int? id;
  final int? eventId;
  final int? cityId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CountryModel? city;

  CityElementModel copyWith({
    int? id,
    int? eventId,
    int? cityId,
    DateTime? createdAt,
    DateTime? updatedAt,
    CountryModel? city,
  }) {
    return CityElementModel(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      cityId: cityId ?? this.cityId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      city: city ?? this.city,
    );
  }

  factory CityElementModel.fromJson(Map<String, dynamic> json) {
    return CityElementModel(
      id: json["id"],
      eventId: json["event_id"],
      cityId: json["city_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      city: json["city"] == null ? null : CountryModel.fromJson(json["city"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "event_id": eventId,
        "city_id": cityId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "city": city?.toJson(),
      };

  @override
  String toString() {
    return "$id, $eventId, $cityId, $createdAt, $updatedAt, $city, ";
  }

  @override
  List<Object?> get props => [
        id,
        eventId,
        cityId,
        createdAt,
        updatedAt,
        city,
      ];
}
