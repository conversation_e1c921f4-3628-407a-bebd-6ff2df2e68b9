import 'package:equatable/equatable.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';

class CategoryElementModel extends Equatable {
  const CategoryElementModel({
    this.id,
    this.eventId,
    this.categoryId,
    this.createdAt,
    this.updatedAt,
    this.category,
  });

  final int? id;
  final int? eventId;
  final int? categoryId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CategoryModel? category;

  CategoryElementModel copyWith({
    int? id,
    int? eventId,
    int? categoryId,
    DateTime? createdAt,
    DateTime? updatedAt,
    CategoryModel? category,
  }) {
    return CategoryElementModel(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      categoryId: categoryId ?? this.categoryId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      category: category ?? this.category,
    );
  }

  factory CategoryElementModel.fromJson(Map<String, dynamic> json) {
    return CategoryElementModel(
      id: json["id"],
      eventId: json["event_id"],
      categoryId: json["category_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      category: json["category"] == null ? null : CategoryModel.fromJson(json["category"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "event_id": eventId,
        "category_id": categoryId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "category": category?.toJson(),
      };

  @override
  String toString() {
    return "$id, $eventId, $categoryId, $createdAt, $updatedAt, $category, ";
  }

  @override
  List<Object?> get props => [
        id,
        eventId,
        categoryId,
        createdAt,
        updatedAt,
        category,
      ];
}
