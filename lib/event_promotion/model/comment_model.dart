import 'package:equatable/equatable.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class CommentModel extends Equatable {
  const CommentModel({
    this.id,
    this.userId,
    this.message,
    this.repliedTo,
    this.eventId,
    this.createdAt,
    this.updatedAt,
    this.user,
    this.replies,
  });

  final int? id;
  final int? userId;
  final String? message;
  final int? repliedTo;
  final int? eventId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final UserModel? user;
  final List<CommentModel>? replies;

  CommentModel copyWith({
    int? id,
    int? userId,
    String? message,
    int? repliedTo,
    int? eventId,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserModel? user,
    List<CommentModel>? replies,
  }) {
    return CommentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      message: message ?? this.message,
      repliedTo: repliedTo ?? this.repliedTo,
      eventId: eventId ?? this.eventId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      user: user ?? this.user,
      replies: replies ?? this.replies,
    );
  }

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json["id"],
      userId: json["user_id"],
      message: json["message"],
      repliedTo: json["replied_to"],
      eventId: json["event_id"],
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      user: json["user"] == null ? null : UserModel.fromJson(json["user"]),
      replies:
          json["replies"] == null ? [] : List<CommentModel>.from(json["replies"]!.map((x) => CommentModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "user_id": userId,
        "message": message,
        "replied_to": repliedTo,
        "event_id": eventId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "user": user?.toJson(),
        "replies": replies == null ? [] : replies?.map((x) => x).toList(),
      };

  @override
  String toString() {
    return "$id, $userId, $message, $repliedTo, $eventId, $createdAt, $updatedAt, $user, $replies, ";
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        message,
        repliedTo,
        eventId,
        createdAt,
        updatedAt,
        user,
        replies,
      ];

  String get timeAgo {
    final now = DateTime.now().toUtc();
    final diff = now.difference(createdAt ?? DateTime.now());

    if (diff.inSeconds < 60) {
      return 'now';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes} min${diff.inMinutes > 1 ? 's' : ''} ago';
    } else if (diff.inHours < 24) {
      return '${diff.inHours} hr${diff.inHours > 1 ? 's' : ''} ago';
    } else if (diff.inDays < 7) {
      return '${diff.inDays} day${diff.inDays > 1 ? 's' : ''} ago';
    } else {
      return '${createdAt?.day}/${createdAt?.month}/${createdAt?.year}';
    }
  }
}
