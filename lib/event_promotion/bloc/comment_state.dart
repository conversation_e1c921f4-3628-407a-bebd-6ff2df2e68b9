part of 'comment_bloc.dart';

sealed class CommentState extends Equatable {
  const CommentState();
  R when<R>({
    required R Function() initial,
    required R Function() loading,
    required R Function(CommentsLoaded) loaded,
    required R Function(HttpFailure message) error,
  }) {
    if (this is CommentInitial) return initial();
    if (this is CommentLoading) return loading();
    if (this is CommentsLoaded) return loaded((this as CommentsLoaded));
    if (this is CommentError) return error((this as CommentError).failure);
    throw Exception('Unrecognized CommentState: $runtimeType');
  }

  R? whenOrNull<R>({
    R Function()? initial,
    R Function()? loading,
    R Function(List<CommentModel>)? loaded,
    R Function(HttpFailure? failure)? error,
  }) {
    if (this is CommentInitial) return initial?.call();
    if (this is CommentLoading) return loading?.call();
    if (this is CommentsLoaded) return loaded?.call((this as CommentsLoaded).comments);
    if (this is CommentError) return error?.call((this as CommentError).failure);
    return null;
  }

  @override
  List<Object?> get props => [];
}

final class CommentInitial extends CommentState {}

class CommentLoading extends CommentState {}

class CommentsLoaded extends CommentState {
  final List<CommentModel> comments;
  final bool hasReachedMax;
  final int? page;
  final int? perPage;
  final bool isLoadingMore;
  final HttpFailure? loadMoreFailure;
  final int? totalComments;

  const CommentsLoaded({
    this.comments = const [],
    this.hasReachedMax = false,
    this.page = 1,
    this.perPage = 10,
    this.isLoadingMore = false,
    this.loadMoreFailure,
    this.totalComments,
  });

  CommentsLoaded copyWith({
    List<CommentModel>? comments,
    bool? hasReachedMax,
    int? page,
    int? perPage,
    bool? isLoadingMore,
    HttpFailure? loadMoreFailure,
    int? totalComments,
  }) {
    return CommentsLoaded(
      comments: comments ?? this.comments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreFailure: loadMoreFailure ?? this.loadMoreFailure,
      totalComments: totalComments ?? this.totalComments,
    );
  }

  @override
  List<Object?> get props => [comments, hasReachedMax, page, perPage, isLoadingMore, loadMoreFailure, totalComments];
}

class CommentError extends CommentState {
  final HttpFailure failure;
  const CommentError({required this.failure});

  CommentError copyWith({
    HttpFailure? message,
  }) {
    return CommentError(
      failure: message ?? failure,
    );
  }
}
