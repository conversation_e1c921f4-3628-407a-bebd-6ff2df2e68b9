// ignore_for_file: public_member_api_docs, sort_constructors_first

part of 'event_bloc.dart';

sealed class EventState extends Equatable {
  const EventState();
  R when<R>({
    required R Function() initial,
    required R Function() loading,
    required R Function(EventLoaded) loaded,
    required R Function(HttpFailure message) error,
  }) {
    if (this is EventInitial) return initial();
    if (this is EventLoading) return loading();
    if (this is EventLoaded) return loaded((this as EventLoaded));
    if (this is EventError) return error((this as EventError).failure);
    throw Exception('Unrecognized EventState: $runtimeType');
  }

  R? whenOrNull<R>({
    R Function()? initial,
    R Function()? loading,
    R Function(List<EventModel>)? loaded,
    R Function(HttpFailure? failure)? error,
  }) {
    if (this is EventInitial) return initial?.call();
    if (this is EventLoading) return loading?.call();
    if (this is EventLoaded) return loaded?.call((this as EventLoaded).events);
    if (this is EventError) return error?.call((this as EventError).failure);
    return null;
  }

  @override
  List<Object?> get props => [];
}

final class EventInitial extends EventState {}

class EventLoading extends EventState {}

class EventLoaded extends EventState {
  final List<EventModel> events;
  final List<EventModel> myEvents;
  final bool isStackLoading;
  final bool hasReachedMax;
  final int? page;
  final bool isLoadingMore;
  final HttpFailure? loadMoreFailure;

  final bool hasReachedMaxMyEvent;
  final int? pageMyEvent;
  final bool isLoadingMoreMyEvent;
  final HttpFailure? loadMoreFailureMyEvent;
  final HttpFailure? myEventFailure;

  const EventLoaded({
    this.events = const [],
    this.myEvents = const [],
    this.isStackLoading = false,
    this.hasReachedMax = false,
    this.page = 1,
    this.isLoadingMore = false,
    this.loadMoreFailure,
    this.hasReachedMaxMyEvent = false,
    this.pageMyEvent,
    this.isLoadingMoreMyEvent = false,
    this.loadMoreFailureMyEvent,
    this.myEventFailure,
  });

  EventLoaded copyWith({
    List<EventModel>? events,
    List<EventModel>? myEvents,
    bool? isStackLoading,
    bool? hasReachedMax,
    int? page,
    bool? isLoadingMore,
    HttpFailure? loadMoreFailure,
    bool? hasReachedMaxMyEvent,
    int? pageMyEvent,
    bool? isLoadingMoreMyEvent,
    HttpFailure? loadMoreFailureMyEvent,
    HttpFailure? myEventFailure,
  }) {
    return EventLoaded(
      events: events ?? this.events,
      myEvents: myEvents ?? this.myEvents,
      isStackLoading: isStackLoading ?? this.isStackLoading,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      page: page ?? this.page,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreFailure: loadMoreFailure ?? this.loadMoreFailure,
      hasReachedMaxMyEvent: hasReachedMaxMyEvent ?? this.hasReachedMaxMyEvent,
      pageMyEvent: pageMyEvent ?? this.pageMyEvent,
      isLoadingMoreMyEvent: isLoadingMoreMyEvent ?? this.isLoadingMoreMyEvent,
      loadMoreFailureMyEvent: loadMoreFailureMyEvent ?? this.loadMoreFailureMyEvent,
      myEventFailure: myEventFailure ?? this.myEventFailure,
    );
  }

  @override
  List<Object?> get props => [
        events,
        isStackLoading,
        hasReachedMax,
        page,
        isLoadingMore,
        loadMoreFailure,
        myEventFailure,
        myEvents,
        hasReachedMaxMyEvent,
        pageMyEvent,
        isLoadingMoreMyEvent,
        loadMoreFailureMyEvent
      ];
}

class EventError extends EventState {
  final HttpFailure failure;
  const EventError({required this.failure});

  EventError copyWith({
    HttpFailure? message,
  }) {
    return EventError(
      failure: message ?? failure,
    );
  }
}
