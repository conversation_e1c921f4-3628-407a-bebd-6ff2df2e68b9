part of 'comment_bloc.dart';

sealed class CommentEvent extends Equatable {
  const CommentEvent();

  @override
  List<Object> get props => [];
}

class LoadComments extends CommentEvent {
  final int eventId;
  const LoadComments({required this.eventId});
}

final class CommentsLoadMore extends CommentEvent {
  final int eventId;
  const CommentsLoadMore({required this.eventId});
}

class AddComment extends CommentEvent {
  final CommentModel comment;

  const AddComment({required this.comment});
}

class UpdateComment extends CommentEvent {
  final CommentModel comment;

  const UpdateComment({required this.comment});
}

class DeleteComment extends CommentEvent {
  final int? commentId;
  final int? repliedTo;

  const DeleteComment({this.commentId, this.repliedTo});
}
