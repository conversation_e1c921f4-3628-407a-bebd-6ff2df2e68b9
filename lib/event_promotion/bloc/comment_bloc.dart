import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/event_promotion/model/comment_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';

part 'comment_event.dart';
part 'comment_state.dart';

class CommentBloc extends Bloc<CommentEvent, CommentState> {
  CommentBloc(this.eventRepository) : super(CommentInitial()) {
    on<LoadComments>(_loadComments);
    on<CommentsLoadMore>(_onLoadMoreComment);
    on<AddComment>(_onAddComment);
    on<UpdateComment>(_onUpdateComment);
    on<DeleteComment>(_onDeleteComment);
  }
  final IEventRepository eventRepository;

  Future<void> _loadComments(LoadComments event, Emitter<CommentState> emit) async {
    emit(CommentLoading());
    final failOrSuccess = await eventRepository.commentList(eventId: event.eventId);
    emit(
      failOrSuccess.fold(
        (l) => CommentError(failure: l),
        (r) => CommentsLoaded(
            comments: r.data ?? [], hasReachedMax: (r.data?.length ?? 0) < 10, totalComments: r.totalComments),
      ),
    );
  }

  Future<void> _onLoadMoreComment(
    CommentsLoadMore event,
    Emitter<CommentState> emit,
  ) async {
    if (state is! CommentsLoaded) return;
    final newLoadedState = state as CommentsLoaded;

    emit(newLoadedState.copyWith(isLoadingMore: true));

    final failOrSucess = await eventRepository.commentList(
      page: (newLoadedState.page ?? 0) + 1,
      eventId: event.eventId,
    );

    emit(
      failOrSucess.fold(
        (l) => newLoadedState.copyWith(isLoadingMore: false, loadMoreFailure: l),
        (success) => newLoadedState.copyWith(
          comments: [...newLoadedState.comments, ...success.data ?? []],
          hasReachedMax: (success.data?.length ?? 0) < 10,
          isLoadingMore: false,
          page: (newLoadedState.page ?? 0) + 1,
        ),
      ),
    );
  }

  void _onAddComment(
    AddComment event,
    Emitter<CommentState> emit,
  ) async {
    final loadedState = state as CommentsLoaded;
    emit(loadedState.copyWith(
        comments: [...loadedState.comments, event.comment], totalComments: (loadedState.totalComments ?? 0) + 1));
  }

  void _onUpdateComment(
    UpdateComment event,
    Emitter<CommentState> emit,
  ) async {
    final loadedState = state as CommentsLoaded;
    final totalComment = loadedState.totalComments ?? 0;
    emit(
      loadedState.copyWith(
        comments: [
          ...loadedState.comments.map(
              (e) => e.id == event.comment.repliedTo ? e.copyWith(replies: [...e.replies ?? [], event.comment]) : e)
        ],
        totalComments: (totalComment + 1),
      ),
    );
  }

  void _onDeleteComment(
    DeleteComment event,
    Emitter<CommentState> emit,
  ) async {
    final loadedState = state as CommentsLoaded;
    if (event.repliedTo != null) {
      emit(
        loadedState.copyWith(
          comments: [
            ...loadedState.comments.map((e) => e.id == event.repliedTo
                ? e.copyWith(
                    replies: [
                      ...e.replies?.where((element) => element.id != event.commentId).toList() ?? [],
                    ],
                  )
                : e)
          ],
          totalComments: (loadedState.totalComments ?? 1) > 0 ? (loadedState.totalComments ?? 1) - 1 : 0,
        ),
      );
    } else {
      final totalReplies =
          loadedState.comments.firstWhere((element) => element.id == event.commentId).replies?.length ?? 0;
      emit(
        loadedState.copyWith(
          comments: [...loadedState.comments.where((element) => element.id != event.commentId)],
          totalComments:
              (loadedState.totalComments ?? 1) > 0 ? (loadedState.totalComments ?? 1) - (totalReplies + 1) : 0,
        ),
      );
    }
  }
}
