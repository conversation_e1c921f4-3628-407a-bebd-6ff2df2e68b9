part of 'event_bloc.dart';

sealed class EventEvent extends Equatable {
  const EventEvent();

  @override
  List<Object> get props => [];
}

class LoadEvents extends EventEvent {
  final String? search;
  const LoadEvents({this.search});
}

final class EventsLoadMore extends EventEvent {
  final String? search;
  const EventsLoadMore({this.search});
}

class LoadMyEvents extends EventEvent {
  final String? search;
  const LoadMyEvents({this.search});
}

final class LoadMoreMyEvents extends EventEvent {
  final String? search;
  const LoadMoreMyEvents({this.search});
}

class AddEvent extends EventEvent {
  final EventModel event;

  const AddEvent({required this.event});
}

class EditEvent extends EventEvent {
  final EventModel event;

  const EditEvent({required this.event});
}

class DeleteEvent extends EventEvent {
  final int eventId;
  const DeleteEvent({required this.eventId});
}

class LikeEvent extends EventEvent {
  final int eventId;
  const LikeEvent({required this.eventId});
}

class UpdateCommentCount extends EventEvent {
  final int eventId;
  final int? commentCount;
  const UpdateCommentCount({required this.eventId, this.commentCount});
}
