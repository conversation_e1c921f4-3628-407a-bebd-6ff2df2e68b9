import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';

part 'event_event.dart';
part 'event_state.dart';

class EventBloc extends Bloc<EventEvent, EventState> {
  EventBloc(this.eventRepository) : super(EventInitial()) {
    on<LoadEvents>(_loadEvents);
    on<EventsLoadMore>(_onLoadMore);
    on<LoadMyEvents>(_loadMyEvents);
    on<LoadMoreMyEvents>(_onLoadMoreMyEvents);
    on<AddEvent>(_onAddEvent);
    on<EditEvent>(_onEditEvent);
    on<DeleteEvent>(_onDelete);
    on<UpdateCommentCount>(_onUpdateCommentCount);
    on<LikeEvent>(_onLikeEvent);
  }
  final IEventRepository eventRepository;

  Future<void> _loadEvents(LoadEvents event, Emitter<EventState> emit) async {
    emit(EventLoading());
    final failOrSuccess = await eventRepository.eventList(search: event.search);
    emit(
      failOrSuccess.fold(
        (l) => EventError(failure: l),
        (r) => EventLoaded(events: r.data ?? [], hasReachedMax: (r.data?.length ?? 0) < 10),
      ),
    );
  }

  Future<void> _onLoadMore(
    EventsLoadMore event,
    Emitter<EventState> emit,
  ) async {
    if (state is! EventLoaded) return;
    final newLoadedState = state as EventLoaded;

    emit(newLoadedState.copyWith(isLoadingMore: true));

    final failOrSucess = await eventRepository.eventList(
      page: (newLoadedState.page ?? 0) + 1,
      search: event.search,
    );

    emit(
      failOrSucess.fold(
        (l) => newLoadedState.copyWith(isLoadingMore: false, loadMoreFailure: l),
        (success) => newLoadedState.copyWith(
          events: [...newLoadedState.events, ...success.data ?? []],
          hasReachedMax: (success.data?.length ?? 0) < 10,
          isLoadingMore: false,
          page: (newLoadedState.page ?? 0) + 1,
        ),
      ),
    );
  }

  Future<void> _loadMyEvents(LoadMyEvents event, Emitter<EventState> emit) async {
    final loaddedState = state is EventLoaded ? state as EventLoaded : const EventLoaded();
    emit(EventLoading());
    final failOrSuccess = await eventRepository.eventList(search: event.search, isOwn: 1);
    emit(
      failOrSuccess.fold(
        (l) => loaddedState.copyWith(myEventFailure: l),
        (r) => loaddedState.copyWith(
          myEvents: r.data ?? [],
          hasReachedMaxMyEvent: (r.data?.length ?? 0) < 10,
          pageMyEvent: 1,
        ),
      ),
    );
  }

  Future<void> _onLoadMoreMyEvents(
    LoadMoreMyEvents event,
    Emitter<EventState> emit,
  ) async {
    if (state is! EventLoaded) return;
    final newLoadedState = state as EventLoaded;

    emit(newLoadedState.copyWith(isLoadingMoreMyEvent: true));

    final failOrSucess = await eventRepository.eventList(
      page: (newLoadedState.pageMyEvent ?? 0) + 1,
      search: event.search,
      isOwn: 1,
    );

    emit(
      failOrSucess.fold(
        (l) => newLoadedState.copyWith(isLoadingMoreMyEvent: false, loadMoreFailureMyEvent: l),
        (success) => newLoadedState.copyWith(
          myEvents: [...newLoadedState.myEvents, ...success.data ?? []],
          hasReachedMaxMyEvent: (success.data?.length ?? 0) < 10,
          isLoadingMoreMyEvent: false,
          pageMyEvent: (newLoadedState.pageMyEvent ?? 0) + 1,
        ),
      ),
    );
  }

  void _onAddEvent(
    AddEvent event,
    Emitter<EventState> emit,
  ) async {
    final loadedState = state as EventLoaded;
    emit(loadedState.copyWith(events: [event.event, ...loadedState.events]));
  }

  void _onEditEvent(
    EditEvent event,
    Emitter<EventState> emit,
  ) async {
    final loadedState = state as EventLoaded;
    emit(
      loadedState.copyWith(
        events: loadedState.events.map((e) => e.id == event.event.id ? event.event : e).toList(),
        myEvents: loadedState.myEvents.map((e) => e.id == event.event.id ? event.event : e).toList(),
      ),
    );
  }

  Future<void> _onDelete(
    DeleteEvent event,
    Emitter<EventState> emit,
  ) async {
    final loadedState = state as EventLoaded;
    emit(loadedState.copyWith(isStackLoading: true));
    final failOrSuccess = await eventRepository.deleteEvent(eventId: event.eventId);
    emit(
      failOrSuccess.fold((l) => EventError(failure: l), (r) {
        final updatedEvents = loadedState.events.where((element) => element.id != event.eventId).toList();
        final updatedMyEvents = loadedState.myEvents.where((element) => element.id != event.eventId).toList();
        Utility.toast(message: r.message);

        return loadedState.copyWith(events: updatedEvents, myEvents: updatedMyEvents, isStackLoading: false);
      }),
    );
  }

  Future<void> _onLikeEvent(
    LikeEvent event,
    Emitter<EventState> emit,
  ) async {
    final loadedState = state as EventLoaded;
    emit(
      loadedState.copyWith(
        events: loadedState.events
            .map((e) => e.id == event.eventId
                ? e.copyWith(
                    isLiked: e.isLiked == 0 ? 1 : 0,
                    likesCount: e.isLiked == 0 ? (e.likesCount ?? 0) + 1 : (e.likesCount ?? 0) - 1)
                : e)
            .toList(),
        myEvents: loadedState.myEvents
            .map((e) => e.id == event.eventId
                ? e.copyWith(
                    isLiked: e.isLiked == 0 ? 1 : 0,
                    likesCount: e.isLiked == 0 ? (e.likesCount ?? 0) + 1 : (e.likesCount ?? 0) - 1)
                : e)
            .toList(),
      ),
    );
  }

  void _onUpdateCommentCount(
    UpdateCommentCount event,
    Emitter<EventState> emit,
  ) async {
    final loadedState = state as EventLoaded;
    emit(
      loadedState.copyWith(
        events: loadedState.events
            .map((e) => e.id == event.eventId ? e.copyWith(totalComments: event.commentCount) : e)
            .toList(),
        myEvents: loadedState.myEvents
            .map((e) => e.id == event.eventId ? e.copyWith(totalComments: event.commentCount) : e)
            .toList(),
      ),
    );
  }
}
