import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/widget/common_button.dart';

class DeleteEventDialog extends StatelessWidget {
  const DeleteEventDialog({super.key, this.isPopTwice = false, required this.eventId});
  final int eventId;
  final bool isPopTwice;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      backgroundColor: AppColors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Delete Event',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(10),
            Text(
              'Are you sure you want to delete?',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const Gap(20),
            Row(
              children: [
                Expanded(
                  child: CommonButton(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    text: 'Cancel',
                  ),
                ),
                const Gap(15),
                Expanded(
                  child: BlocBuilder<EventBloc, EventState>(
                    builder: (context, state) {
                      return state.when(
                        initial: () => const SizedBox.shrink(),
                        loading: () => const SizedBox.shrink(),
                        loaded: (newState) {
                          return CommonButton(
                            isLoading: newState.isStackLoading,
                            onTap: () {
                              context.read<EventBloc>().add(DeleteEvent(eventId: eventId));
                              if (state is EventError || (state is EventLoaded && !state.isStackLoading)) {
                                Navigator.pop(context);
                                if (isPopTwice) {
                                  Navigator.pop(context);
                                }
                              }
                            },
                            text: 'Delete',
                            removeShadow: true,
                          );
                        },
                        error: (message) => const SizedBox.shrink(),
                      );
                    },
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
