import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/comment_bloc.dart';
import 'package:leiuniverse/event_promotion/model/comment_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/injector/injector.dart';

class CommentWidget extends StatefulWidget {
  const CommentWidget({
    super.key,
    this.comment,
    this.onReplyTap,
    this.isReplyView = false,
    this.isMyComment = false,
  });
  final CommentModel? comment;
  final bool isMyComment;
  final bool isReplyView;
  final void Function()? onReplyTap;

  @override
  State<CommentWidget> createState() => _CommentWidgetState();
}

class _CommentWidgetState extends State<CommentWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isReplyView) ...[
          const Divider(
            height: 1,
            thickness: 1,
            color: AppColors.border,
          ),
          const Gap(16),
        ],
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Utility.imageLoader(
              url: widget.comment?.user?.profileImage ?? '',
              placeholder: AppAssets.placeholderImage,
              height: 36,
              width: 36,
              shape: BoxShape.circle,
              isShapeCircular: true,
              fit: BoxFit.cover,
            ),
            const Gap(8),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.comment?.user?.name ?? '',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 15),
                  ),
                  Text(
                    widget.comment?.timeAgo ?? '',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.subText,
                          fontSize: 14,
                        ),
                  )
                ],
              ),
            ),
            if (widget.isMyComment)
              PopupMenuButton(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                color: AppColors.white,
                elevation: 6,
                position: PopupMenuPosition.under,
                child: const Icon(
                  Icons.more_vert_rounded,
                  color: AppColors.black,
                  size: 20,
                ),
                onSelected: (value) {
                  if (value == 0) {
                    log('commnet id ${widget.comment?.id}');
                    log('reply to ${widget.comment?.repliedTo}');

                    deleteComment(context, commentId: widget.comment?.id ?? 0, replyTo: widget.comment?.repliedTo);
                  }
                },
                itemBuilder: (context) {
                  return [
                    PopupMenuItem(
                      value: 0,
                      child: Text(
                        'Delete',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontSize: 15),
                      ),
                    ),
                  ];
                },
              ),
          ],
        ),
        const Gap(8),
        Text(
          widget.comment?.message ?? '',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: 14),
        ),
        if (!widget.isReplyView)
          InkWell(
            onTap: widget.onReplyTap,
            overlayColor: WidgetStateProperty.all(AppColors.transparent),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 8, 8, 8),
              child: Text(
                'Reply',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.primary,
                    ),
              ),
            ),
          ),
        ListView.separated(
          shrinkWrap: true,
          padding: const EdgeInsets.only(left: 40),
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.comment?.replies?.length ?? 0,
          itemBuilder: (context, index) {
            return CommentWidget(
              comment: widget.comment?.replies?[index],
              isReplyView: true,
              isMyComment: widget.isMyComment,
            );
          },
          separatorBuilder: (context, index) {
            return const Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Divider(
                height: 0,
              ),
            );
          },
        ),
        if (!widget.isReplyView) ...[
          const Gap(12),
          const Divider(
            height: 1,
            thickness: 1,
            color: AppColors.border,
          ),
        ],
      ],
    );
  }

  Future<void> deleteComment(BuildContext context, {required int commentId, int? replyTo}) async {
    final response = await getIt<IEventRepository>().deleteComment(
      commentId: commentId,
    );

    await response.fold(
      (l) {
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.status == 1) {
          context.read<CommentBloc>().add(DeleteComment(commentId: commentId, repliedTo: replyTo));
        }
      },
    );
  }
}
