import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class EventPromotionWidget extends StatelessWidget {
  const EventPromotionWidget({
    super.key,
    this.onTap,
    this.onEditTap,
    this.onDeleteTap,
    this.onLikeTap,
    this.isFromMyEvent = false,
    required this.event,
  });
  final void Function()? onTap;
  final void Function()? onEditTap;
  final void Function()? onDeleteTap;
  final void Function()? onLikeTap;
  final bool isFromMyEvent;
  final EventModel event;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: List.generate(
                  event.images?.length ?? 0,
                  (index) {
                    return Padding(
                      padding: const EdgeInsets.only(right: 14),
                      child: Utility.imageLoader(
                        url: event.images?[index].imageUrl ?? '',
                        placeholder: AppAssets.placeholderImage,
                        borderRadius: BorderRadius.circular(8),
                        height: 147,
                        width: 236,
                        fit: BoxFit.cover,
                      ),
                    );
                  },
                ),
              ),
            ),
            if (event.images?.isNotEmpty ?? false) const Gap(14),
            Text(
              event.name ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            if (event.eventLocation.isNotEmpty) ...[
              const Gap(8),
              Row(
                children: [
                  const AppSvgImage(
                    AppAssets.locationIcon,
                  ),
                  const Gap(6),
                  Text(
                    event.eventLocation,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ],
            const Gap(14),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconTextWidget(
                  onTap: onLikeTap,
                  icon: event.isLiked == 1 ? AppAssets.likeHandIcon : AppAssets.dislikeHandIcon,
                  text: '${event.likesCount ?? 0} Likes',
                ),
                const Gap(14),
                IconTextWidget(
                  icon: AppAssets.messageIcon,
                  text: '${event.totalComments ?? 0} Comments',
                ),
              ],
            ),
            const Gap(14),
            const Divider(
              height: 0,
            ),
            if (!isFromMyEvent) ...[
              const Gap(14),
              UserInfoWidget(user: event.user),
            ],
            if (isFromMyEvent) ...[
              const Gap(12),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: event.validTillDate.isNotEmpty
                        ? Text(
                            event.validTillDate,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: AppColors.primary,
                                ),
                          )
                        : const SizedBox.shrink(),
                  ),
                  InkWell(
                    onTap: onEditTap,
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: const AppSvgImage(AppAssets.editIcon),
                  ),
                  const Gap(16),
                  InkWell(
                    onTap: onDeleteTap,
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: const AppSvgImage(AppAssets.deleteIcon),
                  ),
                ],
              )
            ]
          ],
        ),
      ),
    );
  }
}
