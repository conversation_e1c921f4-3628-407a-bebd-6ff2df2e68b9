import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/file_extention.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/event_promotion/response/comment_list_response.dart';
import 'package:leiuniverse/event_promotion/response/comment_response.dart';
import 'package:leiuniverse/event_promotion/response/event_list_response.dart';
import 'package:leiuniverse/event_promotion/response/event_response.dart';
import 'package:leiuniverse/login/response/common_response.dart';

part 'event_repository.dart';

abstract class IEventRepository {
  IEventRepository(this.client);
  final Client client;

  ApiResult<EventListResponse> eventList({
    int page = 1,
    int perPage = 10,
    String? search,
    int isOwn = 0,
  });

  ApiResult<EventResponse> createEvent({
    required PostOrEventModel event,
  });

  ApiResult<EventResponse> updateEvent({
    required int eventId,
    PostOrEventModel? event,
  });

  ApiResult<CommonResponse> deleteEvent({
    required int eventId,
  });

  ApiResult<CommonResponse> likeEvent({
    required int eventId,
  });

  ApiResult<CommentListResponse> commentList({
    int page = 1,
    int perPage = 10,
    int? eventId,
  });

  ApiResult<CommentResponse> addComment({
    required int eventId,
    required String message,
    int? repliedTo,
  });

  ApiResult<CommonResponse> deleteComment({
    required int commentId,
  });
}
