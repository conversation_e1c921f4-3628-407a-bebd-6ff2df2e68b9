part of 'i_event_repository.dart';

@Injectable(as: IEventRepository)
class EventRepository extends IEventRepository {
  EventRepository(super.client);

  @override
  ApiResult<EventListResponse> eventList({int page = 1, int perPage = 10, String? search, int isOwn = 0}) async {
    final response = await client.get(
      url: AppStrings.eventList,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
        'is_owned': isOwn.toString(),
      },
    );
    return response.parseResponse(EventListResponse.fromJson);
  }

  @override
  ApiResult<EventResponse> createEvent({required PostOrEventModel event}) async {
    final List<PickedFileModel> fileList = event.images?.where((element) => element.file != null).toList() ?? [];

    // --- Replicated categoryIds logic ---
    final childSubCategories = event.childSubCategory;
    final fallbackCategoryId = event.subCategory?.id ?? event.category?.id;

    final List<int> categoryIds = (childSubCategories != null && childSubCategories.isNotEmpty)
        ? childSubCategories.map((e) => e.id ?? 0).where((id) => id != 0).toList()
        : (fallbackCategoryId != null && fallbackCategoryId != 0)
            ? [fallbackCategoryId]
            : [];

    final response = await client.multipart(
      url: AppStrings.createEvent,
      files: [
        for (var i = 0; i < fileList.length; i++)
          if (fileList[i].file != null) MapEntry('images[$i]', await fileList[i].file!.compressFile()),
      ],
      requests: {
        if (event.eventName != null) 'name': event.eventName!.trim(),
        if (event.description != null) 'short_description': event.description!.trim(),
        if (event.state?.id != null && event.state?.id != 0) 'state_id': event.state!.id.toString(),
        if (event.country?.id != null && event.country?.id != 0) 'country_id': event.country!.id.toString(),

        // City IDs
        if (event.cities != null && event.cities!.isNotEmpty)
          for (int i = 0; i < event.cities!.length; i++) 'city_id[$i]': event.cities![i].id.toString(),

        // Category IDs (using same logic as advertisement)
        for (int i = 0; i < categoryIds.length; i++) 'category_id[$i]': categoryIds[i].toString(),
      },
    );

    return response.parseResponse(EventResponse.fromJson);
  }

  @override
  ApiResult<EventResponse> updateEvent({required int eventId, PostOrEventModel? event}) async {
    final List<PickedFileModel> fileList = event?.images?.where((element) => element.file != null).toList() ?? [];
    // --- Replicated categoryIds logic ---
    final childSubCategories = event?.childSubCategory;
    final fallbackCategoryId = event?.subCategory?.id ?? event?.category?.id;

    final List<int> categoryIds = (childSubCategories != null && childSubCategories.isNotEmpty)
        ? childSubCategories.map((e) => e.id ?? 0).where((id) => id != 0).toList()
        : (fallbackCategoryId != null && fallbackCategoryId != 0)
            ? [fallbackCategoryId]
            : [];
    final response = await client.multipart(
      url: AppStrings.updateEvent(eventId),
      files: [
        if (event?.images != null)
          for (var i = 0; i < fileList.length; i++)
            if (fileList[i].file != null) MapEntry('images[$i]', await fileList[i].file!.compressFile()),
      ],
      requests: {
        if (event?.eventName != null) 'name': event!.eventName!.trim(),
        if (event?.description != null) 'short_description': event!.description!.trim(),
        if (event?.state?.id != null && event?.state?.id != 0) 'state_id': event!.state!.id.toString(),
        if (event?.country?.id != null && event?.country?.id != 0) 'country_id': event!.country!.id.toString(),

        // City IDs
        if (event?.cities != null && event!.cities!.isNotEmpty)
          for (int i = 0; i < event.cities!.length; i++) 'city_id[$i]': event.cities![i].id.toString(),
        for (int i = 0; i < categoryIds.length; i++) 'category_id[$i]': categoryIds[i].toString(),
        if (event?.deleteImages != null && event!.deleteImages!.isNotEmpty)
          for (var i = 0; i < event.deleteImages!.length; i++) 'deleted_images[$i]': event.deleteImages![i].toString(),
      },
    );

    return response.parseResponse(EventResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteEvent({required int eventId}) async {
    final response = await client.delete(
      url: AppStrings.deleteEvent(eventId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> likeEvent({required int eventId}) async {
    final response = await client.post(
      url: AppStrings.likeEvent(eventId),
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommentListResponse> commentList({int page = 1, int perPage = 10, int? eventId}) async {
    final response = await client.get(
      url: AppStrings.commetsList,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (eventId != null) 'event_id': eventId.toString(),
      },
    );
    return response.parseResponse(CommentListResponse.fromJson);
  }

  @override
  ApiResult<CommentResponse> addComment({required int eventId, required String message, int? repliedTo}) async {
    final response = await client.post(
      url: AppStrings.addComment,
      requests: {
        'event_id': eventId.toString(),
        'message': message.trim(),
        if (repliedTo != null) 'replied_to': repliedTo.toString(),
      },
    );

    return response.parseResponse(CommentResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteComment({required int commentId}) async {
    final response = await client.delete(
      url: AppStrings.deleteComment(commentId),
    );
    return response.parseResponse(CommonResponse.fromJson);
  }
}
