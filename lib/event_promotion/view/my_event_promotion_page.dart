import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/event_promotion/view/create_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/view/event_promotion_details_page.dart';
import 'package:leiuniverse/event_promotion/widget/event_promotion_widget.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';
import 'package:visibility_detector/visibility_detector.dart';

class MyEventPromotionPage extends StatefulWidget {
  const MyEventPromotionPage({super.key});

  @override
  State<MyEventPromotionPage> createState() => _MyEventPromotionPageState();
}

class _MyEventPromotionPageState extends State<MyEventPromotionPage> {
  final isStackloading = ValueNotifier<bool>(false);
  final searchController = TextEditingController();

  Future<void> likeUnlike(int eventId) async {
    isStackloading.value = true;
    final response = await getIt<IEventRepository>().likeEvent(
      eventId: eventId,
    );

    await response.fold(
      (l) {
        isStackloading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isStackloading.value = false;
        if (r.status == 1) {
          context.read<EventBloc>().add(LikeEvent(eventId: eventId));
        }
      },
    );
  }

  @override
  void initState() {
    super.initState();
    context.read<EventBloc>().add(const LoadMyEvents());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'My Event & Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: BlocBuilder<EventBloc, EventState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Stack(
              children: [
                Column(
                  children: [
                    AppTextFormField(
                      controller: searchController,
                      hintText: 'Search',
                      fillColor: AppColors.white,
                      prefixIcon: const Padding(
                        padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                        child: AppSvgImage(
                          AppAssets.searchIcon,
                          color: AppColors.subText,
                        ),
                      ),
                      onChanged: (value) {
                        EasyDebounce.debounce(
                          'search_event',
                          const Duration(milliseconds: 500),
                          () {
                            context.read<EventBloc>().add(LoadMyEvents(search: value));
                          },
                        );
                      },
                    ),
                    state.when(
                      error: (message) => const Center(child: Text('Error')),
                      initial: () => const SizedBox(),
                      loading: () => const Expanded(
                        child: Center(
                          child: CustomProgressIndecator(
                            color: AppColors.primary,
                            size: 40,
                          ),
                        ),
                      ),
                      loaded: (newState) {
                        if (!newState.isLoadingMoreMyEvent && newState.myEvents.isEmpty) {
                          return Flexible(
                            child: Utility.noDataWidget(
                              context: context,
                              text: 'No Events Found',
                            ),
                          );
                        }
                        return Flexible(
                          child: RefreshIndicator(
                            onRefresh: () {
                              context.read<EventBloc>().add(LoadMyEvents(search: searchController.text.trim()));
                              return Future.value();
                            },
                            child: ListView.separated(
                              itemCount: newState.myEvents.length,
                              padding: const EdgeInsets.symmetric(vertical: 20),
                              physics: const AlwaysScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return (newState.myEvents.length) - 1 == index
                                    ? VisibilityDetector(
                                        key: Key(index.toString()),
                                        onVisibilityChanged: (VisibilityInfo info) {
                                          if (index == (newState.myEvents.length) - 1 &&
                                              !newState.hasReachedMaxMyEvent &&
                                              !newState.isLoadingMoreMyEvent) {
                                            context.read<EventBloc>().add(const LoadMoreMyEvents());
                                          }
                                        },
                                        child: Column(
                                          children: [
                                            eventView(newState.myEvents[index]),
                                            newState.isLoadingMoreMyEvent
                                                ? const CustomProgressIndecator(
                                                    color: AppColors.primary,
                                                    size: 40,
                                                  )
                                                : const SizedBox(),
                                          ],
                                        ),
                                      )
                                    : eventView(newState.myEvents[index]);
                              },
                              separatorBuilder: (context, index) {
                                return const Gap(14);
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
                ValueListenableBuilder(
                    valueListenable: isStackloading,
                    builder: (context, value, _) {
                      return value
                          ? const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40))
                          : const SizedBox.shrink();
                    })
              ],
            ),
          );
        },
      ),
    );
  }

  Widget eventView(EventModel? event) {
    return EventPromotionWidget(
      event: event ?? const EventModel(),
      isFromMyEvent: true,
      onLikeTap: () {
        likeUnlike(event?.id ?? 0);
      },
      onEditTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<EventBloc>(),
              child: CreateEventPromotionPage(event: event),
            ),
          ),
        );
      },
      onDeleteTap: () {
        AppDailogs.deleteEventDailog(context, eventId: event?.id ?? 0);
      },
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<EventBloc>(),
              child: EventPromotionDetailsPage(
                event: event,
              ),
            ),
          ),
        );
      },
    );
  }
}
