import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/comment_bloc.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_button.dart';

class WriteCommentView extends StatefulWidget {
  const WriteCommentView({super.key, required this.eventId, this.replyTo, this.totalComments = 0});
  final int eventId;
  final int? replyTo;
  final int totalComments;

  @override
  State<WriteCommentView> createState() => _WriteCommentViewState();
}

class _WriteCommentViewState extends State<WriteCommentView> {
  final commentController = TextEditingController();

  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 538 + MediaQuery.of(context).viewInsets.bottom, // fixed height
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 55,
                height: 5,
                decoration: BoxDecoration(
                  color: AppColors.border,
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
              const Gap(20),
              Text(
                'Write comment',
                style: Theme.of(context).textTheme.headlineLarge,
              ),
              const Gap(4),
              Text(
                'Please share your opinion about event/promotion',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Gap(20),
              AppTextFormField(
                title: 'Your comment',
                maxLines: 6,
                controller: commentController,
              ),
              const Gap(30),
              ValueListenableBuilder<bool>(
                  valueListenable: isButtonLoading,
                  builder: (context, loading, _) {
                    return CommonButton(
                      onTap: loading
                          ? null
                          : () {
                              if (commentController.text.trim().isNotEmpty) {
                                addComment(context);
                              } else {
                                Utility.toast(message: 'Please enter comment');
                              }
                            },
                      isLoading: loading,
                      text: 'Post Comment',
                    );
                  }),
              const Gap(10),
              InkWell(
                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                onTap: () => Navigator.pop(context),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Cancel',
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> addComment(BuildContext context) async {
    isButtonLoading.value = true;
    final response = await getIt<IEventRepository>().addComment(
      eventId: widget.eventId,
      message: commentController.text,
      repliedTo: widget.replyTo,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1 && r.data != null) {
          if (widget.replyTo != null) {
            context.read<CommentBloc>().add(UpdateComment(comment: r.data!));
            // context
            //     .read<EventBloc>()
            //     .add(UpdateCommentCount(eventId: widget.eventId, commentCount: widget.totalComments + 1));
          } else {
            context.read<CommentBloc>().add(AddComment(comment: r.data!));
          }
          Navigator.pop(context);
        }
        Utility.toast(message: r.message);
      },
    );
  }
}
