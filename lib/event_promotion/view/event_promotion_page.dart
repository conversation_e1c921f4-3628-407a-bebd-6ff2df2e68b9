import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/event_promotion/view/create_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/view/event_promotion_details_page.dart';
import 'package:leiuniverse/event_promotion/view/my_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/widget/event_promotion_widget.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:visibility_detector/visibility_detector.dart';

class EventPromotionPageWrapper extends StatelessWidget {
  const EventPromotionPageWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<EventBloc>()..add(const LoadEvents()),
      child: const EventPromotionPage(),
    );
  }
}

class EventPromotionPage extends StatefulWidget {
  const EventPromotionPage({super.key});

  @override
  State<EventPromotionPage> createState() => _EventPromotionPageState();
}

class _EventPromotionPageState extends State<EventPromotionPage> {
  final searchController = TextEditingController();
  final isStackloading = ValueNotifier<bool>(false);

  Future<void> likeUnlike(int eventId) async {
    isStackloading.value = true;
    final response = await getIt<IEventRepository>().likeEvent(
      eventId: eventId,
    );

    await response.fold(
      (l) {
        isStackloading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isStackloading.value = false;
        if (r.status == 1) {
          context.read<EventBloc>().add(LikeEvent(eventId: eventId));
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Event & Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
        actions: [
          CommonButton(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => BlocProvider.value(
                    value: context.read<EventBloc>(),
                    child: const MyEventPromotionPage(),
                  ),
                ),
              );
            },
            fontSize: 14,
            text: 'My Events',
            removeShadow: true,
            width: 83,
            padding: const EdgeInsets.symmetric(
              vertical: 4,
            ),
            margin: const EdgeInsets.only(right: 24, top: 13, bottom: 13),
            borderRadius: 6,
          ),
        ],
      ),
      body: BlocBuilder<EventBloc, EventState>(
        builder: (context, state) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
            child: Stack(
              children: [
                Column(
                  children: [
                    AppTextFormField(
                      controller: searchController,
                      hintText: 'Search',
                      fillColor: AppColors.white,
                      prefixIcon: const Padding(
                        padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                        child: AppSvgImage(
                          AppAssets.searchIcon,
                          color: AppColors.subText,
                        ),
                      ),
                      onChanged: (value) {
                        EasyDebounce.debounce(
                          'search_event',
                          const Duration(milliseconds: 500),
                          () {
                            context.read<EventBloc>().add(LoadEvents(search: value));
                          },
                        );
                      },
                    ),
                    state.when(
                      error: (message) => const Center(child: Text('Error')),
                      initial: () => const SizedBox(),
                      loading: () => const Expanded(
                        child: Center(
                          child: CustomProgressIndecator(
                            color: AppColors.primary,
                            size: 40,
                          ),
                        ),
                      ),
                      loaded: (newState) {
                        if (!newState.isLoadingMore && newState.events.isEmpty) {
                          return Flexible(
                            child: Utility.noDataWidget(
                              context: context,
                              text: 'No events found',
                            ),
                          );
                        }
                        return Flexible(
                          child: RefreshIndicator(
                            onRefresh: () {
                              context.read<EventBloc>().add(LoadEvents(search: searchController.text.trim()));
                              return Future.value();
                            },
                            child: ListView.separated(
                              separatorBuilder: (context, index) => const Gap(20),
                              itemCount: newState.events.length,
                              shrinkWrap: true,
                              padding: const EdgeInsets.symmetric(vertical: 20),
                              physics: const AlwaysScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return (newState.events.length) - 1 == index
                                    ? VisibilityDetector(
                                        key: Key(index.toString()),
                                        onVisibilityChanged: (VisibilityInfo info) {
                                          if (index == (newState.events.length) - 1 &&
                                              !newState.hasReachedMax &&
                                              !newState.isLoadingMore) {
                                            context.read<EventBloc>().add(const EventsLoadMore());
                                          }
                                        },
                                        child: Column(
                                          children: [
                                            eventView(newState.events[index]),
                                            newState.isLoadingMore
                                                ? const CustomProgressIndecator(
                                                    color: AppColors.primary,
                                                    size: 40,
                                                  )
                                                : const SizedBox(),
                                          ],
                                        ),
                                      )
                                    : eventView(newState.events[index]);
                              },
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
                ValueListenableBuilder(
                    valueListenable: isStackloading,
                    builder: (context, value, _) {
                      return value
                          ? const Center(child: CustomProgressIndecator(color: AppColors.primary, size: 40))
                          : const SizedBox.shrink();
                    })
              ],
            ),
          );
        },
      ),
      floatingActionButton: CommonButton(
        width: 141,
        borderRadius: 200,
        fontSize: 15,
        height: 40,
        padding: EdgeInsets.zero,
        isLessShadow: true,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => BlocProvider.value(
                value: context.read<EventBloc>(),
                child: const CreateEventPromotionPage(),
              ),
            ),
          );
        },
        icon: const AppSvgImage(
          AppAssets.postEventIcon,
        ),
        text: 'Post Event',
      ),
    );
  }

  Widget eventView(EventModel event) {
    return EventPromotionWidget(
      event: event,
      onLikeTap: () {
        likeUnlike(event.id ?? 0);
      },
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => BlocProvider.value(
              value: context.read<EventBloc>(),
              child: EventPromotionDetailsPage(event: event),
            ),
          ),
        );
      },
    );
  }
}
