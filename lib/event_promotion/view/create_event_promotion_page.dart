import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/core/common_model/post_event_model.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/market_place/view/target_buyers_view.dart';
import 'package:leiuniverse/market_place/view/target_location_view.dart';
import 'package:leiuniverse/setup_business/view/business_image_view.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class CreateEventPromotionPage extends StatefulWidget {
  const CreateEventPromotionPage({super.key, this.event});
  final EventModel? event;

  @override
  State<CreateEventPromotionPage> createState() => _CreateEventPromotionPageState();
}

class _CreateEventPromotionPageState extends State<CreateEventPromotionPage> {
  final _formKey = GlobalKey<FormState>();
  final adTitleController = TextEditingController();
  final adDescriptionController = TextEditingController();
  final createEventModel = ValueNotifier<PostOrEventModel>(PostOrEventModel());
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    adTitleController.dispose();
    adDescriptionController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    setData();
  }

  void setData() {
    if (widget.event != null) {
      if (widget.event?.name != null) {
        adTitleController.text = widget.event!.name!;
        createEventModel.value = createEventModel.value.copyWith(eventName: widget.event!.name);
      }
      if (widget.event?.shortDescription != null) {
        adDescriptionController.text = widget.event!.shortDescription!;
        createEventModel.value = createEventModel.value.copyWith(description: widget.event!.shortDescription);
      }
      if (widget.event?.images != null) {
        for (var element in widget.event!.images!) {
          createEventModel.value = createEventModel.value
              .copyWith(images: [...createEventModel.value.images ?? [], PickedFileModel(networkFile: element)]);
        }
      }
      if (widget.event?.cities != null) {
        for (var element in widget.event!.cities!) {
          createEventModel.value =
              createEventModel.value.copyWith(cities: [...createEventModel.value.cities ?? [], element.city!]);
        }
      }
      if (widget.event?.categories != null) {
        for (var element in widget.event!.categories!) {
          createEventModel.value = createEventModel.value.copyWith(childSubCategory: [element.category!]);
        }
      }
      if (widget.event?.state != null) {
        createEventModel.value = createEventModel.value.copyWith(state: widget.event?.state!);
      }
      if (widget.event?.country != null) {
        createEventModel.value = createEventModel.value.copyWith(country: widget.event?.country!);
      }
      if (widget.event?.subCategory != null) {
        createEventModel.value = createEventModel.value.copyWith(subCategory: widget.event?.subCategory);
      }
      if (widget.event?.parentCategory != null) {
        createEventModel.value = createEventModel.value.copyWith(category: widget.event?.parentCategory);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        title: widget.event != null ? 'Edit Event/Promotion' : 'Create Event/Promotion',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: ValueListenableBuilder(
              valueListenable: createEventModel,
              builder: (context, postAdValues, _) {
                return Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Images of Event or Promotion* - 2 max',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: AppColors.primary,
                                ),
                          ),
                          const Gap(20),
                          BusinessImageView(
                            maxImage: 2,
                            images: postAdValues.images ?? [],
                            onImageSelected: (images) {
                              createEventModel.value = createEventModel.value.copyWith(
                                images: images,
                              );
                            },
                            onDeletedImages: (deletedImage) {
                              createEventModel.value = createEventModel.value.copyWith(
                                deleteImages: [...createEventModel.value.deleteImages ?? [], deletedImage],
                              );
                            },
                          ),
                          const Gap(20),
                          AppTextFormField(
                            title: 'Event or Promotion\'s Name/Subject',
                            isCapitalize: true,
                            controller: adTitleController,
                            textInputAction: TextInputAction.next,
                            isRequired: true,
                            inputFormatters: [LengthLimitingTextInputFormatter(70)],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter event name';
                              }
                              return null;
                            },
                          ),
                          const Gap(14),
                          AppTextFormField(
                            title: 'Short description',
                            controller: adDescriptionController,
                            maxLines: 4,
                            isRequired: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetLocationView(
                      postAdModel: postAdValues,
                      isOptional: true,
                      onModelUpdated: (targetLocation) {
                        createEventModel.value = createEventModel.value.copyWith(
                          country: targetLocation.country,
                          state: targetLocation.state,
                          cities: targetLocation.cities,
                        );
                      },
                    ),
                    const Divider(
                      height: 0,
                    ),
                    TargetBuyersView(
                      title: 'Target Audience',
                      postOrEventModel: postAdValues,
                      isOptional: true,
                      onModelUpdated: (targetBuyers) {
                        createEventModel.value = createEventModel.value.copyWith(
                          category: targetBuyers.category,
                          subCategory: targetBuyers.subCategory,
                          childSubCategory: targetBuyers.childSubCategory,
                        );
                      },
                    ),
                    Container(
                      padding: const EdgeInsets.all(10),
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      decoration: BoxDecoration(
                        color: AppColors.offGreen,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        AppConstants.eventPromotionAutoExpire,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontSize: 13,
                            ),
                      ),
                    ),
                    const Gap(30),
                    ValueListenableBuilder<bool>(
                        valueListenable: isButtonLoading,
                        builder: (context, loading, _) {
                          return CommonButton(
                            text: widget.event != null ? 'Update Event' : 'Create Event',
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            isLoading: loading,
                            onTap: loading
                                ? null
                                : () {
                                    if (createEventModel.value.images?.isNotEmpty ?? false) {
                                      if (_formKey.currentState!.validate()) {
                                        createEventModel.value = createEventModel.value.copyWith(
                                          eventName: adTitleController.text.trim(),
                                          description: adDescriptionController.text.trim(),
                                        );
                                        widget.event != null ? updateEvent() : createEvent();
                                      }
                                    } else {
                                      Utility.toast(message: 'Please select at least one image');
                                    }
                                  },
                          );
                        }),
                    const Gap(30),
                  ],
                );
              }),
        ),
      ),
    );
  }

  Future<void> createEvent() async {
    isButtonLoading.value = true;
    final response = await getIt<IEventRepository>().createEvent(
      event: createEventModel.value,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1 && r.data != null) {
          context.read<EventBloc>().add(AddEvent(event: r.data!));
          Navigator.pop(context);
        }
        Utility.toast(message: r.message);
      },
    );
  }

  Future<void> updateEvent() async {
    isButtonLoading.value = true;
    final response = await getIt<IEventRepository>().updateEvent(
      eventId: widget.event!.id!,
      event: createEventModel.value,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1 && r.data != null) {
          context.read<EventBloc>().add(EditEvent(event: r.data!));
          Navigator.pop(context, r.data!);
        }
        Utility.toast(message: r.message);
      },
    );
  }
}
