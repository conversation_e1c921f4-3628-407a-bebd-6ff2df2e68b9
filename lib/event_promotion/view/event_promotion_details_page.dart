import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/event_promotion/bloc/comment_bloc.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/event_promotion/model/comment_model.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart';
import 'package:leiuniverse/event_promotion/view/create_event_promotion_page.dart';
import 'package:leiuniverse/event_promotion/view/full_screen_image_view.dart';
import 'package:leiuniverse/event_promotion/view/write_comment_view.dart';
import 'package:leiuniverse/event_promotion/widget/comment_widget.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:leiuniverse/widget/dailogs.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';
import 'package:leiuniverse/widget/user_info_widget.dart';

class EventPromotionDetailsPage extends StatefulWidget {
  const EventPromotionDetailsPage({super.key, this.event});
  final EventModel? event;

  @override
  State<EventPromotionDetailsPage> createState() => _EventPromotionDetailsPageState();
}

class _EventPromotionDetailsPageState extends State<EventPromotionDetailsPage> {
  late EventModel myEvent = widget.event ?? const EventModel();
  final isStackloading = ValueNotifier(false);

  void updateCommentCount(int? totalComment) {
    myEvent = myEvent.copyWith(totalComments: totalComment);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<CommentBloc>()..add(LoadComments(eventId: myEvent.id ?? 0)),
      child: Builder(builder: (context) {
        return BlocListener<CommentBloc, CommentState>(
          listenWhen: (previous, current) =>
              previous.whenOrNull(loaded: (s) => s.hashCode) != current.whenOrNull(loaded: (s) => s.hashCode),
          listener: (context, state) {
            if (state is CommentsLoaded) {
              context
                  .read<EventBloc>()
                  .add(UpdateCommentCount(eventId: myEvent.id ?? 0, commentCount: state.totalComments));
              updateCommentCount(state.totalComments);
            }
          },
          child: Scaffold(
            backgroundColor: AppColors.white,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (myEvent.images != null && myEvent.images!.isNotEmpty)
                  Stack(
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: List.generate(
                            myEvent.images?.length ?? 0,
                            (index) {
                              return InkWell(
                                onTap: myEvent.images?.isNotEmpty ?? false
                                    ? () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) => FullScreenImageViewer(
                                              images: myEvent.images!,
                                              initialIndex: index,
                                            ),
                                          ),
                                        );
                                      }
                                    : null,
                                overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                child: Padding(
                                  padding: EdgeInsets.only(right: index == 0 ? 14 : 0),
                                  child: Utility.imageLoader(
                                    url: myEvent.images?[index].imageUrl ?? '',
                                    placeholder: AppAssets.placeholderImage,
                                    height: 218,
                                    width: myEvent.images?.length == 1 ? MediaQuery.sizeOf(context).width : 323,
                                    fit: BoxFit.cover,
                                    borderRadius: BorderRadius.circular(0),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      Positioned(
                        top: 40,
                        left: 20,
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          child: const AppSvgImage(
                            AppAssets.roundBackIcon,
                          ),
                        ),
                      ),
                      BlocBuilder<AuthenticationBloc, AuthenticationState>(
                        builder: (context, state) {
                          if (state.isAutheticated) {
                            return Positioned(
                              top: 40,
                              right: 20,
                              child: Row(
                                children: [
                                  const AppSvgImage(
                                    AppAssets.roundShareIcon,
                                  ),
                                  if (state.user.id == myEvent.userId) ...[
                                    const Gap(20),
                                    PopupMenuButton(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      color: AppColors.white,
                                      elevation: 6,
                                      position: PopupMenuPosition.under,
                                      onSelected: (value) async {
                                        if (value == 0) {
                                          final newEvent = await Navigator.push<EventModel>(
                                            context,
                                            MaterialPageRoute(
                                              builder: (_) => BlocProvider.value(
                                                value: context.read<EventBloc>(),
                                                child: CreateEventPromotionPage(event: myEvent),
                                              ),
                                            ),
                                          );
                                          if (newEvent != null) {
                                            myEvent = newEvent;
                                            myEvent = myEvent.copyWith(
                                              images: newEvent.images,
                                            );
                                            setState(() {});
                                          }
                                        }
                                        if (value == 1) {
                                          if (context.mounted) {
                                            AppDailogs.deleteEventDailog(context,
                                                eventId: myEvent.id ?? 0, isPopTwice: true);
                                          }
                                        }
                                      },
                                      itemBuilder: (context) {
                                        return [
                                          const PopupMenuItem(
                                            value: 0,
                                            child: Text('Edit'),
                                          ),
                                          const PopupMenuItem(
                                            value: 1,
                                            child: Text('Delete'),
                                          ),
                                        ];
                                      },
                                      child: const AppSvgImage(
                                        AppAssets.roundMenuIcon,
                                      ),
                                    )
                                  ],
                                ],
                              ),
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                    ],
                  )
                else
                  SafeArea(
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(bottom: BorderSide(color: AppColors.border, width: 1)),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 0, 20, 10),
                        child: Row(
                          children: [
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              overlayColor: WidgetStateProperty.all(AppColors.transparent),
                              child: const AppSvgImage(
                                AppAssets.roundBackIcon,
                              ),
                            ),
                            const Spacer(),
                            BlocBuilder<AuthenticationBloc, AuthenticationState>(
                              builder: (context, state) {
                                if (state.isAutheticated) {
                                  return Row(
                                    children: [
                                      const AppSvgImage(
                                        AppAssets.roundShareIcon,
                                      ),
                                      if (state.user.id == myEvent.userId) ...[
                                        const Gap(20),
                                        PopupMenuButton(
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(10),
                                          ),
                                          color: AppColors.white,
                                          elevation: 6,
                                          position: PopupMenuPosition.under,
                                          onSelected: (value) async {
                                            if (value == 0) {
                                              final newEvent = await Navigator.push<EventModel>(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (_) => BlocProvider.value(
                                                    value: context.read<EventBloc>(),
                                                    child: CreateEventPromotionPage(event: myEvent),
                                                  ),
                                                ),
                                              );
                                              if (newEvent != null) {
                                                myEvent = newEvent;
                                                myEvent = myEvent.copyWith(
                                                  images: newEvent.images,
                                                );
                                                setState(() {});
                                              }
                                            }
                                            if (value == 1) {
                                              if (context.mounted) {
                                                AppDailogs.deleteEventDailog(context,
                                                    eventId: myEvent.id ?? 0, isPopTwice: true);
                                              }
                                            }
                                          },
                                          itemBuilder: (context) {
                                            return [
                                              const PopupMenuItem(
                                                value: 0,
                                                child: Text('Edit'),
                                              ),
                                              const PopupMenuItem(
                                                value: 1,
                                                child: Text('Delete'),
                                              ),
                                            ];
                                          },
                                          child: const AppSvgImage(
                                            AppAssets.roundMenuIcon,
                                          ),
                                        ),
                                      ],
                                    ],
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Padding(
                          padding: const EdgeInsetsGeometry.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                myEvent.name ?? '',
                                style: Theme.of(context).textTheme.headlineMedium,
                              ),
                              const Gap(8),
                              if (myEvent.eventLocation.isNotEmpty) ...[
                                Row(
                                  children: [
                                    const AppSvgImage(
                                      AppAssets.locationIcon,
                                    ),
                                    const Gap(6),
                                    Text(
                                      myEvent.eventLocation,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                                const Gap(14),
                              ],
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  ValueListenableBuilder<bool>(
                                      valueListenable: isStackloading,
                                      builder: (context, loading, _) {
                                        return InkWell(
                                          onTap: loading
                                              ? null
                                              : () {
                                                  likeUnlike(context);
                                                },
                                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                                          child: IconTextWidget(
                                            icon: myEvent.isLiked == 1
                                                ? AppAssets.likeHandIcon
                                                : AppAssets.dislikeHandIcon,
                                            text: '${myEvent.likesCount ?? 0} Likes',
                                          ),
                                        );
                                      }),
                                  const Gap(14),
                                  BlocBuilder<CommentBloc, CommentState>(
                                    builder: (context, state) {
                                      if (state is CommentsLoaded) {
                                        return IconTextWidget(
                                          icon: AppAssets.messageIcon,
                                          text: '${state.totalComments ?? 0} Comments',
                                        );
                                      }
                                      return IconTextWidget(
                                        icon: AppAssets.messageIcon,
                                        text: '${myEvent.totalComments ?? 0} Comments',
                                      );
                                    },
                                  ),
                                ],
                              ),
                              const Gap(20),
                              Container(
                                padding: const EdgeInsets.all(14),
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: AppColors.background,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Description'.allInCaps,
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: AppColors.subText,
                                          ),
                                    ),
                                    const Gap(10),
                                    Text(
                                      myEvent.shortDescription ?? '',
                                      // AppConstants.eventPromotionAutoExpire,
                                      style: Theme.of(context).textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ),
                              const Gap(20),
                              UserInfoWidget(
                                user: myEvent.user,
                              ),
                            ],
                          ),
                        ),
                        const Divider(
                          height: 0,
                          thickness: 4,
                        ),
                        BlocBuilder<CommentBloc, CommentState>(
                          builder: (context, state) {
                            return state.when(
                              error: (message) => const Center(child: Text('Error')),
                              initial: () => const SizedBox(),
                              loading: () => const Center(
                                child: CustomProgressIndecator(
                                  color: AppColors.primary,
                                  size: 40,
                                ),
                              ),
                              loaded: (newState) {
                                return Padding(
                                  padding: const EdgeInsets.all(20),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Comments',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              color: AppColors.primary,
                                            ),
                                      ),
                                      const Gap(14),
                                      Flexible(
                                        child: !newState.isLoadingMore && newState.comments.isEmpty
                                            ? Utility.noDataWidget(
                                                context: context,
                                                text: 'No Comments found',
                                              )
                                            : ListView.separated(
                                                shrinkWrap: true,
                                                physics: const NeverScrollableScrollPhysics(),
                                                itemCount: newState.comments.length,
                                                padding: EdgeInsets.zero,
                                                separatorBuilder: (context, index) {
                                                  return const Gap(20);
                                                },
                                                itemBuilder: (context, index) {
                                                  return Column(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      commentView(newState.comments[index], context),
                                                      if (!newState.isLoadingMore &&
                                                          index == newState.comments.length - 1 &&
                                                          !newState.hasReachedMax) ...[
                                                        const Gap(20),
                                                        loadMore(),
                                                      ],
                                                      if (index == newState.comments.length - 1 &&
                                                          newState.isLoadingMore)
                                                        const CustomProgressIndecator(
                                                          color: AppColors.primary,
                                                          size: 40,
                                                        ),
                                                    ],
                                                  );
                                                },
                                              ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            bottomNavigationBar: BottomButtonWidget(
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  backgroundColor: AppColors.white,
                  isScrollControlled: true,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  builder: (_) {
                    return BlocProvider.value(
                      value: context.read<CommentBloc>(),
                      child: WriteCommentView(
                        eventId: myEvent.id ?? 0,
                      ),
                    );
                  },
                );
              },
              text: 'Add Comment',
            ),
          ),
        );
      }),
    );
  }

  Widget loadMore() {
    return InkWell(
      onTap: () {
        context.read<CommentBloc>().add(CommentsLoadMore(eventId: myEvent.id ?? 0));
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          'Load more',
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ),
    );
  }

  Widget commentView(CommentModel comment, BuildContext context) {
    final userId = context.read<AuthenticationBloc>().state.user.id;
    return CommentWidget(
      comment: comment,
      isMyComment: comment.user?.id == userId,
      onReplyTap: () {
        showModalBottomSheet(
          context: context,
          backgroundColor: AppColors.white,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          builder: (_) {
            return BlocProvider.value(
              value: BlocProvider.of<CommentBloc>(context),
              child: WriteCommentView(
                eventId: myEvent.id ?? 0,
                replyTo: comment.id,
                totalComments: myEvent.totalComments ?? 0,
              ),
            );
          },
        );
      },
    );
  }

  Future<void> likeUnlike(BuildContext context) async {
    isStackloading.value = true;
    final response = await getIt<IEventRepository>().likeEvent(
      eventId: myEvent.id ?? 0,
    );

    await response.fold(
      (l) {
        isStackloading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isStackloading.value = false;
        if (r.status == 1) {
          context.read<EventBloc>().add(LikeEvent(eventId: myEvent.id ?? 0));
          myEvent = myEvent.copyWith(
              isLiked: myEvent.isLiked == 0 ? 1 : 0,
              likesCount: myEvent.isLiked == 0 ? (myEvent.likesCount ?? 0) + 1 : (myEvent.likesCount ?? 0) - 1);
          setState(() {});
        }
      },
    );
  }
}
