import 'package:flutter/material.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/setup_business/model/image_model.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:photo_view/photo_view.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class FullScreenImageViewer extends StatefulWidget {
  final List<ImageModel> images;
  final int initialIndex;
  final bool isProfile;

  const FullScreenImageViewer({
    super.key,
    this.images = const [],
    this.initialIndex = 0,
    this.isProfile = false,
  });

  @override
  State<FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<FullScreenImageViewer> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Stack(
        alignment: Alignment.center,
        children: [
          PageView.builder(
            controller: _pageController,
            itemCount: widget.images.length,
            itemBuilder: (context, index) {
              String url =
                  widget.isProfile ? widget.images[index].imagePath ?? '' : widget.images[index].imageUrl ?? '';
              if (!url.startsWith('http')) url = AppStrings.storageUrl + url;
              return PhotoView(
                backgroundDecoration: const BoxDecoration(
                  color: AppColors.background,
                ),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained,
                imageProvider: NetworkImage(url),
              );
            },
          ),
          if (widget.images.length > 1) ...[
            Positioned(
              bottom: 40,
              child: SmoothPageIndicator(
                controller: _pageController,
                count: widget.images.length,
                effect: WormEffect(
                  dotHeight: 8,
                  dotWidth: 8,
                  activeDotColor: AppColors.primary,
                  dotColor: AppColors.subText.withOpacity2(0.7),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
