import 'package:equatable/equatable.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';

class EventResponse extends Equatable {
  const EventResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final EventModel? data;

  EventResponse copyWith({
    int? status,
    String? message,
    EventModel? data,
  }) {
    return EventResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory EventResponse.fromJson(Map<String, dynamic> json) {
    return EventResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : EventModel.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
