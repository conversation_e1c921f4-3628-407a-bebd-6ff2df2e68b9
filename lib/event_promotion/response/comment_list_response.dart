import 'package:equatable/equatable.dart';
import 'package:leiuniverse/event_promotion/model/comment_model.dart';

class CommentListResponse extends Equatable {
  const CommentListResponse({
    this.status,
    this.message,
    this.data,
    this.currentPage,
    this.perPage,
    this.total,
    this.lastPage,
    this.totalComments,
  });

  final int? status;
  final String? message;
  final List<CommentModel>? data;
  final int? currentPage;
  final int? perPage;
  final int? total;
  final int? lastPage;
  final int? totalComments;

  CommentListResponse copyWith({
    int? status,
    String? message,
    List<CommentModel>? data,
    int? currentPage,
    int? perPage,
    int? total,
    int? lastPage,
    int? totalComments,
  }) {
    return CommentListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      perPage: perPage ?? this.perPage,
      total: total ?? this.total,
      lastPage: lastPage ?? this.lastPage,
      totalComments: totalComments ?? this.totalComments,
    );
  }

  factory CommentListResponse.fromJson(Map<String, dynamic> json) {
    return CommentListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<CommentModel>.from(json["data"]!.map((x) => CommentModel.fromJson(x))),
      currentPage: json["current_page"],
      perPage: json["per_page"],
      total: json["total"],
      lastPage: json["last_page"],
      totalComments: json["total_comments"],
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data!.map((x) => x.toJson()).toList(),
        "current_page": currentPage,
        "per_page": perPage,
        "total": total,
        "last_page": lastPage,
        "total_comments": totalComments,
      };

  @override
  String toString() {
    return "$status, $message, $data, $currentPage, $perPage, $total, $lastPage, $totalComments";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
        currentPage,
        perPage,
        total,
        lastPage,
        totalComments,
      ];
}
