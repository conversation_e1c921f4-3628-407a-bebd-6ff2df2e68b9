import 'package:equatable/equatable.dart';
import 'package:leiuniverse/event_promotion/model/event_model.dart';

class EventListResponse extends Equatable {
  const EventListResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final List<EventModel>? data;

  EventListResponse copyWith({
    int? status,
    String? message,
    List<EventModel>? data,
  }) {
    return EventListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory EventListResponse.fromJson(Map<String, dynamic> json) {
    return EventListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<EventModel>.from(json["data"]!.map((x) => EventModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data!.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
