import 'package:equatable/equatable.dart';
import 'package:leiuniverse/event_promotion/model/comment_model.dart';

class CommentResponse extends Equatable {
  const CommentResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final CommentModel? data;

  CommentResponse copyWith({
    int? status,
    String? message,
    CommentModel? data,
  }) {
    return CommentResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory CommentResponse.fromJson(Map<String, dynamic> json) {
    return CommentResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? null : CommentModel.fromJson(json["data"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
