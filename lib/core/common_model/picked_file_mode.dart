import 'dart:io';
import 'dart:typed_data';

import 'package:leiuniverse/setup_business/model/image_model.dart';

class PickedFileModel {
  PickedFileModel({this.fileExtension, this.file, this.bytes, this.networkFile, this.imageId});

  late final File? file;
  final Uint8List? bytes;
  final ImageModel? networkFile;
  final String? fileExtension;
  final int? imageId;

  PickedFileModel copyWith({
    File? file,
    Uint8List? bytes,
    ImageModel? networkFile,
    String? fileExtension,
    int? imageId,
  }) {
    return PickedFileModel(
      file: file ?? this.file,
      bytes: bytes ?? this.bytes,
      networkFile: networkFile ?? this.networkFile,
      fileExtension: fileExtension ?? this.fileExtension,
      imageId: imageId ?? this.imageId,
    );
  }
}
