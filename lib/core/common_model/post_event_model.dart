// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:leiuniverse/core/common_model/picked_file_mode.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class PostOrEventModel {
  final String? title;
  final String? eventName;
  final String? description;
  final String? price;
  final String? contactNumber;
  final CategoryModel? category;
  final CategoryModel? subCategory;
  final List<CategoryModel>? childSubCategory;
  final CountryModel? country;
  final CountryModel? state;
  final List<CountryModel>? cities;
  final int? duration;
  final List<PickedFileModel>? images;
  final List<int>? deleteImages;

  PostOrEventModel({
    this.title,
    this.eventName,
    this.description,
    this.price,
    this.contactNumber,
    this.category,
    this.subCategory,
    this.childSubCategory,
    this.country,
    this.state,
    this.cities,
    this.duration,
    this.images,
    this.deleteImages = const [],
  });

  PostOrEventModel copyWith({
    String? title,
    String? eventName,
    String? description,
    String? price,
    String? contactNumber,
    CategoryModel? category,
    CategoryModel? subCategory,
    List<CategoryModel>? childSubCategory,
    CountryModel? country,
    CountryModel? state,
    List<CountryModel>? cities,
    int? duration,
    List<PickedFileModel>? images,
    List<int>? deleteImages,
  }) {
    return PostOrEventModel(
      title: title ?? this.title,
      eventName: eventName ?? eventName,
      description: description ?? this.description,
      price: price ?? this.price,
      contactNumber: contactNumber ?? this.contactNumber,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      childSubCategory: childSubCategory ?? this.childSubCategory,
      country: country ?? this.country,
      state: state ?? this.state,
      cities: cities ?? this.cities,
      duration: duration ?? this.duration,
      images: images ?? this.images,
      deleteImages: deleteImages ?? this.deleteImages,
    );
  }
}
