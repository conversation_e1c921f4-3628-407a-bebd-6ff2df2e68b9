part of 'i_local_storage_repository.dart';

@Injectable(as: ILocalStorageRepository)
class LocalStorageRepository extends ILocalStorageRepository {
  LocalStorageRepository(super.preferences);

  @override
  Future<bool> setToken(String? value) async {
    if (value == null) return false;
    try {
      await preferences.setString(AppStrings.tokenKey, value);
      await FirebaseMessagingService.joinDefaultGroup();
      return true;
    } on Exception catch (e, s) {
      log('Error Set Token: $e \n $s');
      return false;
    }
  }

  @override
  String? get token => preferences.getString(AppStrings.tokenKey);

  @override
  Future<bool> clearAuth() async {
    try {
      final isRemoveToken = await preferences.remove(AppStrings.tokenKey);
      final isRemoveUser = await preferences.remove(AppStrings.userPrefKey);
      final isRemoveRecentSearch = await preferences.remove(AppStrings.recentSearchKey);

      await leaveDefaultChatGroup();
      return isRemoveUser && isRemoveToken && isRemoveRecentSearch;
    } on Exception catch (e, s) {
      log('Error Clear Auth: $e \n $s');
      return false;
    }
  }

  @override
  Future<UserModel> setUser(UserModel? user) async {
    try {
      final isSuccess = await preferences.setString(AppStrings.userPrefKey, jsonEncode(user));
      if (!isSuccess) {
        throw Exception('Failed to save user data');
      }
      return user!;
    } catch (e) {
      throw Exception('Error saving user: ${e.toString()}');
    }
  }

  @override
  UserModel? get getUser {
    try {
      final userKey = preferences.getString(AppStrings.userPrefKey);
      if (userKey != null) {
        final user = UserModel.fromJson(jsonDecode(userKey) as Map<String, dynamic>);
        return user;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> joinDefaultChatGroup() async {
    final isJoined = preferences.getBool(AppConstants.defaultChatGroup) ?? false;

    if (isJoined) return;

    try {
      await Future.wait([
        FirebaseMessagingService.joinDefaultGroup(),
        preferences.setBool(AppConstants.defaultChatGroup, true),
      ]);
      debugLog('Joined Default Chat Group Notification');
    } catch (e) {
      debugError('Error Joining Default Chat Group: $e');
    }
  }

  @override
  Future<void> leaveDefaultChatGroup() async {
    final isJoined = preferences.getBool(AppConstants.defaultChatGroup) ?? false;

    if (!isJoined) return;

    try {
      await Future.wait([
        FirebaseMessagingService.leaveDefaultGroup(),
        preferences.remove(AppConstants.defaultChatGroup),
      ]);
      debugLog('Left Default Chat Group Notification');
    } catch (e) {
      debugError('Error Leaving Default Chat Group: $e');
    }
  }

  @override
  Future<void> saveRecentSearch(SearchModel search) async {
    try {
      final currentListString = preferences.getString(AppStrings.recentSearchKey);
      List<SearchModel> userList = [];

      if (currentListString != null) {
        final decodedList = jsonDecode(currentListString) as List;
        userList = decodedList.map((e) => SearchModel.fromJson(e as Map<String, dynamic>)).toList();
      }

      // Remove existing occurrence of the user (e.g., by id or username)
      userList.removeWhere((u) => u.id == search.id);

      // Add new user to the front
      userList.insert(0, search);

      // Trim to last 5
      if (userList.length > 5) {
        userList = userList.sublist(0, 5);
      }

      final encodedList = jsonEncode(userList.map((e) => e.toJson()).toList());
      await preferences.setString(AppStrings.recentSearchKey, encodedList);
    } catch (e, s) {
      log('Error saving recent search user: $e \n$s');
    }
  }

  @override
  List<SearchModel> getRecentSearch() {
    try {
      final recentSearch = preferences.getString(AppStrings.recentSearchKey);
      if (recentSearch == null) return [];

      final decodedList = jsonDecode(recentSearch) as List;
      return decodedList.map((e) => SearchModel.fromJson(e as Map<String, dynamic>)).toList();
    } catch (e, s) {
      log('Error getting recent search users: $e \n$s');
      return [];
    }
  }
}
