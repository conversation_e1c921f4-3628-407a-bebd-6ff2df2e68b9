import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

class TimeStampConverter implements JsonConverter<Timestamp?, dynamic> {
  const TimeStampConverter();

  @override
  Timestamp? fromJson(dynamic json) {
    if (json is Timestamp) {
      return json;
    }
    return null;
  }

  @override
  Timestamp? toJson(Timestamp? timestamp) => timestamp;
}
