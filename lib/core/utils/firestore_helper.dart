import 'package:cloud_firestore/cloud_firestore.dart';

extension FirestoreX on FirebaseFirestore {
  CollectionReference<Map<String, dynamic>> chats() {
    return collection('chats');
  }

  CollectionReference<Map<String, dynamic>> userGroupMeta() {
    return collection('user_group_meta');
  }

  CollectionReference<Map<String, dynamic>> messages() => collection('messages');
}

extension DocumentRefrenceXM<T> on DocumentReference<T> {
  CollectionReference<Map<String, dynamic>> messages() => collection('messages');
}
