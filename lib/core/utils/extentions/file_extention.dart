import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:leiuniverse/core/utils/logger_config.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

extension FileExtention on File {
  Future<File> compressFile() async {
    final kFile = this;
    try {
      final fileName = await _getTempFileName();
      if (fileName == null) return kFile;

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        kFile.path,
        fileName,
        quality: _qualityForCompress,
      );
      if (compressedFile != null) {
        debugLog('Image Comressed');
      }
      return compressedFile.file ?? kFile;
    } catch (e) {
      debugError('IMAGE COMPRESS ERRROR $e');
      return kFile;
    }
  }

  int get _qualityForCompress {
    final sizeInBytes = lengthSync();
    final sizeInMb = sizeInBytes / (1024 * 1024);

    final isSizeMoreThan5Mb = sizeInMb >= 5;
    final isSizeMoreThan1Mb = sizeInMb >= 1;

    final quality = isSizeMoreThan5Mb
        ? 60
        : isSizeMoreThan1Mb
            ? 75
            : 100;

    return quality;
  }

  Future<String?> _getTempFileName() async {
    final kFile = this;
    try {
      final tempDir = await getTemporaryDirectory();
      final currentDate = DateTime.now();
      final fileName = path.extension(kFile.path);
      return '${tempDir.path}/${currentDate.microsecondsSinceEpoch}_$fileName';
    } catch (e) {
      return null;
    }
  }
}

extension XFileExtention on XFile? {
  File? get file => this == null ? null : File(this!.path);
}
