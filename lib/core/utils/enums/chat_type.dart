enum ChatType {
  private('PRIVATE'),
  advertisement('ADVERTISEMENT'),
  group('GROUP');

  final String name;

  const ChatType(this.name);

  @override
  String toString() => name;

  static ChatType? fromString(String? value) {
    if (value == null) return null;
    if (value == 'USERS') return ChatType.private;
    if (value == 'GROUP') return ChatType.group;
    if (value == 'MARKETPLACE') return ChatType.advertisement;

    return null;
  }
}
