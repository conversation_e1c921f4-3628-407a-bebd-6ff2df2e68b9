// ignore_for_file: avoid_dynamic_calls

import 'dart:async';
import 'dart:convert';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:leiuniverse/app/app.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/utils/enums/chat_type.dart';
import 'package:leiuniverse/core/utils/logger_config.dart';
import 'package:leiuniverse/firebase_options.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/notification/cubit/refresh_cubit.dart';
import 'package:leiuniverse/user/model/notification_payload.dart';

Future<void> _onBackgroundMessageHandler(RemoteMessage message) async {
  debugLog('[FirebaseMessagingService] Received background message: ${message.toMap()}');
  debugLog('[FirebaseMessagingService] Background message data: ${message.data}');
}

class FirebaseMessagingService {
  factory FirebaseMessagingService() {
    return _singleton;
  }

  FirebaseMessagingService._internal();

  static final FirebaseMessagingService _singleton = FirebaseMessagingService._internal();

  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static String? token;

  late AndroidNotificationChannel channel;

  late StreamSubscription<RemoteMessage> onFrontEndStream;
  late StreamSubscription<RemoteMessage> onOpenAppStream;
  late StreamSubscription<String> tokenStream;
  static String groupKey = 'com.leiuniverse';

  int notificationCount = 1;

  static Future<void> initializeApp() async {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

    await Future.delayed(const Duration(milliseconds: 1200), () {});
    token = await _firebaseMessaging.getToken() ?? '';
    debugLog('[FirebaseMessagingService] FCM Token: $token');
    FirebaseMessaging.onBackgroundMessage(_onBackgroundMessageHandler);
  }

  Future<void> initialize({
    void Function(RemoteMessage remoteMessage)? onMessage,
  }) async {
    await _firebaseMessaging.requestPermission(
      announcement: true,
      carPlay: true,
      criticalAlert: true,
    );

    await getIt<ILocalStorageRepository>().joinDefaultChatGroup();

    if (onMessage != null) {
      await _onOpenedAppFromTerminateMessage(onMessage);
    }

    if (!kIsWeb) {
      channel = const AndroidNotificationChannel(
        'high_importance_channel', // id
        'High Importance Notifications', // title
        description: 'This channel is used for important notifications.', // description
        importance: Importance.high,
      );

      const initializationSettingsAndroid = AndroidInitializationSettings('@drawable/ic_notification');

      const initializationSettingsIOS = DarwinInitializationSettings();

      const initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
      await flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (details) => _onOpenedLocalNotification(details.payload),
      );

      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);

      await _firebaseMessaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      await _onFrontEndMessage(flutterLocalNotificationsPlugin);
    }

    _onOpenedAppMessage();
  }

  Future<void> _onOpenedAppFromTerminateMessage(void Function(RemoteMessage initialMessage) onMessage) async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      onMessage(initialMessage);
      debugLog('[FirebaseMessagingService] App opened from terminated state. Message data: ${initialMessage.data}');
      debugLog('[FirebaseMessagingService] App opened from terminated state. Message: ${initialMessage.toMap()}');
    } else {
      debugLog('[FirebaseMessagingService] No initial message found when opening from terminated state.');
    }
  }

  Future<void> _onOpenedLocalNotification(String? payload) async {
    if (payload != null) {
      debugLog('[FirebaseMessagingService] Opened local notification with payload: $payload');
      final data = jsonDecode(payload);
      final response = NotificationPayload.fromJson(data as Map<String, dynamic>);
      openPagesOnNotificationOnClick(response);
    }
  }

  static Future<void> deleteToken() async {
    await FirebaseMessaging.instance.deleteToken();
    debugLog('[FirebaseMessagingService] FCM Token deleted successfully.');
  }

  void _onOpenedAppMessage() {
    FirebaseMessaging.onMessageOpenedApp.listen((message) async {
      debugLog('[FirebaseMessagingService] App opened from background. Message data: ${message.data}');
      debugLog('[FirebaseMessagingService] App opened from background. Message: ${message.toMap()}');

      final data = message.data;
      final response = NotificationPayload.fromJson(data);
      debugLog('AAAresponse: $response');
      openPagesOnNotificationOnClick(response);
    });
  }

  Future<void> _onFrontEndMessage(FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin) async {
    onFrontEndStream = FirebaseMessaging.onMessage.listen((message) async {
      debugLog('[FirebaseMessagingService] Received foreground message.');
      debugLog(
          '[FirebaseMessagingService] Foreground message title: ${message.notification?.title}, body: ${message.notification?.body} data: ${message.data}');
      await Future.delayed(const Duration(milliseconds: 500));
      await navigatorKey.currentContext?.read<RefreshCubit>().loadCount();
      final data = message.data;
      final response = NotificationPayload.fromJson(data);
      final user = navigatorKey.currentContext?.read<AuthenticationBloc>().state.user;
      if ((response.senderId ?? response.metaDataPayload?.sender?.id) == user?.id.toString()) {
        return;
      }
      if (response.chatType == ChatType.group.name && user?.isJoinedDefaultChatGroup != 1) return;

      final notification = message.notification;
      final android = message.notification?.android;
      if (notification != null && android != null && !kIsWeb) {
        await flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              channelDescription: channel.description,
              icon: '@drawable/ic_notification',
              groupKey: groupKey,
            ),
            iOS: DarwinNotificationDetails(
              threadIdentifier: groupKey,
            ),
          ),
          payload: jsonEncode(message.data),
        );
        notificationCount += 1;
        final groupNotification = getGroupNotifier();
        await flutterLocalNotificationsPlugin.show(0, 'Attention', '$notificationCount messages', groupNotification);
      }
    });
  }

  NotificationDetails getGroupNotifier() {
    final inboxStyleInformation =
        InboxStyleInformation([], contentTitle: '$notificationCount messages', summaryText: 'tynt');
    final androidNotificationDetails = AndroidNotificationDetails(
      channel.id,
      channel.name,
      styleInformation: inboxStyleInformation,
      groupKey: groupKey,
      playSound: false,
      setAsGroupSummary: true,
    );
    final iosNotificationDetail = DarwinNotificationDetails(threadIdentifier: groupKey);
    return NotificationDetails(android: androidNotificationDetails, iOS: iosNotificationDetail);
  }

  static void openPagesOnNotificationOnClick(NotificationPayload response) {
    if (response.metaDataPayload?.clickAction == AppStrings.openChat) {
      navigatorKey.currentState?.push(
        MaterialPageRoute(
          builder: (context) => ChatDetailWrapperPage(
            userId: int.tryParse(response.metaDataPayload?.senderId ?? '') ?? 0,
            groupChatModel: response.metaDataPayload?.group,
            groupId: response.metaDataPayload?.group?.id,
            advertisementId: int.tryParse(response.metaDataPayload?.advertisementId ?? ''),
          ),
        ),
      );
    }
  }

  static Future<void> joinDefaultGroup() async {
    await _firebaseMessaging.subscribeToTopic(AppConstants.defaultChatGroup);
  }

  static Future<void> leaveDefaultGroup() async {
    await _firebaseMessaging.unsubscribeFromTopic(AppConstants.defaultChatGroup);
  }

  void dispose() {
    onFrontEndStream.cancel();
    onOpenAppStream.cancel();
    tokenStream.cancel();
  }
}
