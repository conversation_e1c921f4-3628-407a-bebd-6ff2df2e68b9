import 'package:leiuniverse/core/utils/logger_config.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlManager {
  static Future<void> openDialpad(String? phone) async {
    if (phone == null || phone.trim().isEmpty) return;
    try {
      await launchUrl(Uri.parse('tel:$phone'));
    } catch (e) {
      debugError(e);
      Utility.toast(message: e.toString());
    }
  }

  static Future<void> openEmail(String? email) async {
    if (email == null || email.trim().isEmpty) return;
    try {
      await launchUrl(Uri.parse('mailto:$email'));
    } catch (e) {
      Utility.toast(message: e.toString());
    }
  }

  static Future<void> openUrl(
    String? url, {
    LaunchMode mode = LaunchMode.externalApplication,
  }) async {
    if (url == null || url.trim().isEmpty) return;
    try {
      final newUrl = url.startsWith('http') ? url : 'https://$url';
      await launchUrl(Uri.parse(newUrl), mode: mode);
    } catch (e) {
      Utility.toast(message: e.toString());
    }
  }

  static Future<void> openMap(String? address) async {
    if (address == null || address.trim().isEmpty) return;
    final encodedAddress = Uri.encodeComponent(address);
    final googleMapUrl = 'https://www.google.com/maps/search/?api=1&query=$encodedAddress';
    try {
      await launchUrl(Uri.parse(googleMapUrl));
    } catch (e) {
      Utility.toast(message: e.toString());
    }
  }
}
