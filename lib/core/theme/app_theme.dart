import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/theme/theme_helper.dart';

sealed class AppTheme {
  static ThemeData get light => ThemeData(
        scaffoldBackgroundColor: AppColors.bgColor,
        brightness: Brightness.dark,
        textTheme: ThemeHelper.lightTextTheme,
        inputDecorationTheme: ThemeHelper.inputDecorationLight,
        elevatedButtonTheme: ThemeHelper.elevatedButtonLight,
        useMaterial3: false,
        dividerTheme: ThemeHelper.dividerThemeLight,
        progressIndicatorTheme: ThemeHelper.progressIndicatorThemeLight,
        chipTheme: ThemeHelper.chipThemeLight,
        appBarTheme: ThemeHelper.appBarThemeLight,
      );
}
