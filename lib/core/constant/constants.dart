class AppConstants {
  static const defaultPadding = 16.0;
  // App Titles
  static const appName = 'Lei-Universe';

  static const businessRole = 'BUSINESS';
  static const visitorRole = 'VISITOR';

  static const defaultChatGroup = 'lei-universe-default-group';

  // Buttons
  static const ok = 'OK';
  static const cancel = 'Cancel';
  static const submit = 'Submit';

  // Error Messages
  static const errorSomethingWentWrong = 'Something went wrong. Please try again.';
  static const errorNoInternet = 'No internet connection.';

  // Home Page
  static const homePageTagline = 'FIND AND BE FOUND\n"Talent, Events, Business and Service\nAll in one place"';

  // Labels
  static const email = 'Email';
  static const password = 'Password';

  // Event Promotion
  static const eventPromotionAutoExpire = 'The post of event/ promotion will automatically expire in 30 days.';

  // Other
  static const loading = 'Loading...';
  static const logoutConfirmation = 'Are you sure you want to logout?';
  static const visitorLoginText =
      'Interested members can register for app info and agency searches. You can join group chats, make inquiries, sell items, and search for agency members. Registration as a visitor  member is free.';
  static const bussinessLoginText =
      'Agency  can create up to 1 to 4 different id by registering in different categories of different agencies. For this, interested member has pay ₹150 for one id generation per year, and ₹100 for each add on id.';
  static const bussinessSignupText =
      'Agency  can create up to 1 to 4 different id by registering in different categories of different agencies. For this, interested member has pay ₹100 for one id generation per year, and ₹50 for each add on id.';

  // Settings Keys
  static const terms = 'TERMS';
  static const privacy = 'PRIVACY_POLICY';
  static const aboutUs = 'ABOUT_US';
  static const contactPhone = 'CONTACT_PHONE';
  static const contactEmail = 'CONTACT_EMAIL';
  static const websiteUrl = 'WEBSITE_URL';
  static const companyAddress = 'COMPANY_ADDRESS';
  static const facebook = 'SOCIAL_FACEBOOK';
  static const twitter = 'SOCIAL_TWITTER';
  static const instagram = 'SOCIAL_INSTAGRAM';
  static const youtube = 'SOCIAL_YOUTUBE';

  // Report reasons
  static final List<ReasonModel> reportReasons = [
    ReasonModel(id: 0, reason: 'Abusing Content'),
    ReasonModel(id: 1, reason: 'Suspicious'),
    ReasonModel(id: 2, reason: 'It’s spam'),
    ReasonModel(id: 3, reason: 'Bullying or harassment'),
    ReasonModel(id: 4, reason: 'Sale of illegal or regulated goods'),
    ReasonModel(id: 5, reason: 'Hate speech or symbols'),
    ReasonModel(id: 6, reason: 'Nudity or sexual activity'),
    ReasonModel(id: 7, reason: 'Scam or fraud'),
    ReasonModel(id: 8, reason: 'Something else'),
  ];

  // Chat filters
  static const group = 'GROUP';
  static final List<ChatFilter> chatFilters = [
    ChatFilter(key: 'ALL', value: 'All'),
    ChatFilter(key: 'USERS', value: 'Users'),
    ChatFilter(key: 'GROUP', value: 'Group'),
    ChatFilter(key: 'MARKETPLACE', value: 'Marketplace'),
  ];
}

class ReasonModel {
  final int? id;
  final String? reason;

  ReasonModel({this.id, this.reason});
}

class ChatFilter {
  final String? key;
  final String? value;

  ChatFilter({this.key, this.value});
}
