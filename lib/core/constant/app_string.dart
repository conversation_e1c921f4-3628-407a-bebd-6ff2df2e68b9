class AppStrings {
  static const storageUrl = '';
  static const tokenKey = 'token';
  static const userPrefKey = 'user';
  static const String recentSearchKey = 'recentSearch';
  static const String openChat = 'OPEN_CHAT';

  static const baseUrl = 'https://lei-universe.com/api';

  // AUTH
  static const login = '$baseUrl/v1/users/login';
  static const signUp = '$baseUrl/v1/signup';
  static const verifyOtp = '$baseUrl/v1/users/verify-email';
  static const resendOtp = '$baseUrl/v1/users/resend-verify-otp';
  static const forgotPassword = '$baseUrl/v1/forgot-password';

  // USER
  static const updateProfile = '$baseUrl/v1/users/update';
  static const changePassword = '$baseUrl/v1/users/change-password';
  static const logout = '$baseUrl/v1/users/logout';
  static const deleteAccount = '$baseUrl/v1/users/delete-account';
  static const userDetail = '$baseUrl/v1/users/detail';
  static likeUser(int id) => '$baseUrl/v1/users/$id/like-toggle';
  static const toogleFavorite = '$baseUrl/v1/favourites/toggle-favourites';
  static const favoriteList = '$baseUrl/v1/favourites';
  static const reportUser = '$baseUrl/v1/reports/create';
  static const usersSearch = '$baseUrl/v1/users/search';
  static const notification = '$baseUrl/v1/notifications';
  static const notificationCount = '$baseUrl/v1/notifications/read-status';
  static const chats = '$baseUrl/v1/chats';
  static const sendChat = '$baseUrl/v1/chats/create';
  static deleteChats(String chatId) => '$baseUrl/v1/chats/$chatId/delete';
  static const joinDefaultChatGroup = '$baseUrl/v1/chats/join-default-group';
  static const chatDetail = '$baseUrl/v1/chats/detail';
  static blockUser(String userId) => '$baseUrl/v1/users/block/$userId';

  // BUSINESS
  static const businessList = '$baseUrl/v1/business';
  static const createBusiness = '$baseUrl/v1/business/create';
  static updateBusiness(int id) => '$baseUrl/v1/business/$id/update';
  static deleteBusiness(int id) => '$baseUrl/v1/business/$id/delete';
  static const businessUsers = '$baseUrl/v1/business/users';

  // SETTING
  static const countryList = '$baseUrl/v1/country-list';
  static const stateList = '$baseUrl/v1/state-list';
  static const cityList = '$baseUrl/v1/city-list';
  static const categoryList = '$baseUrl/v1/categories';
  static const faq = '$baseUrl/v1/faqs';
  static const settings = '$baseUrl/v1/settings';
  static const supportTicket = '$baseUrl/v1/support-tickets/create';

  // ADVERTISEMENT
  static const advertisementList = '$baseUrl/v1/advertisements';
  static const createAdvertisement = '$baseUrl/v1/advertisements/create';
  static detailAdvertisement(int id) => '$baseUrl/v1/advertisements/$id/detail';
  static updateAdvertisement(int id) => '$baseUrl/v1/advertisements/update/$id';
  static deleteAdvertisement(int id) => '$baseUrl/v1/advertisements/delete/$id';

  // INQUIRY
  static const createInquiry = '$baseUrl/v1/inquiry/create';

  // EVENT
  static const eventList = '$baseUrl/v1/events';
  static const createEvent = '$baseUrl/v1/events/create';
  static updateEvent(int id) => '$baseUrl/v1/events/update/$id';
  static deleteEvent(int id) => '$baseUrl/v1/events/delete/$id';
  static likeEvent(int id) => '$baseUrl/v1/events/$id/like-toggle';

  // COMMENT
  static const commetsList = '$baseUrl/v1/events/comments';
  static const addComment = '$baseUrl/v1/events/comments/add-comment';
  static deleteComment(int id) => '$baseUrl/v1/events/comments/$id/delete';
}
