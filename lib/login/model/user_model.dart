// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:equatable/equatable.dart';
import 'package:leiuniverse/setup_business/model/business_model.dart';
import 'package:leiuniverse/signup/model/country_model.dart';

class UserModel extends Equatable {
  const UserModel({
    this.isEmailVerified,
    this.canSendMessage,
    this.canCall,
    this.likesCount,
    this.isLiked,
    this.isFavourite,
    this.isBlocked,
    this.isBlockedByMe,
    required this.id,
    this.name,
    this.companyName,
    this.role,
    this.mobileNumber,
    this.email,
    this.profileImage,
    this.pincode,
    this.cityId,
    this.instagramUrl,
    this.facebookUrl,
    this.createdAt,
    this.updatedAt,
    this.city,
    this.businesses,
    this.isJoinedDefaultChatGroup = 1,
  });

  final int? isEmailVerified;
  final int? canSendMessage;
  final int? canCall;
  final int? likesCount;
  final int? isLiked;
  final int? isFavourite;
  final int? isBlocked;
  final int? isBlockedByMe;
  final int id;
  final String? name;
  final String? companyName;
  final String? role;
  final String? mobileNumber;
  final String? email;
  final String? profileImage;
  final String? pincode;
  final int? cityId;
  final String? instagramUrl;
  final String? facebookUrl;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final CountryModel? city;
  final List<BusinessModel>? businesses;
  final int isJoinedDefaultChatGroup;

  UserModel copyWith({
    int? isEmailVerified,
    int? canSendMessage,
    int? canCall,
    int? likesCount,
    int? isLiked,
    int? isFavourite,
    int? isBlocked,
    int? isBlockedByMe,
    int? id,
    String? name,
    String? companyName,
    String? role,
    String? mobileNumber,
    String? email,
    String? profileImage,
    String? pincode,
    int? cityId,
    String? instagramUrl,
    String? facebookUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    CountryModel? city,
    List<BusinessModel>? businesses,
    int? isJoinedDefaultChatGroup,
  }) {
    return UserModel(
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      canSendMessage: canSendMessage ?? this.canSendMessage,
      canCall: canCall ?? this.canCall,
      likesCount: likesCount ?? this.likesCount,
      isLiked: isLiked ?? this.isLiked,
      isFavourite: isFavourite ?? this.isFavourite,
      isBlocked: isBlocked ?? this.isBlocked,
      isBlockedByMe: isBlockedByMe ?? this.isBlockedByMe,
      id: id ?? this.id,
      name: name ?? this.name,
      companyName: companyName ?? this.companyName,
      role: role ?? this.role,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      email: email ?? this.email,
      profileImage: profileImage ?? this.profileImage,
      pincode: pincode ?? this.pincode,
      cityId: cityId ?? this.cityId,
      instagramUrl: instagramUrl ?? this.instagramUrl,
      facebookUrl: facebookUrl ?? this.facebookUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      city: city ?? this.city,
      businesses: businesses ?? this.businesses,
      isJoinedDefaultChatGroup: isJoinedDefaultChatGroup ?? this.isJoinedDefaultChatGroup,
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      isEmailVerified: json["is_email_verified"],
      canSendMessage: json["can_send_message"],
      canCall: json["can_call"],
      likesCount: json["likes_count"],
      isLiked: json["is_liked"],
      isFavourite: json["is_favourited"],
      isBlocked: json["is_blocked"],
      isBlockedByMe: json["is_blocked_by_me"],
      id: json["id"],
      name: json["name"],
      companyName: json["company_name"],
      role: json["role"],
      mobileNumber: json["mobile_number"],
      email: json["email"],
      profileImage: json["profile_image"],
      pincode: json["pincode"],
      cityId: json["city_id"],
      instagramUrl: json["instagram_url"],
      facebookUrl: json["facebook_url"],
      city: json["city"] == null ? null : CountryModel.fromJson(json["city"]),
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
      businesses: json["businesses"] == null
          ? null
          : List<BusinessModel>.from(json["businesses"].map((x) => BusinessModel.fromJson(x))),
      isJoinedDefaultChatGroup: json["is_joined_default_chat_group"] ?? 1,
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "is_email_verified": isEmailVerified,
        "can_send_message": canSendMessage,
        "can_call": canCall,
        "likes_count": likesCount,
        "is_liked": isLiked,
        "is_favourited": isFavourite,
        "is_blocked": isBlocked,
        "is_blocked_by_me": isBlockedByMe,
        "company_name": companyName,
        "role": role,
        "mobile_number": mobileNumber,
        "email": email,
        "profile_image": profileImage,
        "pincode": pincode,
        "city_id": cityId,
        "instagram_url": instagramUrl,
        "facebook_url": facebookUrl,
        "city": city?.toJson(),
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "businesses": businesses == null ? [] : List<dynamic>.from(businesses!.map((x) => x.toJson())),
        "is_joined_default_chat_group": isJoinedDefaultChatGroup,
      };

  @override
  List<Object?> get props => [
        isEmailVerified,
        canSendMessage,
        canCall,
        likesCount,
        isLiked,
        isFavourite,
        isBlocked,
        isBlockedByMe,
        id,
        name,
        companyName,
        role,
        mobileNumber,
        email,
        profileImage,
        pincode,
        cityId,
        instagramUrl,
        facebookUrl,
        city,
        createdAt,
        updatedAt,
        businesses,
        isJoinedDefaultChatGroup,
      ];

  @override
  String toString() {
    return "UserModel($id, $name, $isEmailVerified, $canSendMessage, $canCall, $likesCount, $isLiked, $isFavourite,  $isBlocked, $isBlockedByMe, $companyName, $role, $mobileNumber, $email, $profileImage, $pincode, $cityId, $instagramUrl, $facebookUrl, $city, $businesses, $createdAt, $updatedAt,)";
  }

  String get fullAddress {
    final List<String> address = [];
    if (city?.name != null) address.add(city!.name!);
    if (city?.state?.name != null) address.add(city!.state!.name!);
    if (city?.country?.name != null) address.add(city!.country!.name!);
    return address.join(', ');
  }

  List<BusinessModel> get businessWithCategory {
    return businesses?.where((business) => business.category != null).toList() ?? [];
  }

  static const empty = UserModel(id: 0);

  String get formmtedLikeCount {
    if (likesCount == null) return '';

    if (likesCount! > 1000000000) {
      return '${(likesCount! / 1000000000).toStringAsFixed(1)}B';
    }

    if (likesCount! > 1000000) {
      return '${(likesCount! / 1000000).toStringAsFixed(1)}M';
    }
    if (likesCount! > 1000) {
      return '${(likesCount! / 1000).toStringAsFixed(1)}K';
    }

    if (likesCount! < 2) {
      return '$likesCount';
    }

    return '$likesCount';
  }

  @override
  bool get stringify => true;
}
