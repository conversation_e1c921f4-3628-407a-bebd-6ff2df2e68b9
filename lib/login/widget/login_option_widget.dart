import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class LoginOptionWidget extends StatelessWidget {
  const LoginOptionWidget({super.key, required this.isSelected, required this.title, required this.onTap});
  final bool isSelected;
  final String title;
  final void Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Row(
        children: [
          AppSvgImage(isSelected ? AppAssets.selectedRadioIcon : AppAssets.unselectedRadioIcon),
          const Gap(8),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          )
        ],
      ),
    );
  }
}
