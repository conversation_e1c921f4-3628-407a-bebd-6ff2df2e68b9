import 'package:equatable/equatable.dart';
import 'package:leiuniverse/login/model/user_model.dart';

class UserResponse extends Equatable {
  const UserResponse({
    this.data,
    this.token,
    this.message,
    this.status,
  });

  final UserModel? data;
  final String? token;
  final String? message;
  final int? status;

  factory UserResponse.fromJson(Map<String, dynamic> json) {
    return UserResponse(
      data: json["data"] == null ? null : UserModel.fromJson(json["data"]),
      token: json["token"],
      message: json["message"],
      status: json["status"],
    );
  }

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "token": token,
        "message": message,
        "status": status,
      };

  @override
  List<Object?> get props => [
        data,
        token,
        message,
        status,
      ];

  @override
  String toString() {
    return 'UserResponse(data: $data, token: $token, message: $message, status: $status)';
  }
}
