import 'package:equatable/equatable.dart';

class CommonResponse extends Equatable {
  const CommonResponse({
    this.message,
    this.status,
  });

  factory CommonResponse.fromJson(Map<String, dynamic> json) {
    return CommonResponse(
      message: json['message'] as String?,
      status: json['status'] as int?,
    );
  }

  final String? message;
  final int? status;

  Map<String, dynamic> toJson() => {
        'message': message,
        'status': status,
      };

  @override
  List<Object?> get props => [
        message,
        status,
      ];
}
