import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => ForgotPasswordPageState();
}

class ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final emailController = TextEditingController();
  final isButtonLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    emailController.dispose();
    isButtonLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Forgot Password',
      ),
      body: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              const Gap(16),
              AppTextFormField(
                controller: emailController,
                title: 'Email',
                hintText: '<EMAIL>',
                textInputAction: TextInputAction.next,
                keyboardType: TextInputType.emailAddress,
                isRequired: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter email';
                  } else if (!Utility.isValidEmail(value)) {
                    return 'Please enter valid email';
                  }
                  return null;
                },
              ),
              const Gap(28),
              ValueListenableBuilder<bool>(
                valueListenable: isButtonLoading,
                builder: (context, loading, _) {
                  return CommonButton(
                    isLoading: loading,
                    onTap: () {
                      if (formKey.currentState!.validate()) {
                        forgotPassword();
                      }
                    },
                    text: 'Submit',
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }

  Future<void> forgotPassword() async {
    isButtonLoading.value = true;

    final failOrSuccess = await getIt<IAuthRepository>().forgotPassword(email: emailController.text.trim());

    failOrSuccess.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) {
        isButtonLoading.value = false;
        Utility.toast(message: r.message);
        Navigator.pop(context);
      },
    );
  }
}
