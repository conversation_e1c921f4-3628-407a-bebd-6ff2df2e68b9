import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/home/<USER>/home_page.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/login/view/forgot_password_page.dart';
import 'package:leiuniverse/login/widget/login_option_widget.dart';
import 'package:leiuniverse/setup_business/view/set_up_business_page.dart';
import 'package:leiuniverse/signup/view/sign_up_page.dart';
import 'package:leiuniverse/signup/view/verification_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final isBussinessLogin = ValueNotifier<bool>(false);
  final obscureText = ValueNotifier<bool>(false);
  final emailController = TextEditingController(text: '<EMAIL>'.isDebugging);
  final passwordController = TextEditingController(text: 'password'.isDebugging);
  final isButtonLoading = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    isBussinessLogin.dispose();
    obscureText.dispose();
    emailController.dispose();
    passwordController.dispose();
    isButtonLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CommonAppBar(
                  hideDivider: true,
                ),
                Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        AppAssets.logo,
                        height: 50,
                      ),
                      const Gap(10),
                      Text(
                        'Lei-Universe',
                        style: Theme.of(context).textTheme.headlineLarge,
                      ),
                    ],
                  ),
                ),
                const Gap(22),
                Text(
                  'Login',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const Gap(24),
                ValueListenableBuilder<bool>(
                    valueListenable: isBussinessLogin,
                    builder: (context, value, _) {
                      return Row(
                        children: [
                          LoginOptionWidget(
                            isSelected: !value,
                            title: 'Visitor ID',
                            onTap: () {
                              isBussinessLogin.value = false;
                              if (value) {
                                emailController.clear();
                                passwordController.clear();
                              }
                            },
                          ),
                          const Gap(40),
                          LoginOptionWidget(
                            isSelected: value,
                            title: 'Bussiness ID',
                            onTap: () {
                              isBussinessLogin.value = true;
                              if (!value) {
                                emailController.clear();
                                passwordController.clear();
                              }
                            },
                          ),
                        ],
                      );
                    }),
                const Gap(14),
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: AppColors.offGreen,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ValueListenableBuilder<bool>(
                      valueListenable: isBussinessLogin,
                      builder: (context, value, _) {
                        return Text(
                          value ? AppConstants.bussinessLoginText : AppConstants.visitorLoginText,
                          style: Theme.of(context)
                              .textTheme
                              .labelLarge
                              ?.copyWith(fontSize: 13, letterSpacing: 1.1, height: 1.5),
                        );
                      }),
                ),
                const Gap(14),
                AppTextFormField(
                  controller: emailController,
                  title: 'Email',
                  hintText: '<EMAIL>',
                  textInputAction: TextInputAction.next,
                  keyboardType: TextInputType.emailAddress,
                  isRequired: true,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter email';
                    } else if (!Utility.isValidEmail(value)) {
                      return 'Please enter valid email';
                    }
                    return null;
                  },
                ),
                const Gap(14),
                ValueListenableBuilder<bool>(
                    valueListenable: obscureText,
                    builder: (context, obscure, _) {
                      return AppTextFormField(
                        controller: passwordController,
                        title: 'Password',
                        hintText: 'Password',
                        obscureText: !obscure,
                        isRequired: true,
                        suffixIcon: IconButton(
                          onPressed: () {
                            obscureText.value = !obscure;
                          },
                          icon: AppSvgImage(
                            !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter password';
                          }
                          return null;
                        },
                      );
                    }),
                const Gap(4),
                Align(
                  alignment: Alignment.centerRight,
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ForgotPasswordPage(),
                        ),
                      );
                    },
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Text(
                      'Forgot password?',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ),
                const Gap(30),
                ValueListenableBuilder<bool>(
                    valueListenable: isButtonLoading,
                    builder: (context, loading, _) {
                      return CommonButton(
                        isLoading: loading,
                        onTap: () {
                          if (formKey.currentState!.validate()) {
                            login();
                          }
                        },
                        text: 'Login',
                      );
                    }),
                const Gap(22),
                Center(
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SignUpPage(),
                        ),
                      );
                    },
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(text: 'Don\'t have an account?', style: Theme.of(context).textTheme.bodyMedium),
                          TextSpan(
                            text: ' Signup ',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppColors.primary),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Gap(22),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> login() async {
    isButtonLoading.value = true;
    final response = await getIt<IAuthRepository>().login(
      email: emailController.text,
      password: passwordController.text,
      role: isBussinessLogin.value ? AppConstants.businessRole : AppConstants.visitorRole,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.data != null && r.token != null) {
          getIt<ILocalStorageRepository>().setToken(r.token);
          context.read<AuthenticationBloc>().add(CheckAuthentication(user: r.data));
          if (r.data?.role == AppConstants.businessRole && r.data?.cityId == null) {
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                builder: (context) => const SetUpBusinessPage(),
              ),
              (route) => false,
            );
            return;
          }
          if (r.data?.isEmailVerified == 1) {
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                builder: (context) => const HomePage(),
              ),
              (route) => false,
            );
          } else {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VerificationPage(),
              ),
            );
          }
        }
        Utility.toast(message: r.message);
      },
    );
  }
}
