part of 'i_auth_repository.dart';

@Injectable(as: IAuthRepository)
class AuthRepository extends IAuthRepository {
  AuthRepository(super.client, super.localStorageRepository);

  @override
  ApiResult<UserResponse> login({required String email, required String password, required String role}) async {
    final response = await client.post(
      url: AppStrings.login,
      requests: {
        'email': email.trim(),
        'password': password.trim(),
        'role': role.trim(),
        if (FirebaseMessagingService.token.isPureValid) 'firebase_id': FirebaseMessagingService.token,
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<UserResponse> signUp({
    required String email,
    required String password,
    required String mobileNumber,
    required String name,
    String? companyName,
    required String role,
    File? profileImage,
    String? pincode,
    String? cityId,
  }) async {
    final response = await client.multipart(
      url: AppStrings.signUp,
      files: [
        if (profileImage != null) MapEntry('profile_image', await profileImage.compressFile()),
      ],
      requests: {
        'email': email.trim(),
        'name': name.trim(),
        'password': password.trim(),
        'role': role.trim(),
        'mobile_number': mobileNumber.trim(),
        if (companyName != null) 'company_name': companyName.trim(),
        if (pincode != null) 'pincode': pincode.trim(),
        if (cityId != null) 'city_id': cityId.trim(),
        if (FirebaseMessagingService.token.isPureValid) 'firebase_id': FirebaseMessagingService.token!,
      },
    );

    return response.parseResponse(UserResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> forgotPassword({required String email}) async {
    final response = await client.post(
      url: AppStrings.forgotPassword,
      requests: {
        'email': email.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> verify({required String email, required String otp}) async {
    final response = await client.post(
      url: AppStrings.verifyOtp,
      requests: {
        'email': email.trim(),
        'otp': otp.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> resendOtp({required String email}) async {
    final response = await client.post(
      url: AppStrings.resendOtp,
      requests: {
        'email': email.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final response = await client.post(
      url: AppStrings.changePassword,
      requests: {
        'current_password': currentPassword.trim(),
        'new_password': newPassword.trim(),
      },
    );
    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> logout() async {
    final response = await client.post(url: AppStrings.logout, requests: {
      if (FirebaseMessagingService.token.isPureValid) 'firebase_id': FirebaseMessagingService.token!,
    });

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> deleteAccount() async {
    final response = await client.delete(url: AppStrings.deleteAccount);

    return response.parseResponse(CommonResponse.fromJson);
  }

  @override
  ApiResult<UserResponse> userDetail({String? userId}) async {
    final response = await client.get(
      url: AppStrings.userDetail,
      params: {
        if (userId != null) 'user_id': userId,
      },
    );
    return response.parseResponse(UserResponse.fromJson);
  }
}
