import 'dart:io';

import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/utils/extentions/file_extention.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/firebase_messaging_service.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/login/response/user_response.dart';

part 'auth_repository.dart';

abstract class IAuthRepository {
  IAuthRepository(this.client, this.localStorageRepository);
  final Client client;
  final ILocalStorageRepository localStorageRepository;

  ApiResult<UserResponse> login({
    required String email,
    required String password,
    required String role,
  });

  ApiResult<UserResponse> signUp({
    required String email,
    required String password,
    required String mobileNumber,
    required String name,
    String? companyName,
    required String role,
    File? profileImage,
    String? pincode,
    String? cityId,
  });

  ApiResult<CommonResponse> verify({
    required String email,
    required String otp,
  });

  ApiResult<CommonResponse> resendOtp({
    required String email,
  });

  ApiResult<CommonResponse> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  ApiResult<CommonResponse> forgotPassword({
    required String email,
  });

  ApiResult<CommonResponse> logout();

  ApiResult<CommonResponse> deleteAccount();

  ApiResult<UserResponse> userDetail({String? userId});
}
