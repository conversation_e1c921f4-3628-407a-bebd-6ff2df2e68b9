import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class CityDropdown extends StatefulWidget {
  const CityDropdown({
    super.key,
    this.stateId,
    this.onCityChanged,
    this.isMulti = false,
    this.isOptional = false,
    this.isAddAll = false,
    this.selectedCity = const <CountryModel>[],
    this.isRequired = false,
  });

  final int? stateId;
  final List<CountryModel> selectedCity;
  final void Function(List<CountryModel> city)? onCityChanged;
  final bool isMulti;
  final bool isOptional;
  final bool isAddAll;
  final bool isRequired;

  @override
  State<CityDropdown> createState() => CityDropdownState();
}

class CityDropdownState extends State<CityDropdown> {
  late final cityList = ValueNotifier<List<CountryModel>>([]);
  late final selectedCity = ValueNotifier<List<CountryModel>>([]);

  @override
  void initState() {
    super.initState();

    final List<CountryModel> initialSelected =
        widget.selectedCity.isEmpty ? (widget.isAddAll ? [CountryModel.all] : []) : widget.selectedCity;

    selectedCity.value = initialSelected;
    cityList.value = [
      if (widget.isAddAll) CountryModel.all,
      ...initialSelected.where((e) => e.id != null),
    ];
  }

  @override
  void didUpdateWidget(covariant CityDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);

    final List<CountryModel> newSelection =
        widget.selectedCity.isEmpty ? (widget.isAddAll ? [CountryModel.all] : []) : widget.selectedCity;

    final oldIds = selectedCity.value.map((e) => e.id).toSet();
    final newIds = newSelection.map((e) => e.id).toSet();

    final selectionChanged = !setEquals(oldIds, newIds);
    final stateChanged = widget.stateId != oldWidget.stateId;

    if (selectionChanged || stateChanged) {
      final shouldReset = stateChanged;

      selectedCity.value = shouldReset ? [] : newSelection;
      cityList.value = [
        if (widget.isAddAll) CountryModel.all,
        ...(shouldReset ? [] : newSelection.where((e) => e.id != null)),
      ];

      if (shouldReset) {
        widget.onCityChanged?.call([]);
      }
    }
  }

  @override
  void dispose() {
    cityList.dispose();
    selectedCity.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.stateId == null) {
          return Utility.toast(message: 'Please select state first');
        }

        AppDailogs.selectMultiCityDailog(
          context,
          stateId: widget.stateId,
          isMulti: widget.isMulti,
          isAddAll: widget.isAddAll,
          selectedCityList: selectedCity.value,
          onCityChanged: (cities) {
            final hasAll = cities?.any((e) => e.id == null) ?? false;
            final List<CountryModel> cleanedList = hasAll ? [] : cities ?? [];

            selectedCity.value = cities ?? [];
            cityList.value = [
              if (widget.isAddAll) CountryModel.all,
              ...cities?.where((e) => e.id != null) ?? [],
            ];

            widget.onCityChanged?.call(cleanedList);
          },
        );
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: ValueListenableBuilder<List<CountryModel>>(
        valueListenable: cityList,
        builder: (context, categories, _) {
          return ValueListenableBuilder<List<CountryModel>>(
            valueListenable: selectedCity,
            builder: (context, selectedCategories, _) {
              final isAllSelected = selectedCategories.firstOrNull?.id == null;

              return IgnorePointer(
                child: AppDropDown<CountryModel>(
                  isRequired: widget.isRequired,
                  key: ValueKey(selectedCategories.hashCode),
                  selectedValue: selectedCategories.firstOrNull,
                  onSelect: (_) {},
                  items: categories
                      .map((e) => DropdownMenuItem(
                            value: e,
                            child: Text(e.name ?? ''),
                          ))
                      .toList(),
                  hintText: selectedCategories.isEmpty || isAllSelected ? 'Select' : null,
                  title: 'City',
                  validator: widget.isOptional
                      ? null
                      : (value) {
                          if (value == null || value.id == null) {
                            return 'Please select city';
                          }
                          return null;
                        },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
