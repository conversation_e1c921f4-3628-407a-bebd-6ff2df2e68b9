import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class CommonAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CommonAppBar({
    super.key,
    this.onBackTap,
    this.title,
    this.titleWidget,
    this.actions,
    this.titleSpacing,
    this.leadingWidget,
    this.leadingWidth,
    this.centerTitle = false,
    this.hideDivider = false,
  });
  final VoidCallback? onBackTap;
  final String? title;
  final Widget? titleWidget;
  final Widget? leadingWidget;
  final List<Widget>? actions;
  final bool hideDivider;
  final double? titleSpacing;
  final double? leadingWidth;
  final bool centerTitle;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        AppBar(
          backgroundColor: AppColors.bgColor,
          leading: onBackTap != null
              ? IconButton(
                  splashRadius: 24,
                  onPressed: onBackTap,
                  icon: const AppSvgImage(
                    AppAssets.leftArrowIcon,
                    height: 24,
                    width: 24,
                  ))
              : leadingWidget,
          centerTitle: centerTitle,
          leadingWidth: onBackTap != null ? null : leadingWidth,
          titleSpacing: titleSpacing ?? 0,
          title: title != null
              ? Text(
                  title!,
                  style: Theme.of(context).textTheme.headlineMedium,
                )
              : titleWidget,
          actions: actions,
          elevation: 0,
        ),
        if (!hideDivider) const Divider(height: 1, color: AppColors.border),
      ],
    );
  }

  @override
  Size get preferredSize => hideDivider ? AppBar().preferredSize : AppBar().preferredSize + const Offset(0, 1);
}
