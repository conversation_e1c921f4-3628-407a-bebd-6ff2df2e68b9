import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/common_button.dart';

class BottomButtonWidget extends StatelessWidget {
  const BottomButtonWidget({super.key, required this.onTap, required this.text, this.isLoading = false});

  final VoidCallback onTap;
  final String text;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, -3),
                blurRadius: 21,
                spreadRadius: 0,
                color: AppColors.navyColor.withOpacity2(0.06),
              ),
            ],
          ),
          child: CommonButton(
            isLoading: isLoading,
            onTap: onTap,
            margin: const EdgeInsets.only(bottom: 20),
            text: text,
          ),
        ),
      ],
    );
  }
}
