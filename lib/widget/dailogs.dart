import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:leiuniverse/chat/widget/block_user_dailog.dart';
import 'package:leiuniverse/chat/widget/delete_chat_dailog.dart';
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart';
import 'package:leiuniverse/event_promotion/widget/delete_event_dialog.dart';
import 'package:leiuniverse/market_place/widget/delete_advertisement_dailog.dart';
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/widget/delete_business_dialog.dart';
import 'package:leiuniverse/setup_business/widget/select_category_dialog.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/signup/widget/select_city_dialog.dart';
import 'package:leiuniverse/signup/widget/select_country_dialog.dart';
import 'package:leiuniverse/signup/widget/select_state_dialog.dart';

final class AppDailogs {
  static Future<void> selectCategoryDailog(
    BuildContext context, {
    int? parentId,
    required String title,
    List<CategoryModel>? selectedCategory,
    void Function(List<CategoryModel>? cities)? onCategoryChanged,
    bool isMulti = false,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SelectCategoryDialog(
        title: title,
        selectedCategory: selectedCategory,
        parentId: parentId,
        onCategoryChanged: onCategoryChanged,
        isMulti: isMulti,
      ),
    );
  }

  static Future<void> selectCountryDailog(
    BuildContext context, {
    CountryModel? selectedCountry,
    void Function(CountryModel? country)? onCountryChanged,
    bool isAddAll = false,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SelectCountryDialog(
        selectedCountry: selectedCountry,
        onCountryChanged: onCountryChanged,
        isAddAll: isAddAll,
      ),
    );
  }

  static Future<void> selectStateDailog(
    BuildContext context, {
    CountryModel? selectedState,
    int? countryId,
    void Function(CountryModel? state)? onStateChanged,
    bool isAddAll = false,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SelectStateDialog(
        countryId: countryId,
        selectedState: selectedState,
        onStateChanged: onStateChanged,
        isAddAll: isAddAll,
      ),
    );
  }

  static Future<void> selectMultiCityDailog(
    BuildContext context, {
    List<CountryModel>? selectedCityList,
    void Function(List<CountryModel>? cities)? onCityChanged,
    int? stateId,
    bool isMulti = false,
    bool isAddAll = false,
  }) {
    return showDialog(
      context: context,
      builder: (context) => SelectCityDialog(
        stateId: stateId,
        selectedCityList: selectedCityList,
        onCitiesChanged: onCityChanged,
        isMulti: isMulti,
        isAddAll: isAddAll,
      ),
    );
  }

  static Future<void> deleteBusinessDailog(BuildContext context, {required int businessId}) {
    return showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<BusinessBloc>(),
        child: DeleteBusinessDialog(businessId: businessId),
      ),
    );
  }

  static Future<void> deleteEventDailog(BuildContext context, {required int eventId, bool isPopTwice = false}) {
    return showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<EventBloc>(),
        child: DeleteEventDialog(eventId: eventId, isPopTwice: isPopTwice),
      ),
    );
  }

  static Future<void> deleteAdvertisementDailog(
    BuildContext context, {
    required int advertisementId,
    void Function()? onDelete,
  }) {
    return showDialog(
      context: context,
      builder: (_) => DeleteAdvertisementDialog(id: advertisementId, onDelete: onDelete),
    );
  }

  static Future<void> deleteChatDailog(
    BuildContext context, {
    bool isGroup = false,
    void Function()? onDelete,
  }) {
    return showDialog(
      context: context,
      builder: (_) => DeleteChatDialog(onDelete: onDelete, isGroup: isGroup),
    );
  }

  static Future<void> blockUserDailog(
    BuildContext context, {
    void Function()? onBlock,
  }) {
    return showDialog(
      context: context,
      builder: (_) => BlockUserDialog(onBlockTap: onBlock),
    );
  }
}
