import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class CountryDropdown extends StatefulWidget {
  const CountryDropdown({
    super.key,
    this.onCountryChanged,
    this.selectedCountry,
    this.isOptional = false,
    this.isAddAll = false,
    this.isRequired = false,
  });
  final CountryModel? selectedCountry;
  final void Function(CountryModel country)? onCountryChanged;
  final bool isOptional;
  final bool isAddAll;
  final bool isRequired;

  @override
  State<CountryDropdown> createState() => CountryDropdownState();
}

class CountryDropdownState extends State<CountryDropdown> {
  late final countryList = ValueNotifier<List<CountryModel>>([
    if (widget.isAddAll) CountryModel.all,
    if (widget.selectedCountry != null && widget.selectedCountry!.id != null) widget.selectedCountry!,
  ]);

  late final selectedCountry = ValueNotifier<CountryModel?>(
    widget.selectedCountry ?? (widget.isAddAll ? CountryModel.all : null),
  );

  @override
  void dispose() {
    countryList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        AppDailogs.selectCountryDailog(context, selectedCountry: selectedCountry.value, isAddAll: widget.isAddAll,
            onCountryChanged: (country) {
          countryList.value = [
            if (widget.isAddAll) CountryModel.all,
            if (country != null && country.id != null) country
          ];

          selectedCountry.value = country?.id == null && !widget.isAddAll ? null : country;

          if (country == null && !widget.isAddAll) return;
          widget.onCountryChanged?.call(country ?? CountryModel.all);
        });
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: ValueListenableBuilder<List<CountryModel>>(
        valueListenable: countryList,
        builder: (context, countries, _) {
          return ValueListenableBuilder<CountryModel?>(
            valueListenable: selectedCountry,
            builder: (context, selectedCountry, _) {
              return IgnorePointer(
                child: AppDropDown<CountryModel>(
                  isRequired: widget.isRequired,
                  key: ValueKey(selectedCountry.hashCode),
                  selectedValue: selectedCountry,
                  onSelect: (value) {},
                  items: countries.map((e) => DropdownMenuItem(value: e, child: Text(e.name ?? ''))).toList(),
                  hintText: 'Select',
                  title: 'Country',
                  validator: widget.isOptional
                      ? null
                      : (value) {
                          if (value == null || value.id == null) {
                            return 'Please select country';
                          }
                          return null;
                        },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
