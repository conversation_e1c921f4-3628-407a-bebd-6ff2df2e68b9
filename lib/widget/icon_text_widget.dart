import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class IconTextWidget extends StatelessWidget {
  const IconTextWidget({super.key, required this.icon, required this.text, this.onTap});
  final String icon;
  final String text;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppSvgImage(
            icon,
            height: 20,
            width: 20,
          ),
          const Gap(6),
          Text(
            text,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.subText),
          ),
        ],
      ),
    );
  }
}
