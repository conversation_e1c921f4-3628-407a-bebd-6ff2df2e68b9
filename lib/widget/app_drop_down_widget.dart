import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class AppDropDown<T> extends StatelessWidget {
  const AppDropDown({
    required this.onSelect,
    required this.items,
    super.key,
    this.validator,
    this.selectedValue,
    this.hintText,
    this.title,
    this.maxWidth,
    this.maxHeight,
    this.contentPadding,
    this.hintTextColor,
    this.isRequired = false,
  });
  final Function(T? valueOfCategory) onSelect;
  final String? Function(T?)? validator;

  final T? selectedValue;
  final String? hintText;
  final List<DropdownMenuItem<T>>? items;
  final String? title;
  final double? maxWidth;
  final double? maxHeight;
  final EdgeInsetsGeometry? contentPadding;
  final Color? hintTextColor;
  final bool isRequired;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (title != null) ...[
          Text(
            '${title!.inCaps}${isRequired ? '*' : ''}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.text,
                ),
          ),
          const Gap(6),
        ],
        DropdownButtonFormField<T>(
          value: selectedValue,
          style: Theme.of(context)
              .textTheme
              .bodyMedium
              ?.copyWith(fontWeight: FontWeight.w400, overflow: TextOverflow.ellipsis, height: 1.2),
          onChanged: onSelect,
          hint: Text(
            hintText ?? '',
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(fontSize: 15, color: hintTextColor ?? AppColors.text, height: 1.2),
          ),
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
            constraints: BoxConstraints(
              maxWidth: maxWidth ?? 400,
            ),
          ),
          validator: validator,
          icon: const AppSvgImage(AppAssets.arrowDownIcon),
          items: items,
          isExpanded: true,
          dropdownColor: AppColors.white,
        ),
      ],
    );
  }
}
