import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class CommonButton extends StatelessWidget {
  const CommonButton({
    this.text,
    required this.onTap,
    super.key,
    this.isLoading = false,
    this.backgroundColor,
    this.removeShadow = false,
    this.textColor,
    this.width,
    this.icon,
    this.padding,
    this.textStyle,
    this.margin,
    this.borderRadius,
    this.height,
    this.fontSize,
    this.borderColor,
    this.isLessShadow = false,
  });
  final String? text;
  final void Function()? onTap;
  final bool isLoading;
  final Color? backgroundColor;
  final bool removeShadow;
  final Color? textColor;
  final Color? borderColor;
  final double? width;
  final double? height;
  final Widget? icon;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final double? fontSize;
  final bool isLessShadow;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      margin: margin,
      decoration: BoxDecoration(
        border: removeShadow ? Border.all(color: borderColor ?? AppColors.primary, width: 1) : null,
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        boxShadow: removeShadow
            ? null
            : [
                isLessShadow
                    ? BoxShadow(
                        color: AppColors.primary.withOpacity2(0.25),
                        spreadRadius: 0,
                        blurRadius: 14,
                        offset: const Offset(0, 4),
                      )
                    : BoxShadow(
                        color: AppColors.primary.withOpacity2(0.38),
                        spreadRadius: 0,
                        blurRadius: 14,
                        offset: const Offset(0, 7),
                      ),
              ],
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: removeShadow ? backgroundColor ?? AppColors.white : backgroundColor,
          padding: padding ?? EdgeInsets.symmetric(vertical: isLoading ? 9 : 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius ?? 12),
          ),
        ),
        onPressed: isLoading ? () {} : onTap,
        child: isLoading
            ? CustomProgressIndecator(
                size: 34,
                color: removeShadow ? AppColors.primary : null,
              )
            : text != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (icon != null) ...[icon!, const Gap(8)],
                      Text(
                        text!,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: textStyle ??
                            Theme.of(context).elevatedButtonTheme.style?.textStyle?.resolve({})?.copyWith(
                              color: removeShadow ? textColor ?? AppColors.primary : textColor,
                              fontSize: fontSize,
                            ),
                      ),
                    ],
                  )
                : null,
      ),
    );
  }
}
