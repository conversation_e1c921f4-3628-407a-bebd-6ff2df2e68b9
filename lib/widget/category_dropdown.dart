import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/string_extentions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class CategoryDropdown extends StatefulWidget {
  const CategoryDropdown({
    super.key,
    required this.title,
    this.parentId,
    this.parentCategoryName,
    this.onCategoryChanged,
    this.selectedCategory = const <CategoryModel>[],
    this.isMulti = false,
    this.isOptional = false,
    this.isRequired = false,
  });
  final String title;
  final int? parentId;
  final String? parentCategoryName;
  final List<CategoryModel> selectedCategory;
  final void Function(List<CategoryModel>? categories)? onCategoryChanged;
  final bool isMulti;
  final bool isOptional;
  final bool isRequired;

  @override
  State<CategoryDropdown> createState() => CategoryDropdownState();
}

class CategoryDropdownState extends State<CategoryDropdown> {
  late final categoryList = ValueNotifier<List<CategoryModel>>([...widget.selectedCategory]);
  late final selectedCategory = ValueNotifier<List<CategoryModel>>(widget.selectedCategory);

  @override
  void initState() {
    super.initState();
    if (widget.selectedCategory.isEmpty) {
      // 👇 Add "All" default item
      const allCategory = CategoryModel(id: null, name: "All");
      categoryList.value = [allCategory];
      selectedCategory.value = [allCategory];
    } else {
      categoryList.value = [...widget.selectedCategory];
      selectedCategory.value = [...widget.selectedCategory];
    }
  }

  void resetSelection() {
    categoryList.value = [const CategoryModel(id: null, name: "All")];
    selectedCategory.value = [const CategoryModel(id: null, name: "All")];
  }

  @override
  void dispose() {
    categoryList.dispose();
    selectedCategory.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      overlayColor: WidgetStatePropertyAll(AppColors.transparent),
      onTap: () {
        if (widget.parentCategoryName.isPureValid && widget.parentId == null) {
          Utility.toast(message: 'Please select ${widget.parentCategoryName} first');
          return;
        }
        AppDailogs.selectCategoryDailog(
          context,
          title: widget.title,
          parentId: widget.parentId,
          selectedCategory: selectedCategory.value,
          onCategoryChanged: (category) {
            final hasAll = category?.any((e) => e.id == null) ?? false;

            final List<CategoryModel>? cleanedList = hasAll ? null : category ?? [];

            categoryList.value = category ?? [];
            selectedCategory.value = category ?? [];
            widget.onCategoryChanged?.call(cleanedList);
          },
          isMulti: widget.isMulti,
        );
      },
      child: ValueListenableBuilder<List<CategoryModel>>(
        valueListenable: categoryList,
        builder: (context, categories, _) {
          return ValueListenableBuilder<List<CategoryModel>>(
            valueListenable: selectedCategory,
            builder: (context, selectedCategorires, _) {
              final isAllSelected = selectedCategorires.firstOrNull?.id == null && widget.parentId == null;
              return IgnorePointer(
                child: AppDropDown<CategoryModel>(
                  isRequired: widget.isRequired,
                  key: ValueKey(selectedCategorires.hashCode),
                  selectedValue: selectedCategorires.firstOrNull,
                  onSelect: (value) {},
                  items: categories.map((e) => DropdownMenuItem(value: e, child: Text(e.name ?? ''))).toList(),
                  hintText: isAllSelected ? null : 'Select',
                  title: widget.title,
                  validator: widget.isOptional
                      ? null
                      : (value) {
                          if (value == null || value.id == null) {
                            return 'Please select a ${widget.title.toLowerCase()}';
                          }
                          return null;
                        },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
