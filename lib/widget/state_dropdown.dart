import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/signup/model/country_model.dart';
import 'package:leiuniverse/widget/app_drop_down_widget.dart';
import 'package:leiuniverse/widget/dailogs.dart';

class StateDropdown extends StatefulWidget {
  const StateDropdown({
    super.key,
    this.countryId,
    this.onStateChanged,
    this.selectedState,
    this.isOptional = false,
    this.isAddAll = false,
    this.isRequired = false,
  });

  final int? countryId;
  final CountryModel? selectedState;
  final void Function(CountryModel state)? onStateChanged;
  final bool isOptional;
  final bool isAddAll;
  final bool isRequired;

  @override
  State<StateDropdown> createState() => StateDropdownState();
}

class StateDropdownState extends State<StateDropdown> {
  late final selectedState = ValueNotifier<CountryModel?>(
    widget.selectedState ?? (widget.isAddAll ? CountryModel.all : null),
  );

  late final stateList = ValueNotifier<List<CountryModel>>([
    if (widget.isAddAll) CountryModel.all,
    if (widget.selectedState != null && widget.selectedState!.id != null) widget.selectedState!,
  ]);

  @override
  void didUpdateWidget(covariant StateDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If selected state has changed externally (e.g., from reset or edit), update
    final newState = widget.selectedState ?? (widget.isAddAll ? CountryModel.all : null);
    if (selectedState.value?.id != newState?.id) {
      selectedState.value = newState;

      stateList.value = [
        if (widget.isAddAll) CountryModel.all,
        if (newState != null && newState.id != null) newState,
      ];
    }
  }

  @override
  void dispose() {
    selectedState.dispose();
    stateList.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widget.countryId == null) {
          return Utility.toast(message: 'Please select country first');
        }

        AppDailogs.selectStateDailog(
          context,
          countryId: widget.countryId,
          selectedState: selectedState.value,
          isAddAll: widget.isAddAll,
          onStateChanged: (state) {
            // Always reset list based on selected value
            stateList.value = [
              if (widget.isAddAll) CountryModel.all,
              if (state != null && state.id != null) state,
            ];

            selectedState.value = state?.id == null && !widget.isAddAll ? null : state;

            if (state == null && !widget.isAddAll) return;

            widget.onStateChanged?.call(state ?? CountryModel.all);
          },
        );
      },
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: ValueListenableBuilder<List<CountryModel>>(
        valueListenable: stateList,
        builder: (context, states, _) {
          return ValueListenableBuilder<CountryModel?>(
            valueListenable: selectedState,
            builder: (context, selectedState, _) {
              return IgnorePointer(
                child: AppDropDown<CountryModel>(
                  isRequired: widget.isRequired,
                  key: ValueKey(selectedState?.id), // important for refresh
                  selectedValue: selectedState,
                  onSelect: (_) {},
                  items: states
                      .map((e) => DropdownMenuItem(
                            value: e,
                            child: Text(e.name ?? ''),
                          ))
                      .toList(),
                  hintText: 'Select',
                  title: 'State',
                  validator: widget.isOptional
                      ? null
                      : (value) {
                          if (value == null || value.id == null) {
                            return 'Please select state';
                          }
                          return null;
                        },
                ),
              );
            },
          );
        },
      ),
    );
  }
}
