import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class UserInfoWidget extends StatelessWidget {
  const UserInfoWidget({super.key, this.user});

  final UserModel? user;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      overlayColor: WidgetStatePropertyAll(AppColors.transparent),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProfileDetailPage(userId: user?.id),
          ),
        );
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Utility.imageLoader(
            url: user?.profileImage ?? '',
            placeholder: AppAssets.userAvtarImage,
            height: 36,
            width: 36,
            fit: BoxFit.cover,
            borderRadius: BorderRadius.circular(6),
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, 4),
                blurRadius: 4,
                spreadRadius: 0,
                color: AppColors.black.withOpacity2(0.15),
              ),
            ],
          ),
          const Gap(14),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        user?.name ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                    if ((user?.likesCount ?? 0) > 500) ...[
                      const Gap(3),
                      const AppSvgImage(
                        AppAssets.verifyIcon,
                        height: 17,
                        width: 17,
                      ),
                    ],
                  ],
                ),
                if (user?.companyName != null && user!.companyName!.isNotEmpty)
                  Text(
                    user!.companyName!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                          color: AppColors.subText,
                        ),
                  ),
              ],
            ),
          ),
          // if (user?.role == AppConstants.businessRole) ...[
          //   const Gap(10),
          //   Row(
          //     children: [
          //       const AppSvgImage(AppAssets.fillStarIcon, height: 18, width: 18),
          //       const Gap(6),
          //       Text(
          //         '4.6',
          //         style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
          //       ),
          //     ],
          //   ),
          // ],
        ],
      ),
    );
  }
}
