import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';

class UserDataWidget extends StatelessWidget {
  const UserDataWidget({super.key, this.user});

  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Utility.imageLoader(
              url: user?.profileImage ?? '',
              placeholder: AppAssets.userAvtarImage,
              height: 60,
              width: 60,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.circular(6),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 4),
                  blurRadius: 4,
                  spreadRadius: 0,
                  color: AppColors.black.withOpacity2(0.15),
                ),
              ],
            ),
            const Gap(14),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Row(
                          children: [
                            Flexible(
                              child: Text(
                                user?.name ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.headlineMedium,
                              ),
                            ),
                            if ((user?.likesCount ?? 0) > 500) ...[
                              const Gap(3),
                              const AppSvgImage(
                                AppAssets.verifyIcon,
                                height: 17,
                                width: 17,
                              ),
                            ],
                          ],
                        ),
                      ),
                      // if (user?.role == AppConstants.businessRole) ...[
                      //   const AppSvgImage(AppAssets.fillStarIcon, height: 18, width: 18),
                      //   const Gap(6),
                      //   Text(
                      //     '4.6',
                      //     style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 1.2),
                      //   ),
                      // ],
                    ],
                  ),
                  const Gap(4),
                  if (user?.companyName != null && user!.companyName!.isNotEmpty) ...[
                    Text(
                      user?.companyName ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            color: AppColors.subText,
                          ),
                    ),
                    const Gap(4),
                  ],
                  if (user?.city?.name != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        color: AppColors.background,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const AppSvgImage(
                            AppAssets.locationIcon,
                            height: 16,
                            width: 16,
                          ),
                          const Gap(2),
                          Text(
                            '${user?.fullAddress}',
                            style: Theme.of(context).textTheme.labelLarge,
                          ),
                        ],
                      ),
                    )
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
