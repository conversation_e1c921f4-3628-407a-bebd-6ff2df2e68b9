import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';

class CategoryTextWidget extends StatelessWidget {
  const CategoryTextWidget({super.key, this.category, this.onTap});
  final CategoryModel? category;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          category?.name ?? '',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(height: 1.2),
        ),
      ),
    );
  }
}
