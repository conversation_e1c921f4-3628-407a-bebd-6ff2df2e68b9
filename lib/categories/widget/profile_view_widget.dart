import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/categories/widget/user_data_widget.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/url_manager.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/setup_business/widget/category_type_widget.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/common_button.dart';
import 'package:leiuniverse/widget/icon_text_widget.dart';

class UserProfileWidget extends StatelessWidget {
  const UserProfileWidget({
    super.key,
    this.onTap,
    this.likeOnTap,
    this.messageOnTap,
    this.user,
  });
  final void Function()? onTap;
  final void Function()? likeOnTap;
  final void Function()? messageOnTap;
  final UserModel? user;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UserDataWidget(
              user: user,
            ),
            const Gap(14),
            if (user?.businessWithCategory != null && user?.businessWithCategory.isNotEmpty == true) ...[
              Wrap(
                runSpacing: 10,
                spacing: 10,
                children: List.generate(
                  user?.businessWithCategory.length ?? 0,
                  (index) => CategoryTypeWidget(
                    title: user?.businessWithCategory[index].category?.parentCategory?.parentCategory?.name ??
                        user?.businessWithCategory[index].category?.parentCategory?.name ??
                        user?.businessWithCategory[index].category?.name ??
                        '',
                  ),
                ),
              ),
              const Gap(14),
            ],
            const Divider(
              height: 0,
            ),
            const Gap(14),
            Row(
              children: [
                IconTextWidget(
                  icon: user?.isLiked == 0 ? AppAssets.dislikeHandIcon : AppAssets.likeHandIcon,
                  text: '${user?.likesCount ?? 0} Likes',
                  onTap: likeOnTap,
                ),
                const Spacer(),
                if (user?.canSendMessage == 1 && context.read<AuthenticationBloc>().state.user.id != user?.id)
                  InkWell(
                    onTap: messageOnTap,
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: const AppSvgImage(
                      AppAssets.roundMessageIcon,
                    ),
                  ),
                if (user?.canCall == 1 && user?.mobileNumber != null) ...[
                  const Gap(20),
                  CommonButton(
                    onTap: () {
                      UrlManager.openDialpad(user?.mobileNumber);
                    },
                    isLessShadow: true,
                    width: 93,
                    height: 34,
                    padding: EdgeInsets.zero,
                    borderRadius: 200,
                    text: 'Call',
                    icon: const AppSvgImage(AppAssets.callIcon, height: 20, width: 20),
                  ),
                ],
              ],
            )
          ],
        ),
      ),
    );
  }
}
