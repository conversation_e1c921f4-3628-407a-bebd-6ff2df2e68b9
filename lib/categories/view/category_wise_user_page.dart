import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/widget/profile_view_widget.dart';
import 'package:leiuniverse/chat/view/chat_detail_page.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/profile/view/profile_detail_page.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class CategoriesWiseUserPage extends StatefulWidget {
  const CategoriesWiseUserPage({super.key, this.categoryModel});
  final CategoryModel? categoryModel;

  @override
  State<CategoriesWiseUserPage> createState() => _CategoriesWiseUserPageState();
}

class _CategoriesWiseUserPageState extends State<CategoriesWiseUserPage> with PaginationMixin {
  final searchController = TextEditingController();
  final businessUserList = ValueNotifier<List<UserModel>>(<UserModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    businessUserList.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IBusinessRepository>()
        .businessUserList(categoryId: widget.categoryModel?.id, search: searchController.text.trim());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      businessUserList.value = [...r.data ?? []];
      hasReachedMax = (r.data?.length ?? 0) < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess =
        await getIt<IBusinessRepository>().businessUserList(page: page, categoryId: widget.categoryModel?.id);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = (r.data?.length ?? 0) < 10;
      businessUserList.value = [...businessUserList.value, ...r.data ?? []];
    });
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  Future<void> likeUser({required int id}) async {
    final previousUser = businessUserList.value.firstWhere((e) => e.id == id);

    businessUserList.value = businessUserList.value
        .map((e) => e.id == id
            ? e.copyWith(
                isLiked: e.isLiked == 0 ? 1 : 0,
                likesCount: e.isLiked == 0 ? (e.likesCount ?? 0) + 1 : (e.likesCount ?? 0) - 1)
            : e)
        .toList();

    final failOrSuccess = await getIt<IUserRepository>().likeUser(id: id);

    failOrSuccess.fold((l) {
      businessUserList.value = businessUserList.value.map((e) => e.id == id ? previousUser : e).toList();

      Utility.toast(message: l.message);
    }, (r) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: widget.categoryModel?.name ?? '',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppTextFormField(
              controller: searchController,
              hintText: 'Search...',
              fillColor: AppColors.white,
              prefixIcon: const Padding(
                padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                child: AppSvgImage(
                  AppAssets.searchIcon,
                  color: AppColors.subText,
                ),
              ),
              onChanged: (value) {
                EasyDebounce.debounce(
                  'search_business',
                  const Duration(milliseconds: 500),
                  () {
                    load();
                  },
                );
              },
            ),
            Flexible(
              child: ValueListenableBuilder<bool>(
                valueListenable: isLoading,
                builder: (context, loading, _) {
                  return ValueListenableBuilder<List<UserModel>>(
                    valueListenable: businessUserList,
                    builder: (context, users, _) {
                      if (loading) {
                        return const Center(
                          child: CustomProgressIndecator(
                            size: 40,
                            color: AppColors.primary,
                          ),
                        );
                      }
                      if (!loading && users.isEmpty) {
                        return Utility.noDataWidget(context: context, text: 'No User Found');
                      }
                      return RefreshIndicator(
                        onRefresh: () {
                          return load();
                        },
                        child: ListView.separated(
                          physics: const AlwaysScrollableScrollPhysics(),
                          controller: scrollPaginationController,
                          itemCount: users.length,
                          separatorBuilder: (context, index) {
                            return const Gap(20);
                          },
                          padding: const EdgeInsets.symmetric(vertical: 20),
                          itemBuilder: (context, index) {
                            return ValueListenableBuilder<bool>(
                              valueListenable: isLoadingMore,
                              builder: (context, loadingMore, _) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    UserProfileWidget(
                                      user: users[index],
                                      onTap: () async {
                                        final newUser = await Navigator.push<UserModel>(
                                          context,
                                          MaterialPageRoute(
                                            builder: (_) => ProfileDetailPage(userId: users[index].id),
                                          ),
                                        );
                                        if (newUser != null) {
                                          businessUserList.value = [
                                            ...businessUserList.value.map((e) => e.id == newUser.id
                                                ? e.copyWith(
                                                    isLiked: newUser.isLiked,
                                                    likesCount: newUser.likesCount,
                                                  )
                                                : e)
                                          ];
                                        }
                                      },
                                      likeOnTap: () {
                                        likeUser(id: users[index].id);
                                      },
                                      messageOnTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => ChatDetailWrapperPage(
                                              userId: users[index].id,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                    if (!hasReachedMax && loadingMore && index == users.length - 1)
                                      const CustomProgressIndecator(color: AppColors.primary, size: 40),
                                  ],
                                );
                              },
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
