import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:leiuniverse/categories/view/sub_categories_page.dart';
import 'package:leiuniverse/categories/widget/category_item_view.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class CategoriesPage extends StatefulWidget {
  const CategoriesPage({super.key});

  @override
  State<CategoriesPage> createState() => _CategoriesPageState();
}

class _CategoriesPageState extends State<CategoriesPage> with PaginationMixin {
  final categoryList = ValueNotifier<List<CategoryModel>>(<CategoryModel>[]);
  final searchController = TextEditingController();
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    categoryList.dispose();
    searchController.dispose();
    disposePagination();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IBusinessRepository>().categoryList(search: searchController.text.trim());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      categoryList.value = [...r.data];
      hasReachedMax = r.data.length < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IBusinessRepository>().categoryList(page: page);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = r.data.length < 10;
      categoryList.value = [...categoryList.value, ...r.data];
    });
  }

  Future<void> refresh() async {
    categoryList.value = [];
    page = 1;
    hasReachedMax = false;
    await load();
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'Categories',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ValueListenableBuilder<bool>(
            valueListenable: isLoading,
            builder: (context, loading, _) {
              return Stack(
                children: [
                  Column(
                    children: [
                      AppTextFormField(
                        controller: searchController,
                        hintText: 'Search',
                        fillColor: AppColors.white,
                        prefixIcon: const Padding(
                          padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                          child: AppSvgImage(
                            AppAssets.searchIcon,
                            color: AppColors.subText,
                          ),
                        ),
                        onChanged: (value) {
                          EasyDebounce.debounce(
                            'search_category',
                            const Duration(milliseconds: 500),
                            () {
                              refresh();
                            },
                          );
                        },
                      ),
                      Expanded(
                        child: ValueListenableBuilder<bool>(
                          valueListenable: isLoadingMore,
                          builder: (context, loadingMore, _) {
                            return ValueListenableBuilder<List<CategoryModel>>(
                              valueListenable: categoryList,
                              builder: (context, categories, _) {
                                if (!loading && categories.isEmpty) {
                                  return Utility.noDataWidget(context: context, text: 'No Category Found');
                                }
                                return RefreshIndicator(
                                  onRefresh: () {
                                    return refresh();
                                  },
                                  child: GridView.builder(
                                    physics: const AlwaysScrollableScrollPhysics(),
                                    padding: const EdgeInsets.only(top: 20),
                                    controller: scrollPaginationController,
                                    itemCount:
                                        (loadingMore && !hasReachedMax) ? (categories.length) + 1 : categories.length,
                                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      mainAxisSpacing: 20,
                                      crossAxisSpacing: 20,
                                    ),
                                    itemBuilder: (context, index) {
                                      if ((index == categories.length) && loadingMore) {
                                        return const SizedBox(
                                          height: 200,
                                          width: 200,
                                          child: Center(
                                            child: CustomProgressIndecator(
                                              size: 40,
                                              color: AppColors.primary,
                                            ),
                                          ),
                                        );
                                      }

                                      final category = categories[index];
                                      return CategoryItemView(
                                        category: category,
                                        onTap: () {
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder: (context) => SubCategoriesPage(
                                                category: category,
                                              ),
                                            ),
                                          );
                                        },
                                      );
                                    },
                                  ),
                                );
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  if (loading)
                    const Center(
                      child: CustomProgressIndecator(
                        size: 40,
                        color: AppColors.primary,
                      ),
                    ),
                ],
              );
            }),
      ),
    );
  }
}
