import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/categories/view/category_wise_user_page.dart';
import 'package:leiuniverse/categories/widget/category_text_widget.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/pagination/pagination_mixin.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/setup_business/model/category_model.dart';
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class ChildSubCategoriesPage extends StatefulWidget {
  const ChildSubCategoriesPage({super.key, this.category});
  final CategoryModel? category;

  @override
  State<ChildSubCategoriesPage> createState() => _ChildSubCategoriesPageState();
}

class _ChildSubCategoriesPageState extends State<ChildSubCategoriesPage> with PaginationMixin {
  final childSubCategoryList = ValueNotifier<List<CategoryModel>>(<CategoryModel>[]);
  final isLoading = ValueNotifier(false);
  final isLoadingMore = ValueNotifier(false);
  final searchController = TextEditingController();

  int page = 1;

  bool hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    initiatePagination();
    load();
  }

  @override
  void dispose() {
    isLoading.dispose();
    isLoadingMore.dispose();
    childSubCategoryList.dispose();
    disposePagination();
    searchController.dispose();
    super.dispose();
  }

  Future<void> load() async {
    isLoading.value = true;

    final failOrSuccess = await getIt<IBusinessRepository>()
        .categoryList(parentId: widget.category?.id, search: searchController.text.trim());

    failOrSuccess.fold((l) {
      isLoading.value = false;
      Utility.toast(message: l.message);
    }, (r) {
      isLoading.value = false;
      childSubCategoryList.value = [...r.data];
      hasReachedMax = r.data.length < 10;
      page = 2;
    });
  }

  Future<void> loadMore() async {
    isLoadingMore.value = true;
    final failOrSuccess = await getIt<IBusinessRepository>().categoryList(page: page, parentId: widget.category?.id);

    failOrSuccess.fold((l) {
      isLoadingMore.value = false;
    }, (r) {
      isLoadingMore.value = false;
      page += 1;
      hasReachedMax = r.data.length < 10;
      childSubCategoryList.value = [...childSubCategoryList.value, ...r.data];
    });
  }

  void onRefresh() {
    page = 1;
    hasReachedMax = false;
    childSubCategoryList.value = [];
    load();
  }

  @override
  void onReachedLast() {
    if (!isLoading.value && !isLoadingMore.value && !hasReachedMax) {
      loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: widget.category?.name ?? '',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            return Stack(
              children: [
                Column(
                  children: [
                    AppTextFormField(
                      controller: searchController,
                      hintText: 'Search',
                      fillColor: AppColors.white,
                      prefixIcon: const Padding(
                        padding: EdgeInsets.fromLTRB(14, 14, 8, 14),
                        child: AppSvgImage(
                          AppAssets.searchIcon,
                          color: AppColors.subText,
                        ),
                      ),
                      onChanged: (value) {
                        EasyDebounce.debounce(
                          'search_child_sub_category',
                          const Duration(milliseconds: 500),
                          () {
                            onRefresh();
                          },
                        );
                      },
                    ),
                    Expanded(
                      child: ValueListenableBuilder<bool>(
                        valueListenable: isLoadingMore,
                        builder: (context, loadingMore, _) {
                          return ValueListenableBuilder<List<CategoryModel>>(
                            valueListenable: childSubCategoryList,
                            builder: (context, childSubCategories, _) {
                              if (!loading && childSubCategories.isEmpty) {
                                return Utility.noDataWidget(context: context, text: 'No Child Category Found');
                              }
                              return RefreshIndicator(
                                onRefresh: () async {
                                  return onRefresh();
                                },
                                child: ListView.separated(
                                  physics: const AlwaysScrollableScrollPhysics(),
                                  padding: const EdgeInsets.only(top: 20),
                                  controller: scrollPaginationController,
                                  itemCount: childSubCategories.length,
                                  separatorBuilder: (context, index) {
                                    return const Gap(20);
                                  },
                                  itemBuilder: (context, index) {
                                    return Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        subCategoryView(index),
                                        if (!hasReachedMax && loadingMore && index == childSubCategories.length - 1)
                                          const Padding(
                                            padding: EdgeInsets.only(top: 20),
                                            child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                                          ),
                                      ],
                                    );
                                  },
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
                if (loading)
                  const Center(
                    child: CustomProgressIndecator(
                      size: 40,
                      color: AppColors.primary,
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget subCategoryView(int index) {
    final category = childSubCategoryList.value[index];
    return CategoryTextWidget(
      category: category,
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CategoriesWiseUserPage(
              categoryModel: category,
            ),
          ),
        );
      },
    );
  }
}
