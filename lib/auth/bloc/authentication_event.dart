part of 'authentication_bloc.dart';

sealed class AuthenticationEvent extends Equatable {
  const AuthenticationEvent();

  @override
  List<Object?> get props => [];
}

final class CheckAuthentication extends AuthenticationEvent {
  const CheckAuthentication({this.user});

  final UserModel? user;
}

final class UpdateUser extends AuthenticationEvent {
  const UpdateUser({this.user});

  final UserModel? user;
}

final class UpdateUserApi extends AuthenticationEvent {
  const UpdateUserApi({this.canAnyOneSendMessage, this.canAnyOneCall});

  final bool? canAnyOneSendMessage;
  final bool? canAnyOneCall;

  @override
  List<Object?> get props => [canAnyOneSendMessage, canAnyOneCall];
}

final class LogOutAuthetication extends AuthenticationEvent {}

final class DeleteAccount extends AuthenticationEvent {
  const DeleteAccount();
}

final class LikeUser extends AuthenticationEvent {
  const LikeUser({required this.id});
  final int id;
}
