import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/utils/network/http_failure.dart';
import 'package:leiuniverse/login/model/user_model.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/user/repository/i_user_repository.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

@lazySingleton
class AuthenticationBloc extends Bloc<AuthenticationEvent, AuthenticationState> {
  AuthenticationBloc({required this.authRepository, required this.localStorageRepository, required this.userRepository})
      : super(const AuthenticationState.unknown()) {
    on<CheckAuthentication>(_onCheck);
    on<LogOutAuthetication>(_onLogout);
    on<UpdateUser>(_onUpdateUser);
    on<UpdateUserApi>(_onUpdateUserApi);
    on<DeleteAccount>(_onDeleteAccount);
    on<LikeUser>(_onUserLike);
  }

  final IAuthRepository authRepository;
  final IUserRepository userRepository;
  final ILocalStorageRepository localStorageRepository;

  Future<void> _onCheck(
    CheckAuthentication event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (event.user != null) {
      emit(AuthenticationState.autheticated(event.user!));
      return;
    }
    if (localStorageRepository.token == null) {
      emit(const AuthenticationState.unAutheticated(null));
      return;
    }
    emit(const AuthenticationState.loading());

    final result = await authRepository.userDetail();

    emit(
      result.fold(
        AuthenticationState.unAutheticated,
        (r) {
          if (r.data == null) {
            return const AuthenticationState.unAutheticated(null);
          }
          return AuthenticationState.autheticated(r.data!);
        },
      ),
    );
  }

  Future<void> _onLogout(
    LogOutAuthetication event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(isStackLoading: true));

    final result = await authRepository.logout();

    emit(
      result.fold(
        (l) => state.copyWith(isStackLoading: false, failure: l),
        (r) => const AuthenticationState.unAutheticated(null),
      ),
    );
  }

  Future<void> _onUpdateUser(
    UpdateUser event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state.isUnAutheticated) return;
    emit(state.copyWith(user: event.user));
  }

  Future<void> _onUpdateUserApi(
    UpdateUserApi event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state.isUnAutheticated) return;
    emit(state.copyWith(isLoading: true));

    final result = await userRepository.update(
      canAnyOneCall: event.canAnyOneCall,
      canAnyOneSendMessage: event.canAnyOneSendMessage,
    );

    emit(
      result.fold(
        (l) => state.copyWith(isLoading: false, failure: l),
        (r) => state.copyWith(
          isLoading: false,
          user: r.data,
        ),
      ),
    );
  }

  Future<void> _onDeleteAccount(
    DeleteAccount event,
    Emitter<AuthenticationState> emit,
  ) async {
    emit(state.copyWith(isStackLoading: true));

    final result = await authRepository.deleteAccount();

    emit(
      result.fold(
        (l) => state.copyWith(isStackLoading: false, failure: l),
        (r) => const AuthenticationState.unAutheticated(null),
      ),
    );
  }

  void _onUserLike(
    LikeUser event,
    Emitter<AuthenticationState> emit,
  ) async {
    if (state.isUnAutheticated) return;
    final newUser = state.user;

    final updatedUser = newUser.copyWith(
      isLiked: newUser.isLiked == 1 ? 0 : 1,
      likesCount: newUser.isLiked == 1 ? newUser.likesCount! - 1 : newUser.likesCount! + 1,
    );

    emit(state.copyWith(user: updatedUser, likeFailure: null));

    final result = await userRepository.likeUser(id: event.id);

    if (result.isLeft()) {
      result.getLeft().fold(() {}, (l) => emit(state.copyWith(user: newUser, likeFailure: l)));
    }
  }
}
