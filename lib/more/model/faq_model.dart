import 'package:equatable/equatable.dart';

class FaqModel extends Equatable {
  const FaqModel({
    this.id,
    this.question,
    this.answer,
  });

  final int? id;
  final String? question;
  final String? answer;

  FaqModel copyWith({
    int? id,
    String? question,
    String? answer,
  }) {
    return FaqModel(
      id: id ?? this.id,
      question: question ?? this.question,
      answer: answer ?? this.answer,
    );
  }

  factory FaqModel.fromJson(Map<String, dynamic> json) {
    return FaqModel(
      id: json["id"],
      question: json["question"],
      answer: json["answer"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
        "answer": answer,
      };

  @override
  String toString() {
    return "$id, $question, $answer, ";
  }

  @override
  List<Object?> get props => [
        id,
        question,
        answer,
      ];
}
