import 'package:equatable/equatable.dart';

class SettingModel extends Equatable {
  const SettingModel({
    this.key,
    this.value,
  });

  final String? key;
  final String? value;

  SettingModel copyWith({
    String? key,
    String? value,
  }) {
    return SettingModel(
      key: key ?? this.key,
      value: value ?? this.value,
    );
  }

  factory SettingModel.fromJson(Map<String, dynamic> json) {
    return SettingModel(
      key: json["key"],
      value: json["value"],
    );
  }

  Map<String, dynamic> toJson() => {
        "key": key,
        "value": value,
      };

  @override
  String toString() {
    return "$key, $value, ";
  }

  @override
  List<Object?> get props => [
        key,
        value,
      ];
}
