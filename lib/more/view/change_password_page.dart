import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/repository/i_auth_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/bottom_button_widget.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final currentPassObscure = ValueNotifier<bool>(false);
  final newPassObscure = ValueNotifier<bool>(false);
  final confirmPassObscure = ValueNotifier<bool>(false);
  final formKey = GlobalKey<FormState>();

  final isLoading = ValueNotifier<bool>(false);

  @override
  void dispose() {
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    currentPassObscure.dispose();
    newPassObscure.dispose();
    confirmPassObscure.dispose();
    isLoading.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Change password',
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ValueListenableBuilder<bool>(
                  valueListenable: currentPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: currentPasswordController,
                      title: 'Current Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      textInputAction: TextInputAction.next,
                      isRequired: true,
                      suffixIcon: IconButton(
                        onPressed: () {
                          currentPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter current password';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<bool>(
                  valueListenable: newPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: newPasswordController,
                      title: 'New Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      textInputAction: TextInputAction.next,
                      isRequired: true,
                      suffixIcon: IconButton(
                        onPressed: () {
                          newPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter new password';
                        }
                        return null;
                      },
                    );
                  },
                ),
                const Gap(20),
                ValueListenableBuilder<bool>(
                  valueListenable: confirmPassObscure,
                  builder: (context, obscure, _) {
                    return AppTextFormField(
                      controller: confirmPasswordController,
                      title: 'Confirm Password',
                      hintText: 'Password',
                      obscureText: !obscure,
                      isRequired: true,
                      textInputAction: TextInputAction.done,
                      suffixIcon: IconButton(
                        onPressed: () {
                          confirmPassObscure.value = !obscure;
                        },
                        icon: AppSvgImage(
                          !obscure ? AppAssets.eyesCloseIcon : AppAssets.eyesOpenIcon,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter confirm password';
                        }
                        if (value != newPasswordController.text) {
                          return 'Password does not match';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (_, value, __) {
            return BottomButtonWidget(
              isLoading: value,
              onTap: changePassword,
              text: 'Change Password',
            );
          }),
    );
  }

  Future<void> changePassword() async {
    if (!formKey.currentState!.validate()) return;
    isLoading.value = true;
    final response = await getIt<IAuthRepository>().changePassword(
      currentPassword: currentPasswordController.text.trim(),
      newPassword: newPasswordController.text.trim(),
    );
    isLoading.value = false;
    response.fold(
      (l) {
        Utility.toast(message: l.message);
        isLoading.value = false;
      },
      (r) {
        Utility.toast(message: r.message);
        isLoading.value = false;
        Navigator.pop(context);
      },
    );
  }
}
