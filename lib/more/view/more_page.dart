import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/constant/constants.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/more/model/setting_model.dart';
import 'package:leiuniverse/more/repository/i_setting_repository.dart';
import 'package:leiuniverse/more/view/about_page.dart';
import 'package:leiuniverse/more/view/change_password_page.dart';
import 'package:leiuniverse/more/view/contact_us_page.dart';
import 'package:leiuniverse/more/view/faq_page.dart';
import 'package:leiuniverse/more/view/terms_and_privacy_page.dart';
import 'package:leiuniverse/more/widget/delete_profile_view.dart';
import 'package:leiuniverse/more/widget/more_element_view.dart';
import 'package:leiuniverse/more/widget/sign_out_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';

class MorePage extends StatefulWidget {
  const MorePage({super.key});

  @override
  State<MorePage> createState() => _MorePageState();
}

class _MorePageState extends State<MorePage> {
  final settingList = ValueNotifier<List<SettingModel>>([]);
  final isSettingLoading = ValueNotifier<bool>(false);
  String? terms;
  String? privacy;
  String? aboutUs;
  String? contactPhone;
  String? contactEmail;
  String? websiteUrl;
  String? companyAddress;
  String? facebook;
  String? instagram;
  String? twitter;
  String? youtube;

  @override
  void initState() {
    super.initState();
    getSettings();
  }

  Future<void> getSettings() async {
    isSettingLoading.value = true;

    final response = await getIt<ISettingRepository>().settings();

    await response.fold(
      (l) {
        isSettingLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data != null && r.data!.isNotEmpty) {
          settingList.value = [...r.data!];
          terms = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.terms)?.value;
          privacy = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.privacy)?.value;
          aboutUs = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.aboutUs)?.value;
          contactPhone =
              settingList.value.firstWhereOrNull((element) => element.key == AppConstants.contactPhone)?.value;
          contactEmail =
              settingList.value.firstWhereOrNull((element) => element.key == AppConstants.contactEmail)?.value;
          websiteUrl = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.websiteUrl)?.value;
          companyAddress =
              settingList.value.firstWhereOrNull((element) => element.key == AppConstants.companyAddress)?.value;
          facebook = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.facebook)?.value;
          instagram = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.instagram)?.value;
          twitter = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.twitter)?.value;
          youtube = settingList.value.firstWhereOrNull((element) => element.key == AppConstants.youtube)?.value;
        }
        isSettingLoading.value = false;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        title: 'More',
        onBackTap: () {
          Navigator.pop(context);
        },
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: AppColors.white,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(6),
                        BlocSelector<AuthenticationBloc, AuthenticationState, ({bool isLoading, bool canSendMessage})>(
                          selector: (state) =>
                              (isLoading: state.isLoading, canSendMessage: state.user.canSendMessage == 1),
                          builder: (context, state) {
                            return IgnorePointer(
                              ignoring: state.isLoading,
                              child: MoreElementView(
                                icon: AppAssets.moreSendMessageIcon,
                                text: 'Any one can send message',
                                isSwitch: state.canSendMessage,
                                onSwitch: (value) {
                                  context.read<AuthenticationBloc>().add(UpdateUserApi(canAnyOneSendMessage: value));
                                },
                              ),
                            );
                          },
                        ),
                        BlocSelector<AuthenticationBloc, AuthenticationState, ({bool isLoading, bool canCall})>(
                          selector: (state) => (isLoading: state.isLoading, canCall: state.user.canCall == 1),
                          builder: (context, state) {
                            return IgnorePointer(
                              ignoring: state.isLoading,
                              child: MoreElementView(
                                icon: AppAssets.moreCallIcon,
                                text: 'Any one can call',
                                isSwitch: state.canCall,
                                onSwitch: (value) {
                                  context.read<AuthenticationBloc>().add(UpdateUserApi(canAnyOneCall: value));
                                },
                              ),
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.changePasswordIcon,
                          text: 'Change password',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const ChangePasswordPage(),
                              ),
                            );
                          },
                        ),
                        const Gap(6),
                      ],
                    ),
                  ),
                  const Gap(20),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: AppColors.white,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Gap(6),
                        MoreElementView(
                          icon: AppAssets.termsPrivacyIcon,
                          text: 'Terms & Privacy Policy',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => TermsAndPrivacyPage(
                                  terms: terms ?? '',
                                  privacy: privacy ?? '',
                                ),
                              ),
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.contactUsIcon,
                          text: 'Contact us',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => ContactUsPage(
                                  phone: contactPhone,
                                  email: contactEmail,
                                  website: websiteUrl,
                                  address: companyAddress,
                                  facebook: facebook,
                                  instagram: instagram,
                                  twitter: twitter,
                                  youtube: youtube,
                                ),
                              ),
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.shareIcon,
                          text: 'Share app',
                          onTap: () {
                            // Navigator.push(
                            //   context,
                            //   MaterialPageRoute(
                            //     builder: (context) => const ShareAppPage(),
                            //   ),
                            // );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.faqIcon,
                          text: 'FAQs',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FaqPage(),
                              ),
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.aboutUsIcon,
                          text: 'About us',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AboutPage(
                                  about: aboutUs,
                                ),
                              ),
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.moreDeleteIcon,
                          text: 'Delete profile',
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: AppColors.white,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(24),
                                  topRight: Radius.circular(24),
                                ),
                              ),
                              builder: (context) {
                                return const DeleteProfileView();
                              },
                            );
                          },
                        ),
                        MoreElementView(
                          icon: AppAssets.signOutIcon,
                          text: 'Sign out',
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              backgroundColor: AppColors.white,
                              shape: const RoundedRectangleBorder(
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(24),
                                  topRight: Radius.circular(24),
                                ),
                              ),
                              builder: (context) {
                                return const SignOutView();
                              },
                            );
                          },
                        ),
                        const Gap(6),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
            selector: (state) => state.isLoading,
            builder: (context, isLoading) {
              return ValueListenableBuilder<bool>(
                valueListenable: isSettingLoading,
                builder: (context, loading, _) {
                  return isLoading || loading
                      ? const Center(
                          child: CustomProgressIndecator(
                            color: AppColors.primary,
                            size: 40,
                          ),
                        )
                      : const SizedBox.shrink();
                },
              );
            },
          )
        ],
      ),
    );
  }
}
