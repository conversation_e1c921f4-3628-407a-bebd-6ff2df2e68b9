import 'package:flutter/material.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/more/model/faq_model.dart';
import 'package:leiuniverse/more/repository/i_setting_repository.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/custom_progress_indecator.dart';
import 'package:visibility_detector/visibility_detector.dart';

class FaqPage extends StatefulWidget {
  const FaqPage({super.key});

  @override
  State<FaqPage> createState() => _FaqPageState();
}

class _FaqPageState extends State<FaqPage> {
  final faqList = ValueNotifier<List<FaqModel>>([]);
  int page = 0;

  bool stop = false;

  final isLoading = ValueNotifier<bool>(false);
  final isPageLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    getFaqList(isFirst: true);
  }

  Future<void> getFaqList({bool isFirst = false}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    page += 1;
    final response = await getIt<ISettingRepository>().faqs(
      page: page,
    );

    await response.fold(
      (l) {
        isLoading.value = false;
        isPageLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        if (r.data != null && r.data!.isNotEmpty) {
          if (r.data!.length < 10) {
            stop = true;
          }
          faqList.value = [...faqList.value, ...r.data!];
        }
        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'FAQs',
      ),
      body: ValueListenableBuilder<bool>(
          valueListenable: isLoading,
          builder: (context, loading, _) {
            return loading
                ? const Center(
                    child: CustomProgressIndecator(color: AppColors.primary, size: 40),
                  )
                : ValueListenableBuilder<List<FaqModel>>(
                    valueListenable: faqList,
                    builder: (context, faqs, _) {
                      return !loading && faqs.isEmpty
                          ? Utility.noDataWidget(
                              context: context,
                              text: 'No FAQs found',
                            )
                          : RefreshIndicator(
                              onRefresh: () async {
                                page = 0;
                                stop = false;
                                faqList.value = [];
                                getFaqList(isFirst: true);
                              },
                              child: ListView.builder(
                                itemCount: faqs.length,
                                padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                                physics: const AlwaysScrollableScrollPhysics(),
                                itemBuilder: (context, index) {
                                  return (faqs.length) - 1 == index
                                      ? VisibilityDetector(
                                          key: Key(index.toString()),
                                          onVisibilityChanged: (VisibilityInfo info) {
                                            if (!stop &&
                                                index == (faqs.length) - 1 &&
                                                !isLoading.value &&
                                                !isPageLoading.value) {
                                              getFaqList(isFirst: false);
                                            }
                                          },
                                          child: Column(
                                            children: [
                                              faqView(index),
                                              ValueListenableBuilder<bool>(
                                                valueListenable: isPageLoading,
                                                builder: (context, value, _) {
                                                  if (value) {
                                                    return const CustomProgressIndecator(
                                                        color: AppColors.subText, size: 40);
                                                  }
                                                  return const SizedBox.shrink();
                                                },
                                              )
                                            ],
                                          ),
                                        )
                                      : faqView(index);
                                },
                              ),
                            );
                    });
          }),
    );
  }

  Widget faqView(int index) {
    final faq = faqList.value[index];
    return Theme(
      data: Theme.of(context).copyWith(
        unselectedWidgetColor: AppColors.subText,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ExpansionTile(
            tilePadding: EdgeInsets.zero,
            childrenPadding: const EdgeInsets.symmetric(vertical: 8),
            dense: true,
            enabled: true,
            iconColor: AppColors.subText,
            title: Text(
              faq.question ?? '',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      faq.answer ?? '',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                  ),
                ],
              )
            ],
          ),
          const Padding(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: Divider(
              height: 1,
              color: AppColors.border,
            ),
          )
        ],
      ),
    );
  }
}
