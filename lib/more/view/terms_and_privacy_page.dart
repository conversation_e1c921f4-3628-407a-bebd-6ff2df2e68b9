import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/extentions/colors_extnetions.dart';
import 'package:leiuniverse/more/widget/terms_privacy_tab_view.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class TermsAndPrivacyPage extends StatelessWidget {
  TermsAndPrivacyPage({super.key, required this.terms, required this.privacy});
  final String terms;
  final String privacy;

  final isTermsSelected = ValueNotifier<bool>(true);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Terms & Privacy Policy',
      ),
      body: Column(
        children: [
          ValueListenableBuilder<bool>(
            valueListenable: isTermsSelected,
            builder: (context, value, _) {
              return Padding(
                padding: const EdgeInsets.all(20.0),
                child: TermsPrivacyTabView(
                  isPrivacySelected: !value,
                  isTermsSelected: value,
                  onTapPrivacy: () {
                    isTermsSelected.value = false;
                  },
                  onTapTerms: () {
                    isTermsSelected.value = true;
                  },
                ),
              );
            },
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(14),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 5),
                  blurRadius: 10,
                  spreadRadius: 0,
                  color: AppColors.darkCerulean.withOpacity2(0.04),
                ),
              ],
            ),
            child: ValueListenableBuilder<bool>(
                valueListenable: isTermsSelected,
                builder: (context, value, _) {
                  return Html(
                    data: value ? terms : privacy,
                    style: {
                      "body": Style(
                        margin: Margins.zero,
                        padding: HtmlPaddings.zero,
                      ),
                      "p": Style(
                        margin: Margins.zero,
                        padding: HtmlPaddings.zero,
                      ),
                    },
                  );
                }),
          ),
        ],
      ),
    );
  }
}
