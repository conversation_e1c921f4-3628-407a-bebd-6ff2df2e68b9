import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';

class ShareAppPage extends StatefulWidget {
  const ShareAppPage({super.key});

  @override
  State<ShareAppPage> createState() => _ShareAppPageState();
}

class _ShareAppPageState extends State<ShareAppPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Share app',
      ),
      body: Column(
        children: [
          Image.asset(AppAssets.shareAppImage),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Text(
                  'Invite your Friends',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(height: 1.4),
                ),
                const Gap(16),
                Text(
                  'Share the fun and help your friends connect!\nSend an invite to your friends and let them join\nyou on Spotlink to explore, meet new people, and\ncreate meaningful connections.',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(color: AppColors.subText, height: 1.4),
                ),
                const Gap(32),
                AppTextFormField(
                  title: 'Invite link',
                  hintText: 'sdsda',
                  readOnly: true,
                  suffixIcon: InkWell(
                    onTap: () {},
                    overlayColor: WidgetStateProperty.all(AppColors.transparent),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          height: 36,
                          width: 1,
                          color: AppColors.border,
                        ),
                        const Gap(12),
                        const AppSvgImage(AppAssets.copyIcon),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
