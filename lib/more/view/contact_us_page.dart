import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/core/theme/app_assets.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/url_manager.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/more/repository/i_setting_repository.dart';
import 'package:leiuniverse/widget/app_svg_image.dart';
import 'package:leiuniverse/widget/app_text_form_field.dart';
import 'package:leiuniverse/widget/common_app_bar.dart';
import 'package:leiuniverse/widget/common_button.dart';

class ContactUsPage extends StatefulWidget {
  const ContactUsPage(
      {super.key,
      this.phone,
      this.email,
      this.website,
      this.address,
      this.facebook,
      this.twitter,
      this.instagram,
      this.youtube});
  final String? phone;
  final String? email;
  final String? website;
  final String? address;
  final String? facebook;
  final String? twitter;
  final String? instagram;
  final String? youtube;

  @override
  State<ContactUsPage> createState() => _ContactUsPageState();
}

class _ContactUsPageState extends State<ContactUsPage> {
  final subjectController = TextEditingController();
  final messageController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final isButtonLoading = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CommonAppBar(
        onBackTap: () {
          Navigator.pop(context);
        },
        title: 'Contact us',
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: double.infinity,
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: AppColors.primary,
              ),
              child: Column(
                children: [
                  Text(
                    'Get in Touch',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(color: AppColors.white),
                  ),
                  Text(
                    'Say something to start',
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(color: AppColors.white),
                  ),
                  const Gap(12),
                  if (widget.phone != null)
                    contactView(
                      icon: AppAssets.contactUsCallIcon,
                      title: widget.phone!,
                      onTap: () {
                        UrlManager.openDialpad(widget.phone);
                      },
                    ),
                  if (widget.email != null)
                    contactView(
                      icon: AppAssets.contactEmailIcon,
                      title: widget.email!,
                      onTap: () {
                        UrlManager.openEmail(widget.email);
                      },
                    ),
                  if (widget.website != null)
                    contactView(
                      icon: AppAssets.mapLocationIcon,
                      title: widget.website!,
                      onTap: () {
                        UrlManager.openUrl(widget.website);
                      },
                    ),
                  if (widget.address != null)
                    contactView(
                      icon: AppAssets.contactUsLocationIcon,
                      title: widget.address!,
                      onTap: () {},
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (widget.facebook != null)
                        InkWell(
                          onTap: () {
                            UrlManager.openUrl(widget.facebook);
                          },
                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          child: const Padding(
                            padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                            child: AppSvgImage(AppAssets.contactUsFacebookIcon),
                          ),
                        ),
                      if (widget.twitter != null)
                        InkWell(
                          onTap: () {
                            UrlManager.openUrl(widget.twitter);
                          },
                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          child: const Padding(
                            padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                            child: AppSvgImage(AppAssets.contactUsTwiterIcon),
                          ),
                        ),
                      if (widget.instagram != null)
                        InkWell(
                          onTap: () {
                            UrlManager.openUrl(widget.instagram);
                          },
                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          child: const Padding(
                            padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                            child: AppSvgImage(AppAssets.contactUsInstagramIcon),
                          ),
                        ),
                      if (widget.youtube != null)
                        InkWell(
                          onTap: () {
                            UrlManager.openUrl(widget.youtube);
                          },
                          overlayColor: WidgetStateProperty.all(AppColors.transparent),
                          child: const Padding(
                            padding: EdgeInsets.fromLTRB(8, 8, 8, 0),
                            child: AppSvgImage(AppAssets.contactUsYoutubeIcon),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              color: AppColors.white,
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Drop a Message', style: Theme.of(context).textTheme.headlineMedium),
                    const Gap(20),
                    AppTextFormField(
                      controller: subjectController,
                      title: 'Subject',
                      isRequired: true,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter subject';
                        }
                        return null;
                      },
                    ),
                    const Gap(20),
                    AppTextFormField(
                      controller: messageController,
                      maxLines: 3,
                      title: 'Mesasage',
                      isRequired: true,
                      hintText: 'Enter details here',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter message';
                        }
                        return null;
                      },
                    ),
                    const Gap(30),
                    ValueListenableBuilder<bool>(
                        valueListenable: isButtonLoading,
                        builder: (context, loading, _) {
                          return CommonButton(
                            isLoading: loading,
                            onTap: () {
                              if (formKey.currentState!.validate()) {
                                supportTicket();
                              }
                            },
                            text: 'Send Message',
                          );
                        }),
                    const Gap(10),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget contactView({required String icon, required String title, void Function()? onTap}) {
    return InkWell(
      onTap: onTap,
      overlayColor: WidgetStateProperty.all(AppColors.transparent),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 50),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppSvgImage(icon),
            const Gap(7),
            Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: AppColors.white),
            )
          ],
        ),
      ),
    );
  }

  Future<void> supportTicket() async {
    isButtonLoading.value = true;
    final response = await getIt<ISettingRepository>().supportTicket(
      subject: subjectController.text,
      message: messageController.text,
    );

    await response.fold(
      (l) {
        isButtonLoading.value = false;
        Utility.toast(message: l.message);
      },
      (r) async {
        isButtonLoading.value = false;
        if (r.status == 1) {
          Navigator.pop(context);
        }
        Utility.toast(message: r.message);
      },
    );
  }
}
