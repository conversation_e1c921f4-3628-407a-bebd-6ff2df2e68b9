part of 'i_setting_repository.dart';

@Injectable(as: ISettingRepository)
class SettingRepository extends ISettingRepository {
  SettingRepository(super.client);

  @override
  ApiResult<CountryListResponse> countryList({int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.countryList,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(CountryListResponse.fromJson);
  }

  @override
  ApiResult<CountryListResponse> stateList({int? countryId, int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.stateList,
      params: {
        if (countryId != null) 'country_id': countryId.toString(),
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(CountryListResponse.fromJson);
  }

  @override
  ApiResult<CountryListResponse> cityList({int? stateId, int page = 1, int perPage = 10, String? search}) async {
    final response = await client.get(
      url: AppStrings.cityList,
      params: {
        if (stateId != null) 'state_id': stateId.toString(),
        'page': page.toString(),
        'per_page': perPage.toString(),
        if (search != null && search.trim().isNotEmpty) 'search': search,
      },
    );
    return response.parseResponse(CountryListResponse.fromJson);
  }

  @override
  ApiResult<FaqListResponse> faqs({int page = 1, int perPage = 10}) async {
    final response = await client.get(
      url: AppStrings.faq,
      params: {
        'page': page.toString(),
        'per_page': perPage.toString(),
      },
    );

    return response.parseResponse(FaqListResponse.fromJson);
  }

  @override
  ApiResult<SettingResponse> settings() async {
    final response = await client.get(
      url: AppStrings.settings,
    );

    return response.parseResponse(SettingResponse.fromJson);
  }

  @override
  ApiResult<CommonResponse> supportTicket({required String subject, required String message}) async {
    final response = await client.post(
      url: AppStrings.supportTicket,
      requests: {
        'subject': subject.trim(),
        'message': message.trim(),
      },
    );

    return response.parseResponse(CommonResponse.fromJson);
  }
}
