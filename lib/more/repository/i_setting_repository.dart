import 'package:injectable/injectable.dart';
import 'package:leiuniverse/core/constant/app_string.dart';
import 'package:leiuniverse/core/utils/extentions/fpdart_extentions.dart';
import 'package:leiuniverse/core/utils/network/client.dart';
import 'package:leiuniverse/login/response/common_response.dart';
import 'package:leiuniverse/more/response/faq_list_response.dart';
import 'package:leiuniverse/more/response/setting_response.dart';
import 'package:leiuniverse/signup/response/country_list_response.dart';

part 'setting_repository.dart';

abstract class ISettingRepository {
  ISettingRepository(this.client);
  final Client client;

  ApiResult<CountryListResponse> countryList({
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<CountryListResponse> stateList({
    int? countryId,
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<CountryListResponse> cityList({
    int? stateId,
    int page = 1,
    int perPage = 10,
    String? search,
  });

  ApiResult<FaqListResponse> faqs({
    int page = 1,
    int perPage = 10,
  });

  ApiResult<SettingResponse> settings();

  ApiResult<CommonResponse> supportTicket({
    required String subject,
    required String message,
  });
}
