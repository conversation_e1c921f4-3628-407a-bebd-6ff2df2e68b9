import 'package:equatable/equatable.dart';
import 'package:leiuniverse/more/model/faq_model.dart';

class FaqListResponse extends Equatable {
  const FaqListResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final List<FaqModel>? data;

  FaqListResponse copyWith({
    int? status,
    String? message,
    List<FaqModel>? data,
  }) {
    return FaqListResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory FaqListResponse.fromJson(Map<String, dynamic> json) {
    return FaqListResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<FaqModel>.from(json["data"]!.map((x) => FaqModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null ? [] : data?.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
