import 'package:equatable/equatable.dart';
import 'package:leiuniverse/more/model/setting_model.dart';

class SettingResponse extends Equatable {
  const SettingResponse({
    this.status,
    this.message,
    this.data,
  });

  final int? status;
  final String? message;
  final List<SettingModel>? data;

  SettingResponse copyWith({
    int? status,
    String? message,
    List<SettingModel>? data,
  }) {
    return SettingResponse(
      status: status ?? this.status,
      message: message ?? this.message,
      data: data ?? this.data,
    );
  }

  factory SettingResponse.fromJson(Map<String, dynamic> json) {
    return SettingResponse(
      status: json["status"],
      message: json["message"],
      data: json["data"] == null ? [] : List<SettingModel>.from(json["data"]!.map((x) => SettingModel.fromJson(x))),
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.map((x) => x.toJson()).toList(),
      };

  @override
  String toString() {
    return "$status, $message, $data, ";
  }

  @override
  List<Object?> get props => [
        status,
        message,
        data,
      ];
}
