import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart';
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart';
import 'package:leiuniverse/core/theme/app_color.dart';
import 'package:leiuniverse/core/utils/utility.dart';
import 'package:leiuniverse/injector/injector.dart';
import 'package:leiuniverse/login/view/login_page.dart';
import 'package:leiuniverse/widget/common_button.dart';

class SignOutView extends StatelessWidget {
  const SignOutView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status == AuthenticationStatus.unAutheticated) {
          Utility.toast(message: 'Signed out successfully');
          getIt<ILocalStorageRepository>().clearAuth();
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => const LoginPage(),
            ),
            (route) => false,
          );
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sign Out',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const Gap(4),
            Text(
              'Are you sure you want to sign out?',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const Gap(30),
            Row(
              children: [
                Expanded(
                  child: CommonButton(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    text: 'No',
                  ),
                ),
                const Gap(15),
                Expanded(
                  child: BlocSelector<AuthenticationBloc, AuthenticationState, bool>(
                    selector: (state) => state.isStackLoading,
                    builder: (context, isLoading) {
                      return CommonButton(
                        onTap: () {
                          context.read<AuthenticationBloc>().add(LogOutAuthetication());
                        },
                        text: 'Sign out',
                        isLoading: isLoading,
                        removeShadow: true,
                        backgroundColor: AppColors.background,
                        textColor: AppColors.ebonyClay,
                        borderColor: AppColors.background,
                      );
                    },
                  ),
                ),
              ],
            ),
            const Gap(20),
          ],
        ),
      ),
    );
  }
}
