// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:cloud_firestore/cloud_firestore.dart' as _i974;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:leiuniverse/auth/bloc/authentication_bloc.dart' as _i947;
import 'package:leiuniverse/chat/bloc/chat_bloc.dart' as _i819;
import 'package:leiuniverse/chat/bloc/chat_detail_bloc.dart' as _i931;
import 'package:leiuniverse/chat/repository/i_chat_repository.dart' as _i464;
import 'package:leiuniverse/core/local_storage/i_local_storage_repository.dart'
    as _i1048;
import 'package:leiuniverse/core/utils/network/client.dart' as _i149;
import 'package:leiuniverse/core/utils/network/http_client.dart' as _i688;
import 'package:leiuniverse/event_promotion/bloc/comment_bloc.dart' as _i542;
import 'package:leiuniverse/event_promotion/bloc/event_bloc.dart' as _i786;
import 'package:leiuniverse/event_promotion/repository/i_event_repository.dart'
    as _i22;
import 'package:leiuniverse/injector/firebase_injectable_module.dart' as _i974;
import 'package:leiuniverse/injector/shared_preference_injectable_module.dart'
    as _i988;
import 'package:leiuniverse/inquiry/repository/i_inquiry_repository.dart'
    as _i851;
import 'package:leiuniverse/login/repository/i_auth_repository.dart' as _i501;
import 'package:leiuniverse/market_place/bloc/marketplace_bloc.dart' as _i627;
import 'package:leiuniverse/market_place/market_detail_bloc/marketplace_detail_bloc.dart'
    as _i578;
import 'package:leiuniverse/market_place/my_market/my_market_bloc.dart'
    as _i619;
import 'package:leiuniverse/market_place/repository/i_marketplace_repository.dart'
    as _i423;
import 'package:leiuniverse/more/repository/i_setting_repository.dart' as _i401;
import 'package:leiuniverse/setup_business/bloc/business_bloc.dart' as _i977;
import 'package:leiuniverse/setup_business/repository/i_business_repository.dart'
    as _i755;
import 'package:leiuniverse/user/repository/i_user_repository.dart' as _i719;
import 'package:shared_preferences/shared_preferences.dart' as _i460;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final injectableModule = _$InjectableModule();
    final firebaseInjectableModule = _$FirebaseInjectableModule();
    await gh.factoryAsync<_i460.SharedPreferences>(
      () => injectableModule.prefs,
      preResolve: true,
    );
    gh.lazySingleton<_i974.FirebaseFirestore>(
        () => firebaseInjectableModule.firestore);
    gh.factory<_i1048.ILocalStorageRepository>(
        () => _i1048.LocalStorageRepository(gh<_i460.SharedPreferences>()));
    gh.factory<_i149.Client>(
        () => _i688.HttpClient(gh<_i1048.ILocalStorageRepository>()));
    gh.factory<_i719.IUserRepository>(
        () => _i719.UserRepository(gh<_i149.Client>()));
    gh.factory<_i401.ISettingRepository>(
        () => _i401.SettingRepository(gh<_i149.Client>()));
    gh.factory<_i851.IInquiryRepository>(
        () => _i851.InquiryRepository(gh<_i149.Client>()));
    gh.factory<_i22.IEventRepository>(
        () => _i22.EventRepository(gh<_i149.Client>()));
    gh.factory<_i423.IMarketplaceRepository>(
        () => _i423.MarketplaceRepository(gh<_i149.Client>()));
    gh.factory<_i542.CommentBloc>(
        () => _i542.CommentBloc(gh<_i22.IEventRepository>()));
    gh.factory<_i786.EventBloc>(
        () => _i786.EventBloc(gh<_i22.IEventRepository>()));
    gh.factory<_i464.IChatRepository>(() => _i464.ChatRepository(
          gh<_i149.Client>(),
          gh<_i974.FirebaseFirestore>(),
        ));
    gh.factory<_i501.IAuthRepository>(() => _i501.AuthRepository(
          gh<_i149.Client>(),
          gh<_i1048.ILocalStorageRepository>(),
        ));
    gh.factoryParam<_i578.MarketplaceDetailBloc, int, dynamic>((
      advertisementId,
      _,
    ) =>
        _i578.MarketplaceDetailBloc(
          repository: gh<_i423.IMarketplaceRepository>(),
          userRepository: gh<_i719.IUserRepository>(),
          advertisementId: advertisementId,
        ));
    gh.lazySingleton<_i947.AuthenticationBloc>(() => _i947.AuthenticationBloc(
          authRepository: gh<_i501.IAuthRepository>(),
          localStorageRepository: gh<_i1048.ILocalStorageRepository>(),
          userRepository: gh<_i719.IUserRepository>(),
        ));
    gh.factoryParam<_i931.ChatDetailBloc, Map<String, dynamic>,
        Map<String, dynamic>?>((
      userData,
      extraData,
    ) =>
        _i931.ChatDetailBloc(
          chatRepository: gh<_i464.IChatRepository>(),
          authRepository: gh<_i501.IAuthRepository>(),
          userData: userData,
          extraData: extraData,
        ));
    gh.factory<_i755.IBusinessRepository>(
        () => _i755.BusinessRepository(gh<_i149.Client>()));
    gh.factory<_i627.MarketplaceBloc>(() =>
        _i627.MarketplaceBloc(repository: gh<_i423.IMarketplaceRepository>()));
    gh.factory<_i619.MyMarketBloc>(() =>
        _i619.MyMarketBloc(repository: gh<_i423.IMarketplaceRepository>()));
    gh.factoryParam<_i819.ChatBloc, int?, dynamic>((
      advertisementIdFromParams,
      _,
    ) =>
        _i819.ChatBloc(
          chatRepository: gh<_i464.IChatRepository>(),
          advertisementIdFromParams: advertisementIdFromParams,
        ));
    gh.factory<_i977.BusinessBloc>(
        () => _i977.BusinessBloc(gh<_i755.IBusinessRepository>()));
    return this;
  }
}

class _$InjectableModule extends _i988.InjectableModule {}

class _$FirebaseInjectableModule extends _i974.FirebaseInjectableModule {}
