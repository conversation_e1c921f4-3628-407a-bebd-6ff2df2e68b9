name: leiuniverse
description: ""

publish_to: 'none' 


version: 1.0.0+6

environment:
  sdk: '>=3.3.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter


  cupertino_icons: ^1.0.6
  injectable: ^2.5.0
  get_it: ^8.0.3
  flutter_svg: ^2.2.0
  gap: ^3.0.1
  fluttertoast: ^8.2.12
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  pin_code_fields: ^8.0.1
  flutter_html: ^3.0.0
  http: ^1.4.0
  equatable: ^2.0.7
  fpdart: ^1.1.1
  shared_preferences: ^2.5.3
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1
  collection: ^1.19.1
  bloc_concurrency: ^0.3.0
  talker_logger: ^4.9.2
  flutter_image_compress: ^2.4.0
  path: ^1.9.1
  path_provider: ^2.1.5
  easy_debounce: ^2.0.3
  visibility_detector: ^0.4.0+2
  url_launcher: ^6.3.1
  intl: ^0.20.2
  rxdart: ^0.28.0
  firebase_core: ^3.15.1
  firebase_messaging: ^15.2.9
  flutter_local_notifications: ^19.3.1
  another_flutter_splash_screen: ^1.2.1
  cloud_firestore: ^5.6.12
  gif_view: ^1.0.3
  firebase_ui_firestore: ^1.7.3
  grouped_list: ^6.0.0
  smooth_page_indicator: ^1.2.1
  photo_view: ^0.15.0
  purchases_flutter: ^9.2.1
  percent_indicator: ^4.2.5
  flutter_rating_bar: ^4.0.1
  lottie: ^3.3.1

dev_dependencies:
  injectable_generator: ^2.7.0
  build_runner: ^2.5.0
  flutter_test:
    sdk: flutter


  flutter_lints: ^3.0.0


flutter:

  
  uses-material-design: true
  generate: true
  assets:
    - assets/images/
    - assets/svg/

  fonts:
    - family: SFProDisplay
      fonts:
        - asset: fonts/SFProDisplay-Regular.ttf
          weight: 400
        - asset: fonts/SFProDisplay-Medium.ttf
          weight: 500
        - asset: fonts/SFProDisplay-Semibold.ttf
          weight: 600
        - asset: fonts/SFProDisplay-Bold.ttf
          weight: 700
        - asset: fonts/SFProDisplay-Heavy.ttf
          weight: 900

